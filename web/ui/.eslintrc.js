module.exports = {
  extends: ['@tencent/eslint-config-tencent', 'plugin:vue/recommended'],
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: 'babel-eslint',
    ecmaVersion: 2017,
    sourceType: 'module',
  },

  globals: {
    TCIC: null,
    wx: null,
  },

  rules: {
    'no-empty': 'off',
    'vue/multi-word-component-names': 'off',
    'no-unused-vars': 'off',
    'no-alert': 2,
    'no-console': 'off',  // 允许在代码中保留 console 命令
    'linebreak-style': 'off',
    'no-param-reassign': 'off',  // 允许修改函数参数
    'prefer-destructuring': 'off',
    'object-curly-spacing': ['error', 'always'],
    'max-len': ['warn', { code: 180 }],
    'import/first': 'off',
    'template-curly-spacing': 'off',
    indent: 'off',
  },
};
