import {
  TCICCustomUI,
  THookType,
  invokeMsgBoxHooks,
  invokeSyncHooks,
  invokeSyncHooksAsCallback,
} from './customUI';

import { ComponentFactory } from '@/pages/class/component/component_factory';
import Constant from '../../util/Constant';
import CustomMsg from '../../util/CustomMsg';
import { LayoutFactory } from '@/pages/class/layout/layout_factory';
import Lodash from 'lodash';
// import CDPDebugger from '../../util/CDPDebugger'; // 后端服务下线不能使用了
import Version from './version';
import Vue from 'vue';
import i18next from '../../util/i18nextByKey';
import i18nextOri from 'i18next';
import { install } from 'resize-observer';
/* eslint-disable no-nested-ternary */
import moment from 'moment';

if (process.env.NODE_ENV === 'development') {
  Vue.config.devtools = true;
}

// 安装polyfill
try {
  if (!window.ResizeObserver) {
    console.warn('window.ResizeObserver is not support, install polyfill');
    install();
  }
} catch (e) {
  console.error(e);
}

// 默认主题
if (TCIC.SDK.instance.isCollegeClass()) {
  if (TCIC.SDK.instance.isElectron()) {
    document.body.classList.add('college-electron');
  } else {
    document.body.classList.add('college-web');
  }
} else {
  document.body.classList.add('dark');
  if (TCIC.SDK.instance.isRecordMode()) {
    document.body.classList.add('record-mode');
  }
}


function pingHost(host) {
  const favicon = 'favicon.ico';
  const img = new Image();
  const start = performance.now();
  const time = 5 * 1000;
  return new Promise((resolve) => {
    img.src = `${host}/${favicon}?${+new Date()}`;
    img.onload = function () {
      resolve({
        url: host,
        time: performance.now() - start,
        status: 'success',
      });
    };
    img.onerror = function () {
      resolve({
        url: host,
        time: performance.now() - start,
        status: 'fail',
      });
    };
    setTimeout(() => {
      resolve({
        url: host,
        time: performance.now() - start,
        status: 'timeout',
      });
    }, time);
  });
}

class ClassMainEntry {
  loadingIdObj = {};
  loadingDom = null;
  isShowFreeEditionClassEnd = false;
  constructor() {
    this.sdk = TCIC.SDK.getInstance();

    // 信息ready之后会更新
    const { roomInfo, roleInfo } = this.sdk.getNameConfig();
    this.roomInfo = roomInfo;
    this.roleInfo = roleInfo;
    this.sdk.on(TCIC.TMainEvent.Name_Config_Update, (nameConfig) => {
      // 更新词条配置
      const { roomInfo, roleInfo } = nameConfig;
      this.roomInfo = roomInfo;
      this.roleInfo = roleInfo;
    });

    // 注册状态给外部使用
    this.sdk.registerState(Constant.TStateRecordMode, '录制模式', false);
    this.sdk.registerState(Constant.TStateShareClassroomInfo, `${roomInfo.room}信息分享内容`, null);
    this.sdk.registerState(Constant.TStateDeviceDetect, '设备检测中', true);
    this.sdk.registerState(Constant.TStateDisableChat, '禁止聊天', false);
    this.sdk.registerState(Constant.TStateDisableChatTips, '禁止聊天提示', '禁止聊天');
    this.sdk.registerState(Constant.TStateShowLiveCommenting, '显示弹幕消息', true);
    this.sdk.registerState(Constant.TStateShowQuickIM, '显示弹幕聊天窗', true);
    this.sdk.registerState(Constant.TStateChatBoxTips, '聊天盒子提示', '');
    this.sdk.registerState(Constant.TStateLocalAVBeforeClassBegin, `${roomInfo.startRoom}前打开本地音视频`, true);
    this.sdk.registerState(Constant.TStateAudioMode, '纯音视频上课', false);
    this.sdk.registerState(Constant.TStateIsGettingSystemInfo, '是否正在进行系统信息收集', false);
    this.sdk.registerState(Constant.TStateScreenPlayerVisible, '显示屏幕分享播放器', false);
    this.sdk.registerState(Constant.TStateVodPlayerVisible, '显示点播播放器', false);
    this.sdk.registerState(Constant.TStateLiveIntroduce, '直播简介', null);
    this.sdk.registerState(Constant.TStateCloseKeyboard, '移动端关闭键盘', false);
    this.sdk.registerState(Constant.TStateComponentLoaded, '组件加载完成', false);
    this.sdk.registerState(Constant.TStateImgSendStatus, '图片发送状态', null);
    this.sdk.registerState(Constant.TStateFileSendStatus, '文件发送状态', null);
    this.sdk.registerState(Constant.TStateCarousel, '循环上台', false);
    this.sdk.registerState(Constant.TStateCarouselInterval, '循环上台间隔', 30);
    this.sdk.registerState(Constant.TStateChatTipsEnable, '聊天弹幕', true);
    this.sdk.registerState(Constant.TStateQucikIMEdit, '编辑状态不隐藏底部栏', false);
    this.sdk.registerState(Constant.TStateStartSubCamera, '是否正在分享辅路摄像头', false);
    this.sdk.registerState(Constant.TStateFullScreen, '白板是否全屏', false);
    this.sdk.registerState(Constant.TStateShowChatBox, '消息盒子显示状态', false);
    this.sdk.registerState(Constant.TStateVideoTipsComponentDown, '抛出消息让气泡下移', false);
    this.sdk.registerState(Constant.TStateImmerseMode, '进入沉浸模式', false);
    this.sdk.registerState(Constant.TStateNetWorkTipsAudio, '网络提示界面开启音频上课', false);
    this.sdk.registerState(Constant.TStateTeacherComponentLoaded, `${roleInfo.teacher}视频是否加载`, false);
    this.sdk.registerState(Constant.TStateShowTeacherVideoInWhiteBoardArea, `在白板区域最大化显示${roleInfo.teacher}视频`, false);
    this.sdk.registerState(Constant.TStateShowStudentsVideoWrap, `${roleInfo.student}视频列表是否可见`, true);
    this.sdk.registerState(Constant.TStateShowRightColumn, '右侧边栏是否可见', true);
    this.sdk.registerState(Constant.TStatePassThroughStatus, '是否可透传点击', false);
    this.sdk.registerState(Constant.TStateBigVideoMode, '公开课视频全屏展示', false);
    this.sdk.registerState(Constant.TStateVideoWallMode, '视频墙模式', false);
    this.sdk.registerState(Constant.TEventShutDownScreenShare, '关闭屏幕共享', false);
    this.sdk.registerState(Constant.TStateCollegeVideoLayout, '视频布局类型', Constant.TConstantCollegeLayoutNormal);
    this.sdk.registerState(Constant.TStateVideoMaxLoadCount, '视频最大加载数量', 50);
    this.sdk.registerState(Constant.TStateVideoLoaderCount, '视频加载数量', 0);
    this.sdk.registerState(Constant.TStateVideoLoaderPage, '当前视频加载页', 0);
    this.sdk.registerState(Constant.TStateCollegeDoubleScreenMode, '当前是否双屏模式', false);
    this.sdk.registerState(Constant.TStateCollegeRequestMicPermission, `是否${roleInfo.student}自己解除禁音`, false);
    this.sdk.registerState(Constant.TStateCollegeEnableStudentMicRequest, `是否允许${roleInfo.student}自己解除禁音`, true);
    this.sdk.registerState(Constant.TStatePPtSharingState, 'ppt分享状态切换', false);
    this.sdk.registerState(Constant.TStateHookToastMessage, '是否hook内部toast消息', false);
    this.sdk.registerState(Constant.TStateEnableUpdateLayout, '是否启用默认的布局更新逻辑', true);
    this.sdk.registerState(Constant.TStateVideoBeautyConfig, '美颜参数配置', 0);
    this.sdk.registerState(Constant.TStateCoTeachingShowHandupList, '双师是否显示举手列表', false);
    this.sdk.registerState(Constant.TStateCoTeachingMemberList, '双师课堂成员列表', []);
    this.sdk.registerState(Constant.TStateCoTeachingLinkUsers, '双师课堂连麦成员列表', []);
    this.sdk.registerState(Constant.TStateCoTeachingLinkUserCount, '双师课堂连麦次数', {});
    this.sdk.registerState(Constant.TStateCoTeachingStudentLayout, `双师课堂${roleInfo.student}未连麦前的布局`, TCIC.TClassLayout.CTOnlyTeacher);
    this.sdk.registerState(Constant.TStateCoMicVolume, '双师课堂麦克风设置的音量', 100);
    this.sdk.registerState(Constant.TStateScreenPermissionGranted, '屏幕共享权限是否开启', true);
    this.sdk.registerState(Constant.TStateLiveTeacherInvitingMe, `直播课${roleInfo.teacher}正在邀请我`, false);
    this.sdk.registerState(Constant.TStateHidePrivateDocument, `是否屏蔽私人${roomInfo.courseware}`, false);
    this.sdk.registerState(Constant.TStateShowSubCameraComponent, '是否显示辅助摄像头组件', false);
    this.sdk.registerState(Constant.TStateSubCameraMirror, '辅助摄像头是否镜像', false);
    this.sdk.registerState(Constant.TConstantVideoWallPage, '视频墙页数', 0);
    this.sdk.registerState(Constant.TStateWebScale, '布局缩放比例', 1);
    this.sdk.registerState(Constant.TStateDocCustom, '自定义 Document', { customTab: {} });
    this.sdk.registerState(Constant.TStateMicVolume, '麦克风采集音量', 100);
    this.sdk.registerState(Constant.TStateNoEndClass, '关闭下课按钮', ''); // 不能直接为false, 否则会设置覆盖session参数.
    this.sdk.registerState(Constant.TStateBigClassLayout, '自定义大班课布局', {});

    // 检查是否处于录制模式，url中带有 role=supervisor&mode=record 为录制模式
    if (this.sdk.isRecordMode()) {
      this.sdk.setState(Constant.TStateRecordMode, true);
    }

    this.isMobilePhone = this.sdk.isMobile() && !this.sdk.isPad();
    this.videoShrinkedBeforeScreenShare = false;
    this.loadingId = 0;

    this.isMouseDown = false;
    this.mouseMoveTask = null;
    this.bFirstLayout = true;
    this.isLeavingClass = false;
    // this.sdk.getUserInfo(this.sdk.getUserId()).then((userInfo) => {
    // const conf = {
    //   // TODO: name不起作用
    //   name: userInfo.nickname,
    //   ext2: JSON.stringify({
    //     ...(JSON.parse(TCIC.aegis.config.ext2 || '{}')),
    //     nickname: userInfo.nickname,
    //     schoolId: userInfo.schoolId,
    //   }),
    // };
    // TCIC.aegis.setConfig(conf);
    // console.error('%c [ conf ]-127', 'font-size:13px; background:pink; color:#bf2c9f;', conf);
    // });
    window.customReload = () => {
      this.removeunloadListener();
      window.location.reload();
    };
    window.removeunloadListener = () => this.removeunloadListener();
  }
  helperLng(lngObj) {
    if (typeof lngObj !== 'object') {
      return lngObj;
    }
    const lngRadio = this.getLanguage();
    return lngObj[lngRadio] || '';
  }
  getLanguage() {
    return TCIC.SDK.instance.getLanguage();
  }
  init() {
    this.sdk.promiseState(TCIC.TMainState.Class_Info_Ready, true)
      .then(() => {   // 需等待获取到课堂信息后才能区分布局类型
        // 更新词条配置
        const { roomInfo, roleInfo } = this.sdk.getNameConfig();
        this.roomInfo = roomInfo;
        this.roleInfo = roleInfo;
        TCIC.SDK.instance.reportLog('LayoutFactoryInstance', LayoutFactory.getInstance().name);
        TCIC.SDK.instance.reportLog('nodeJsProxyVersion', window.__TCIC_NODE_SERVER_HTML_VERSION);
        TCIC.SDK.instance.reportLog('ResourcesLoadTime', window.ResourcesLoadTime);
        TCIC.SDK.instance.reportLog('ResourcesLoadNotSuccess', window.ResourcesLoadNotSuccess);
        TCIC.SDK.instance.reportLog('LoadAllTime', window.LoadAllTime);
        // 延迟上报，防止LCP没统计完
        const timmer = setTimeout(() => {
          TCIC.SDK.instance.reportLog('LCPData', JSON.stringify(window.LCPData));
          clearTimeout(timmer);
        }, 600);

        LayoutFactory.getInstance().initLayout();
      });
    // TODO:
    window.i18nextPromise().then(() => {
      const lang = TCIC.SDK.instance.getLanguage();
      const i18nHasLangData = !!i18nextOri.getDataByLanguage(lang);
      if (!i18nHasLangData) {
        TCIC.SDK.instance.reportLog(
          'messageLanguageError',
          `ui, lang ${lang}, i18nLang ${i18nextOri.language}, i18nHasLangData ${lang} ${i18nHasLangData}, sdkVersion ${TCIC.SDK.instance.getVersion()}`,
        );
      }
      // 显示加载图标
      this.sdk.loadComponent('loading-component', {
        width: '100%',
        height: '100%',
        display: this.sdk.isCollegeClass() ? 'none' : 'block',
        zIndex: this.isMobilePhone && this.sdk.isLiveClass() ? 1501 : 1000,
      })
        .then((loadingDom) => {
          // 信息ready之前不显示具体名称
          if (this.sdk.getSeriousErrorTimes() === 0) {
            this.loadingId = loadingDom.getVueInstance()
              .showText(i18next.t('加载中...'));
          } else {
            this.loadingId = loadingDom.getVueInstance()
              .showText(i18next.t('出现异常，正在重新加载...'));
          }
          this.afterLoading();
          this.sdk.promiseState(TCIC.TMainState.Class_Info_Ready, true)
            .then(() => {
              // 更新title和icon
              this.renderTitleAndFavicon();
              // 获取课堂信息完成后再加载组件(用于根据课堂类型加载不同组件)
              this.loadComponents();
            });
          if (!this.isMobilePhone || !this.sdk.isTeacher()) {
            // 加载白板组件
            this.sdk.loadComponent('board-component', { zIndex: 1 })
              .then(() => {
                this.isBoardLoaded = true;      // 白板加载成功
                if (this.sdk.getParams('delay', 'false') === 'false' || this.isNeedJoinClass) {  // 检查是否设置了延迟进房
                  this.joinClass();
                }
              });
          }
        });
    });
  }

  joinClass() {
    // 初始化本地camera、mic、speaker状态
    this.sdk.registerState(Constant.TStateOpenCamera, '登录时是否打开摄像头', this.sdk.getParams('camera') === undefined ? 1 : this.sdk.getParams('camera'));
    this.sdk.registerState(Constant.TStateOpenSpeaker, '登录时是否打开扬声器', this.sdk.getParams('speaker') === undefined ? 1 : this.sdk.getParams('speaker'));
    this.sdk.registerState(Constant.TStateOpenMic, '登录时是否打开麦克风', this.sdk.getParams('mic') === undefined ? 1 : this.sdk.getParams('mic'));
    // 初始化SDK
    this.sdk.initialize({
      id: 'white-board',
      styleConfig: {
        globalBackgroundColor: this.sdk.getParams('boardColor') || '#ffffff',
        textColor: '#ff0000',
        selectBoxColor: '#727272',
        cursorSize: [40, 40],
        brushThin: 50,
        selectAnchorColor: '#006eff',
      },

      authConfig: {
        isAutoHideRemoteCursor: true,
        showRemoteSelectBox: true,
        showLocalOperator: true,
        showRemoteOperator: true,
        showCursorOnTouch: false, // touch不显示画笔cursor
        pinchToZoomEnable: false,
        elementOperationAuthority: {
          line: {
            disableRotate: true,
          },
          graph: {
            disableRotate: true,
          },
          text: {
            disableUseEmoji: true, // 禁止文本元素使用emoji表情
          },
        },
      },
    }, {
      webUIVersion: Version.getBuildVersion(),
    })
      .then(() => {
        this.sdk.reportLog('joinClassSuccess', this.loadingId);
        const isEnteringTRTC = TCIC.SDK.instance.getState(TCIC.TMainState.Joined_TRTC) === 'entering';
        if (!isEnteringTRTC) {
          // 进房成功后隐藏加载图标
          this.sdk.getComponent('loading-component').getVueInstance()
            .hideText(this.loadingId);
          this.loadingId = 0;
        }
        // 进房成功后刷新布局
        this.sdk.promiseState(Constant.TStateComponentLoaded, true).then(() => {
          this.updateLayoutAfterJoinClass();
        });
        // 注册音量回调
        this.sdk.enableVolumeEvaluation(500);

        if (this.sdk.isFeatureAvailable('MobileImmerseMode') || !this.sdk.isMobile()) {
          if ('ontouchstart' in window) {
            document.addEventListener('touchstart', () => {
              this.onMouseMoveEventHandler(true, true);
            });
            document.addEventListener('touchmove', () => {
              this.onMouseMoveEventHandler(false);
            });
            document.addEventListener('touchend', () => {
              this.onMouseMoveEventHandler(true, false);
            });
          } else {
            document.addEventListener('mousedown', () => {
              this.onMouseMoveEventHandler(true, true);
            });
            document.addEventListener('mousemove', () => {
              this.onMouseMoveEventHandler(false);
            });
            document.addEventListener('mouseup', () => {
              this.onMouseMoveEventHandler(true, false);
            });
          }
          this.onMouseMoveEventHandler(false);       // 初始化定时器
        }

        this.sdk.subscribeState(TCIC.TMainState.Device_Orientation, (orientation) => {
          const isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
          console.warn('%c [ isPortrait ]-288', 'font-size:13px; background:pink; color:#bf2c9f;', isPortrait);

          // this.sdk.updateComponent('quickmsg-show-component', {
          //   display: 'block',
          // });

          if (this.sdk.isUnitedClass()) {
            // 170以上版本在layout中已经处理
            return;
          }
          // 以下兼容170以下版本

          // 滚动消息组建
          const isLiveClass = this.sdk.isLiveClass();
          if ((this.isMobilePhone
            && isLiveClass
            && !isPortrait)
            || !isLiveClass) {
            this.sdk.updateComponent('quickmsg-show-component', {
              display: 'block',
            });
          }
          if (this.isMobilePhone && isLiveClass && isPortrait) {
            this.sdk.updateComponent('quickmsg-show-component', {
              display: 'none',
            });
          }
        });

        this.sdk.promiseState(Constant.TStateDeviceDetect, false).then(() => {
          // 设备检测完成后重置视频码率
          this.sdk.resetLocalVideoEncodeParams();
        });
      })
      .catch((err) => {
        this.sdk.reportLog('joinClassFailed', JSON.stringify(err));
        invokeSyncHooksAsCallback(THookType.Error_JoinClassFail, {
          error: err,
          errorCode: err?.name === 'TCICError' ? err.errorCode : null,
          errorMsg: err?.name === 'TCICError' ? err.errorMsg : null,
        }, {
          complete: () => {
            this.sdk.showErrorMsgBox({
              message: err.errorMsg,
              module: 'TCIC',
              code: err.errorCode,
            });
          },
        });
        throw err;
      })
      .finally(async () => {
        const isEnteringTRTC = TCIC.SDK.instance.getState(TCIC.TMainState.Joined_TRTC) === 'entering';
        if (isEnteringTRTC) {
          this.sdk.subscribeState(TCIC.TMainState.Joined_TRTC, (rtcState) => {
            // 成功或失败都隐藏loading
            if (rtcState !== 'entering') {
              this.sdk.getComponent('loading-component').getVueInstance()
                .hideText(this.loadingId);
            }
          });
        } else {
          this.sdk.getComponent('loading-component').getVueInstance()
            .hideText(this.loadingId);
        }
        // const userId = TCIC.SDK.instance.getUserId();
        // CDPDebugger.init(userId);
        /**
         * 初始化成功，开始检测网络和获取TRTC的统计信息
         */
        const reportNetStates = async () => {
          let netWorkStates = await Promise.all(['https://www.baidu.com', 'https://www.google.com', 'https://www.amazon.com/', 'https://www.aliexpress.com/'].map(pingHost));
          netWorkStates = netWorkStates.filter(item => item.status === 'success');
          const reportData = netWorkStates.map(item => ({
            url: item.url,
            time: item.time.toFixed(1),
          }));
          TCIC.SDK.instance.reportLog('userNetWorkDetect', `${JSON.stringify(reportData)}`);
        };
        reportNetStates();
        const detectTime = 60 * 1000;// 每分钟检测一下与其它域的网络状况
        setInterval(async () => {
          reportNetStates();
        }, detectTime);
        if (!this.sdk.getState(Constant.TStateRecordMode) && this.sdk.isWeb() && !this.sdk.isMobile()) {
          window.addEventListener('beforeunload', this.handleBeforeunloadListener);
        }
      });
  }

  handleBeforeunloadListener(e) {
    e.preventDefault();
    e.returnValue = '您确定要离开此页面吗？';
    return '您确定要离开此页面吗？';
  }

  removeunloadListener()  {
    const callback =  this.handleBeforeunloadListener;
    window.removeEventListener('beforeunload', callback);
  }


  onMouseMoveEventHandler(hasStatus, isRealse) {
    if (TCIC.SDK.instance.getState(Constant.TStateRecordMode, false)) {
      // 录制模式忽略
      return;
    }
    if (TCIC.SDK.instance.getState(Constant.TStateDeviceDetect, true)) {
      // 设备检测时忽略
      return;
    }

    if (TCIC.SDK.instance.isAiRoom()) {
      // AI 课堂忽略
      return;
    }
    // if (!TCIC.SDK.instance.isClassLayoutHasDoc()) {
    //   // 纯视频模式忽略
    //   return;
    // }
    if (TCIC.SDK.instance.getState(TCIC.TMainState.Screen_Share, 2) !== 2 && TCIC.SDK.instance.isElectron()) {  // 非屏幕分享时才有沉浸模式
      clearTimeout(this.mouseMoveTask);
      return;
    }
    // 竖屏 公开课忽略
    const isSmallScreen = this.sdk.isMobile() && !this.sdk.isPad();
    const deviceOrientation = this.sdk.getState(TCIC.TMainState.Device_Orientation, TCIC.TDeviceOrientation.Portrait);
    const isLiveClass = this.sdk.isLiveClass();
    if (isSmallScreen
      && isLiveClass
      && deviceOrientation === TCIC.TDeviceOrientation.Portrait) {    // 手机公共课竖屏
      return;
    }
    if (!isSmallScreen && isLiveClass && TCIC.SDK.instance.isStudent()) {    // 非手机端公开课学生端忽略
      return;
    }
    if (hasStatus) {  // 有状态则记录状态
      this.isMouseDown = isRealse;
    }
    clearTimeout(this.mouseMoveTask);
    this.sdk.setState(Constant.TStateImmerseMode, false);
    if (!this.isMouseDown) {  // 放手后才起沉浸式定时器
      this.mouseMoveTask = setTimeout(() => {
        this.sdk.setState(Constant.TStateImmerseMode, true);
      }, 3000);
    }
  }

  afterLoading() {
    // 试用版仅能使用25分钟/15分钟
    this.sdk.subscribeState(TCIC.TMainState.Class_Duration, (duration) => {
      const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
      const schoolId = schoolInfo?.schoolId;
      if (!!schoolId) {
        const isDemo = schoolId === 3923193 || schoolId === 3454016;
        const freeEditionLimitation = isDemo ? 900 : TCIC.SDK.instance.isFreeEditionTo25Minutes() ? 1500 : 0;
        if (!this.isShowFreeEditionClassEnd && !!freeEditionLimitation && duration >= freeEditionLimitation) {
          this.isShowFreeEditionClassEnd = true;
          if (freeEditionLimitation > 0) {
            TCICCustomUI.hooks(TCICCustomUI.THookType.MsgBox_ClassEnded_BeforeShow).tap((event) => {
              event.cancel();
            }, 100);
            const language = TCIC.SDK.instance.getLanguage();
            const showPurchase = language === 'zh';
            const message = isDemo ? i18next.t('单次课程限时15分钟，如需完整课程请升级至正式版本') : i18next.t('试用版单次课程限时25分钟，如需完整课程请升级至正式版本。');
            this.sdk.endClass()
                .then(() => {
                  this.sdk.showErrorMsgBox({
                    title: i18next.t('课堂已结束'),
                    message,
                    buttons: showPurchase ? [i18next.t('去购买')] : null,
                    callback: (index) => {
                      if (index === 0) {
                        showPurchase && window.open('https://buy.cloud.tencent.com/lcic', '_blank');
                        this.sdk.unInitialize();
                      } else {
                        this.sdk.unInitialize();
                      }
                    },
                  });
                })
                .catch((error) => {
                  setTimeout(() => this.sdk.showErrorMsgBox({
                    title: i18next.t('课堂已结束'),
                    message,
                    buttons: showPurchase ? [i18next.t('去购买')] : null,
                    callback: (index) => {
                      showPurchase && window.open('https://buy.cloud.tencent.com/lcic', '_blank');
                      this.sdk.unInitialize();
                    },
                  }), 100);
                });
          }
        }
      }
    });

    // 监听消息窗显示关闭事件
    this.sdk.on(TCIC.TMainEvent.Show_Msg_Box, (info) => {
      if (this.sdk.getState(Constant.TStateRecordMode)) {
        if (process.env.NODE_ENV === 'development') {
          // 方便开发模式调试
        } else {
          window.customReload(); // 录制模式下，不弹窗，直接刷新页面
          return;
        }
      }
      const msgBoxId = info.msgBoxId.toString();
      let componentName = 'msg-box-component';
      if (this.sdk.isCollegeClass()) {
        componentName = 'college-msg-box-component';
      } else if (this.sdk.isCoTeachingClass()) {
        componentName = 'co-teaching-msg-box-component';
      } else {
        componentName = 'msg-box-component';
      }
      this.sdk.loadComponent(componentName, {
        width: '100%',
        height: '100%',
        zIndex: 30000 + info.msgBoxId,
        display: 'block',
      }, null, msgBoxId)
        .then((msgBox) => {
          if (this.sdk.isCollegeClass()) {
            // 取消透传点击
            TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, false);
          }
          TCIC.SDK.instance.addScreenShareClickArea(msgBox);
          msgBox.getVueInstance().show(info);

          // 语言检查
          const language = TCIC.SDK.instance.getLanguage();
          const checkMsg = `${info.title}/${info.message}/${info.buttons?.join('/')}`;
          if (!/zh(-\w+)?/g.test(language) && /[\u4e00-\u9fa5]+/g.test(checkMsg)) {
            // 不是中文，如果内容中出现了中文就上报
            TCIC.SDK.instance.reportLog('messageLanguageError', `class on Show_Msg_Box: ${checkMsg}`);
          }
        });
      // hook回调函数
      const callback = info.callback;
      info.callback = (index, options) => {
        this.sdk.reportLog('msgBoxBtnClick', `${info.title},${info.message}||clicked:${index}:${info.buttons[index]}, options:${this.sdk.objToString(options)}`);
        if (this.sdk.isCollegeClass()) {
          // 开启透传点击
          const fullScreenUserId = TCIC.SDK.instance.getState(Constant.TStateCollClassVodFullScreen);
          const enable = fullScreenUserId === '';
          // TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, enable);
        }
        // 先删除组件，再调用实际的回调
        this.sdk.removeComponent(componentName, msgBoxId);
        if (callback) {
          callback(index, options);
        }
      };
    });
    this.sdk.on(TCIC.TMainEvent.Close_Msg_Box, (info) => {
      const msgBoxId = info.msgBoxId.toString();
      let componentName = 'msg-box-component';
      if (this.sdk.isCollegeClass()) {
        componentName = 'college-msg-box-component';
      } else if (this.sdk.isCoTeachingClass()) {
        componentName = 'co-teaching-msg-box-component';
      } else {
        componentName = 'msg-box-component';
      }
      const msgBox = this.sdk.getComponent(componentName, msgBoxId);
      if (msgBox) {
        // 强制关闭时返回-1
        msgBox.getVueInstance()
          .close(-1);
      }
    });

    this.sdk.on(TCIC.TMainEvent.Self_Offline, () => {
      this.sdk.showErrorMsgBox({
        message: i18next.t('状态异常，请尝试重新进入课堂'),
        buttons: [i18next.t('重新进入'), i18next.t('取消')],
        callback: (index) => {
          if (index === 1) {
            // this.sdk.unInitialize();
          } else if (index === 0) {
            window.customReload();
          }
        },
      });
    });

    // 监听被踢事件
    this.sdk.on(TCIC.TMainEvent.Kick_Out_By_Another, () => {
      // TODO: 优化这里逻辑，内部多次调用 _quitClass 是不对的
      // 上面原始逻辑在房间内提示后并没有真正的彻底关闭多媒体，需要点击确认退房执行下面方法后才会销毁
      // 这里直接调用最终方法，避免这种场景：
      // 1、web端已经开课，turito 会通过 tcic:// 唤起客户端
      // 2、web被挤掉线后音视频还在干扰原始逻辑

      // 如果用户在上面提示没有点退出，一定时间后主动强制退出
      const unInitializeTimer = setTimeout(() => {
        this.sdk.unInitialize();
      }, 5000);
      this.sdk.showErrorMsgBox({
        message: i18next.t('你的帐号已在其他地方登录'),
        module: 'IM',
        callback: () => {
          clearTimeout(unInitializeTimer);
        },
      });
    });
    this.sdk.on(TCIC.TMainEvent.Kick_Out_By_Expire, () => {
      this.sdk.showErrorMsgBox({
        message: i18next.t('你的帐号签名已过期'),
        module: 'IM',
      });
    });
    this.sdk.on(TCIC.TMainEvent.Kick_Out_By_Self, (forever) => {
      // 录制模式忽略被踢的异常
      if (TCIC.SDK.instance.getState(Constant.TStateRecordMode, false)) {
        TCIC.SDK.instance.reportLog('RecordModeKickedOutBySelf', '1');
        this.sdk.quitRtcRoom();
        return;
      }
      this.sdk.showErrorMsgBox({
        message: i18next.t('你的帐号已在其他地方登录'),
        module: 'IM',
      });
    });
    this.sdk.on(TCIC.TMainEvent.Kick_Out_By_Teacher, (forever) => {
      if (forever) {
        this.sdk.showErrorMsgBox({
          message: i18next.t('你已被移出{{arg_0}}', { arg_0: this.roomInfo.room }),
          module: 'TCIC',
        });
      } else {
        this.sdk.showErrorMsgBox({
          message: i18next.t('你已被移出{{arg_0}}，稍后可重新进入', { arg_0: this.roomInfo.room }),
          module: 'TCIC',
        });
      }
    });

    // 监听资源重新加载事件
    this.sdk.on(TCIC.TMainEvent.Try_Reload_Resource, (type) => {
      if (this.sdk.getState(TCIC.TMainState.Network_Broken)) {
        return;
      }
      Lodash.debounce(() => {
        window.showToast(i18next.t('课件加载失败，请检查网络连接后重试。'));
      }, 3000);
    });

    // 监听资源加载失败事件
    this.sdk.on(TCIC.TMainEvent.Load_Resource_Error, (type) => {
      if (this.sdk.getState(TCIC.TMainState.Network_Broken)) {
        return;
      }
      switch (type) {
        case TCIC.TResourceType.Board_Image:
          window.showToast(i18next.t(
            '图片{{arg_0}}加载出错，{{arg_0}}文件不存在或文件格式不支持',
            { arg_0: this.roomInfo.courseware },
          ));
          break;
      }
    });
    this.sdk.on(TCIC.TMainEvent.After_Leave, () => {
      console.warn('%c [ After_Leave ]-584', 'font-size:13px; background:pink; color:#bf2c9f;');
    });
    // 监听请求退出课堂事件
    this.sdk.on(TCIC.TMainEvent.Leave_Class, () => {
      if (this.isLeavingClass) {
        return;
      }
      const dialogType = TCIC.SDK.instance.isPortraitClass() && TCIC.SDK.instance.isMobile() ? 'actionSheet' : 'dialog';

      const origIsEndClassAllowed = (this.sdk.isTeacher() || this.sdk.isAssistant())
        && this.sdk.getClassInfo().status === TCIC.TClassStatus.Already_Start;

      let isEndClassAllowed = invokeSyncHooks(
        THookType.MsgBox_LeaveClass_IsEndClassAllowed,
        origIsEndClassAllowed,
      );
      if (TCIC.SDK.instance.getParams('noEndClass')) {
        isEndClassAllowed = false;
      }

      const title = isEndClassAllowed
        ? i18next.t('teacherEndRoomConfirm.title', this.roomInfo)
        : i18next.t('commonLeaveRoomConfirm.title', this.roomInfo);
      const message = isEndClassAllowed
        ? i18next.t('teacherEndRoomConfirm.message', this.roomInfo)
        : i18next.t('commonLeaveRoomConfirm.message', this.roomInfo);
      const buttons = [
        {
          // 离开课堂
          text: this.roomInfo.leaveRoom,
          onClick: () => {
            this.removeunloadListener();
            const isAIClass = this.sdk.isAiRoom();
            if (isAIClass)  {
              this.sdk.endClass().then(() => {
                  this.sdk.unInitialize();
              });
            } else {
              this.sdk.unInitialize();
            }
          },
          isLeaving: true,
        },
        {
          // 取消
          text: i18next.t('取消'),
          onClick: () => {
            this.isLeavingClass = false;
          },
        },
      ];

      if (isEndClassAllowed) {
        // 下课
        buttons.unshift({
          text: this.roomInfo.endRoom,
          onClick: () => {
            this.removeunloadListener();
            // 先下课
            this.sdk.endClass()
              .then(() => {
                // 然后离开课堂
                this.sdk.unInitialize();
              })
              .catch((error) => {
                if (error.errorCode === 10301) {  // 课堂已结束
                  this.sdk.unInitialize();
                } else {
                  // 下课失败
                  console.error(`setClass->stop class fail: ${error.errorCode}|${error.errorMsg}`);
                  window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}}', { arg_0: this.roomInfo.endRoom, arg_1: error.errorMsg }), 'error');
                  this.isLeavingClass = false;
                }
              });
          },
          isLeaving: true,
        });
      }

      invokeMsgBoxHooks(THookType.MsgBox_LeaveClass_BeforeShow, {
        title,
        message,
        buttons,
        onComplete: (_, button) => {
          if (!button || !button.isLeaving) {
            this.isLeavingClass = false;
          }
        },
      }, (params) => {
        this.isLeavingClass = true;

        this.sdk.showMessageBox(
          params.title,
          params.message,
          params.buttons,
          params.callback,
          [],
          '',
          false,
          dialogType,
        );
      });
    });

    // 监听SDK异常事件
    this.sdk.on(TCIC.TMainEvent.Error, (err) => {
      this.sdk.showErrorMsgBox({
        title: i18next.t('出现异常'),
        message: err.errorMsg,
        buttons: [i18next.t('重新进入'), i18next.t('检测网络')],
        module: err.module, // 不明确的错误模块来源尝试从err对象里获取
        code: err.errorCode,
        callback: (index) => {
          if (index === 1) {
            // this.sdk.unInitialize();
            TCIC.SDK.instance.getComponent('network-detector-component').getVueInstance()
              .toggle(true);
          } else if (index === 0) {
            window.customReload();
          }
        },
      });
    });
    // tim error
    this.sdk.on(TCIC.TMainEvent.Tim_Error, (err) => {
      this.sdk.showErrorMsgBox({
        title: i18next.t('出现异常'),
        message: err.errorMsg,
        module: 'IM', // 明确的错误模块来源就直接用明确的来源
        code: err.errorCode,
        buttons: [i18next.t('重新进入'), i18next.t('检测网络')],
        callback: (index) => {
          if (index === 1) {
            // this.sdk.unInitialize();
            TCIC.SDK.instance.getComponent('network-detector-component').getVueInstance()
              .toggle(true);
          } else if (index === 0) {
            window.customReload();
          }
        },
      });
    });
    // tim 超时
    this.sdk.on(TCIC.TMainEvent.Tim_Error_Timeout, (err) => {
      // tim超时异常了，退出rtc房间
      this.sdk.quitRtcRoom();
      this.sdk.showErrorMsgBox({
        title: i18next.t('出现异常'),
        message: err.errorMsg,
        module: 'IM',
        code: err.errorCode,
        buttons: [i18next.t('重新进入'), i18next.t('取消')],
        callback: (index) => {
          if (index === 1) {
            // this.sdk.unInitialize();
          } else if (index === 0) {
            window.customReload();
          }
        },
      });
    });

    // 监听SDK警告事件
    this.sdk.on(TCIC.TMainEvent.Warn, (err) => {
      window.showToast(`${err.errorMsg}`, 'error');
    });

    // 监听课堂状态
    this.sdk.subscribeState(TCIC.TMainState.Class_Status, (status) => {
      // const isLive = this.sdk.isLiveClass();
      // const isSmallScreen = this.sdk.isMobile() && !this.sdk.isPad();
      // if (isLive && isSmallScreen) {
      //   // 手机端公开课不展示以下的弹窗
      //   return;
      // }
      const classInfo = this.sdk.getClassInfo();
      if (status === TCIC.TClassStatus.Has_Ended) {  // 已下课
        localStorage.removeItem(this.sdk.getUserId());
        if (this.sdk.isCollegeClass()) {
          invokeMsgBoxHooks(
            THookType.MsgBox_ClassEnded_BeforeShow,
            {
              title: classInfo.className,
              message: i18next.t('roomStatusDesc.hasEnded', this.roomInfo),
              buttons: [
                {
                  text: this.roomInfo.leaveRoom,
                  onClick: () => {
                    this.sdk.unInitialize();
                  },
                },
              ],
              onClose: () => {
                this.sdk.unInitialize();
              },
              report: false,
            },
            params => this.sdk.showErrorMsgBox(params),
          );
        } else {
          invokeMsgBoxHooks(
            THookType.MsgBox_ClassEnded_BeforeShow,
            {
              title: classInfo.className,
              message: i18next.t('roomStatusDesc.hasEnded', this.roomInfo),
              buttons: [
                {
                  text: this.roomInfo.leaveRoom,
                  onClick: () => {
                    this.sdk.unInitialize();
                  },
                },
              ],
              onClose: () => {
                this.sdk.unInitialize();
              },
            },
            params => this.sdk.showErrorMsgBox(params),
          );
        }
      } else if (status === TCIC.TClassStatus.Has_Expired) {  // 已过期，只有没上课时才会变成该状态
        localStorage.removeItem(this.sdk.getUserId());
        const startTime = moment.utc(classInfo.startTime * 1000).local();
        const endTime = moment.utc(classInfo.endTime * 1000).local();
        const messageParams = {
          ...this.roomInfo,
          ...this.roleInfo,
          arg_0: startTime.format('YYYY-MM-DD HH:mm'),
          arg_1: endTime.format('YYYY-MM-DD HH:mm'),
        };
        this.sdk.showErrorMsgBox({
          title: classInfo.className,
          module: 'TCIC',
          message: this.sdk.isTeacher()
            ? i18next.t('roomStatusDesc.hasExpiredWithTeacherGuide', messageParams)
            : i18next.t('roomStatusDesc.hasExpiredWithCommonGuide', messageParams),
          report: false,
        });
      }
    });

    // 监听版本更新
    this.sdk.on(TCIC.TMainEvent.Version_Update, (params) => {
      if (params.force) {
        this.sdk.showMessageBox('', i18next.t('新版本已准备好，建议您更新'), [i18next.t('更新'), i18next.t('取消')], (index) => {
          if (index === 0) {
            this.sdk.reloadClass();
          }
        });
      }
    });

    // 监听自定义消息
    this.sdk.on(TCIC.TMainEvent.Recv_Custom_IM_Msg, (msg) => {
      CustomMsg.processCustomMsg(msg);
    });
  }

  renderTitleAndFavicon() {
    const { schoolName, customContent } = TCIC.SDK.instance.getSchoolInfo();
    // 初始化logo
    const logo = customContent.logo ? customContent.logo : 'https://class.qcloudclass.com/static/livelogo.png';
    try {
      document.title = schoolName || this.roomInfo.defaultAppName;
      const favicon = document.querySelector('#favicon');
      if (favicon) {
        favicon.href = logo;
      }
    } catch (e) {
      console.error('renderTitleAndFavicon', e);
    }
  }

  updateLayoutAfterJoinClass() {
    if (this.sdk.getState(Constant.TStateEnableUpdateLayout)) {
      // 内部更新布局后通知自定义布局
      LayoutFactory.getInstance().updateLayoutAfterJoinClass()
        .then(() => {
          this.sdk.notify(Constant.TEventUpdateLayoutAfterJoinClass);
        })
        .catch((err) => {
          this.sdk.notify(Constant.TEventUpdateLayoutAfterJoinClass);
        });
    } else {
      // 通知自定义布局
      this.sdk.notify(Constant.TEventUpdateLayoutAfterJoinClass);
    }
  }

  updateLayout() {
    if (this.sdk.getState(Constant.TStateEnableUpdateLayout)) {
      // 内部更新布局后通知自定义布局
      LayoutFactory.getInstance().updateLayout()
        .then(() => {
          this.sdk.notify(Constant.TEventUpdateLayout);
        })
        .catch((err) => {
          this.sdk.notify(Constant.TEventUpdateLayout);
        });
    } else {
      // 通知自定义布局
      this.sdk.notify(Constant.TEventUpdateLayout);
    }
  }
  showCoreProcessLoading(text, type) {
    if (this.loadingDom == null) {
      this.sdk.loadComponent('loading-component', {
        width: '100%',
        height: '100%',
        display: 'block',
        zIndex: this.isMobilePhone && this.sdk.isLiveClass() ? 1501 : 1000,
      })
        .then((loadingDom) => {
          this.loadingDom = loadingDom;
          this.loadingIdObj[type] = this.loadingDom.getVueInstance().showText(text);
        });
    } else {
      this.loadingIdObj[type] = this.loadingDom.getVueInstance().showText(text);
    }
  }
  hideCoreProcessLoading(type) {
    if (this.loadingDom == null) {
      this.sdk.loadComponent('loading-component', {
        width: '100%',
        height: '100%',
        display: 'block',
        zIndex: this.isMobilePhone && this.sdk.isLiveClass() ? 1501 : 1000,
      })
        .then((loadingDom) => {
          this.loadingDom = loadingDom;
          this.loadingDom.getVueInstance().hideText(this.loadingIdObj[type], true);
          delete this.loadingIdObj[type];
        });
    } else {
      this.loadingDom.getVueInstance().hideText(this.loadingIdObj[type]);
      delete this.loadingIdObj[type];
    }
  }
  projectCoreProcess(flow) {
    if (TCIC.TMainEvent.Core_process_Start_Login_IM === flow) {
      this.showCoreProcessLoading(i18next.t('正在登录IM'), flow);
    } else if (flow === TCIC.TMainEvent.Core_process_Start_Login_IM_Success || flow == TCIC.TMainEvent.Core_process_Start_Login_IM_Error) {
      this.hideCoreProcessLoading(TCIC.TMainEvent.Core_process_Start_Login_IM);
    } else if (flow === TCIC.TMainEvent.Core_process_Start_Init_Board) {
      this.showCoreProcessLoading(i18next.t('正在始化白板'), flow);
    } else if (flow === TCIC.TMainEvent.Core_process_Start_Init_Board_Success || flow == TCIC.TMainEvent.Core_process_Start_Init_Board_Error) {
      this.hideCoreProcessLoading(TCIC.TMainEvent.Core_process_Start_Init_Board);
      TCIC.SDK.instance.reportLog('BoardInitTime', TCIC.SDK.instance.getInstances().board.getBoardInitTime());
    } else if (flow === TCIC.TMainEvent.Core_process_Start_Join_IM_Group) {
      this.showCoreProcessLoading(i18next.t('正在进群'), flow);
    } else if (flow === TCIC.TMainEvent.Core_process_Join_IM_Group_Success || flow == TCIC.TMainEvent.Core_process_Join_IM_Group_Error) {
      this.hideCoreProcessLoading(TCIC.TMainEvent.Core_process_Start_Join_IM_Group);
    } else if (flow == TCIC.TMainEvent.Core_process_Start_Join_TRTC) {
      this.showCoreProcessLoading(i18next.t('正在进房'), flow);
    } else if (flow == TCIC.TMainEvent.Core_process_Join_TRTC_Success || flow == TCIC.TMainEvent.Core_process_Join_TRTC_Error) {
      this.hideCoreProcessLoading(TCIC.TMainEvent.Core_process_Start_Join_TRTC);
    }
  }
  loadComponents() {
    // 组件规范参考：http://tapd.oa.com/smallclassroom_xinlian/markdown_wikis/show/#1220426766001966955
    TCIC.SDK.instance.reportLog('ComponentFactoryInstance', ComponentFactory.getInstance().name);
    ComponentFactory.getInstance().loadComponents()
      .then(() => {
        this.sdk.setState(Constant.TStateComponentLoaded, true);
      });

    // 关联纯白板展示模式相关组件状态
    this.sdk.subscribeState(Constant.TStateFullScreen, (value) => {
      const isLiveClass = this.sdk.isLiveClass();
      const isThreeOneOnOneClass = this.sdk.isOneOnOneClass() && this.sdk.getClassLayout() === TCIC.TClassLayout.Three;
      const needHideSlide = isLiveClass || isThreeOneOnOneClass;
      if (needHideSlide) {  // 直播课，通过边栏折叠按钮折叠
        const comp = this.sdk.getComponent('side-toggle-button-component');
        comp && comp.getVueInstance()
          .toggleExpand(!value, true);
        // 隐藏视频悬浮窗
        TCIC.SDK.instance.setState(Constant.TStateVideoCtrlUserId, '');
      } else {
        const comp = this.sdk.getComponent('videowrap-component');
        comp && comp.getVueInstance()
          .toggleShow(!value, true);
      }
      // 同步白板/标题组件状态
      // const comp = this.sdk.getComponent('board-footer-component');
      // comp && comp.getVueInstance()
      //   .toggleFullscreen(value, true);

      const header = this.sdk.getComponent('header-component');
      header && header.getVueInstance().$refs.header.toggleFullScreen(value, true);
    }, { noEmitWhileSubscribe: true, noEmitWhileRegister: true });

    // 监听状态变化，更新布局
    // 课堂布局状态变化
    this.sdk.subscribeState(TCIC.TMainState.Class_Layout, this.updateLayout.bind(this));
    // 屏幕分享状态变化
    this.sdk.subscribeState(TCIC.TMainState.Screen_Share, this.updateLayout.bind(this));
    // 视频播放状态变化
    this.sdk.subscribeState(TCIC.TMainState.Vod_Play, this.updateLayout.bind(this));
    // 白板操作权限状态变化
    this.sdk.subscribeState(TCIC.TMainState.Board_Permission, this.updateLayout.bind(this));
    // 设备方向
    this.sdk.subscribeState(TCIC.TMainState.Device_Orientation, this.updateLayout.bind(this));
    // 公开课连麦人数发生变化
    this.sdk.subscribeState(TCIC.TMainState.Stage_Count, this.updateLayout.bind(this));
    // 上课状态发生变化
    this.sdk.subscribeState(TCIC.TMainState.Class_Status, this.updateLayout.bind(this));
    // 全屏状态变化
    this.sdk.subscribeState(Constant.TStateFullScreen, this.updateLayout.bind(this));
    // 缩略图显示状态变化
    this.sdk.subscribeState(Constant.TStateIsShowThumbnailComponent, this.updateLayout.bind(this));
    // 底部工具栏显示状态变化
    this.sdk.subscribeState(Constant.TStateIsHideFooterComponent, this.updateLayout.bind(this));
    // 设备检测状态变化
    this.sdk.subscribeState(Constant.TStateDeviceDetect, this.updateLayout.bind(this));
    // 导航栏显示状态变化
    this.sdk.subscribeState(Constant.TStateHeaderVisible, this.updateLayout.bind(this));
    // 弹幕聊天窗显示开关变化
    this.sdk.subscribeState(Constant.TStateShowQuickIM, this.updateLayout.bind(this));
    // 屏幕分享播放器显示状态变化
    this.sdk.subscribeState(Constant.TStateScreenPlayerVisible, this.updateLayout.bind(this));
    // 点播播放器显示状态变化
    this.sdk.subscribeState(Constant.TStateVodPlayerVisible, this.updateLayout.bind(this));
    // 老师视频是否加载
    this.sdk.subscribeState(Constant.TStateTeacherComponentLoaded, this.updateLayout.bind(this));
    // (大教学)视频窗口显示在白板区域状态变更
    this.sdk.subscribeState(Constant.TStateShowTeacherVideoInWhiteBoardArea, this.updateLayout.bind(this));
    // (大教学)学生视频列表显示状态变更
    this.sdk.subscribeState(Constant.TStateShowStudentsVideoWrap, this.updateLayout.bind(this));
    // (大教学)右边栏显示状态变更
    this.sdk.subscribeState(Constant.TStateShowRightColumn, this.updateLayout.bind(this));
    // 公开课视频是否全屏展示
    this.sdk.subscribeState(Constant.TStateBigVideoMode, this.updateLayout.bind(this));
    // 双师是否显示举手列表
    this.sdk.subscribeState(Constant.TStateCoTeachingShowHandupList, this.updateLayout.bind(this));
    // 录制模式（主要用于本地测试使用）
    this.sdk.subscribeState(Constant.TStateRecordMode, this.updateLayout.bind(this));
    // 应用大小发生变化
    this.sdk.on(TCIC.TMainEvent.App_Resized, this.updateLayout.bind(this));
    // 进入到房间
    this.sdk.on(TCIC.TMainState.Joined_Class, this.updateLayout.bind(this));
    // 页面可见性变化时
    this.sdk.subscribeState(TCIC.TMainState.Class_Visible, (flag) => {
      if (flag) {
        this.updateLayout();
      }
    });
    this.sdk.subscribeState(TCIC.TMainState.Enable_Stage, (enable) => {
      this.updateLayout();
    });
    this.sdk.subscribeState(TCIC.TMainState.Stage_Status, (enable) => {
      this.updateLayout();
    });
    // 弹幕状态变更
    this.sdk.subscribeState(Constant.TStateChatTipsEnable, (isChatTips) => {
      const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
      const screenShare = this.sdk.getState(TCIC.TMainState.Screen_Share, 2);
      const isSubCameraStarted = this.sdk.getState(Constant.TStateStartSubCamera, false);
      const isVodPlayOnElectron = TCIC.SDK.instance.isTeacherOrAssistant()
        && TCIC.SDK.instance.isElectron()
        && (vodPlay < 2);
      const isTeacherScreenShareOnElectron = TCIC.SDK.instance.isTeacher()
        && TCIC.SDK.instance.isElectron()
        && (screenShare < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
      if (isTeacherScreenShareOnElectron) {
        this.sdk.updateComponent('footer-component', {
          width: isChatTips ? '500px' : '230px',
        });
      }
    });
    // 主要流程改变事件
    this.sdk.subscribeState(TCIC.TMainState.Project_Core_Process, this.projectCoreProcess.bind(this));
    // 直播课老师正在邀请我
    this.sdk.subscribeState(Constant.TStateLiveTeacherInvitingMe, this.updateLayout.bind(this));
    const orientationEvent = 'onorientationchange' in window ? 'orientationchange' : 'resize';
    const cond = this.isMobilePhone && (!this.sdk.isTeacher() || !this.sdk.isMiniProgramWebview());
    this.sdk.reportLog('orientationEvent', `${cond},${orientationEvent}`);
    if (cond) {
      window.addEventListener(orientationEvent, (event) => {
        if (TCIC.SDK.instance.getInstances().session?.getLockedOrientation()) {
          return;
        }

        const angle = this.sdk.getOrientationAngle();
        const type = this.sdk.getOrientationType();

        if (type) {
          if (type.includes('portrait')) {
            this.sdk.reportLog('orientationchange', `Portrait:${type}`);
            this.sdk.setDeviceOrientation(TCIC.TDeviceOrientation.Portrait);
          } else if (type.includes('landscape')) {
            this.sdk.reportLog('orientationchange', `Landscape:${type}`);
            this.sdk.setDeviceOrientation(TCIC.TDeviceOrientation.Landscape);
          }
        } else {
          if (angle === 180 || angle === 0) {
            this.sdk.reportLog('orientationchange', `Portrait:${angle},${screen?.orientation?.type}`);
            this.sdk.setDeviceOrientation(TCIC.TDeviceOrientation.Portrait);
          }
          if (angle === 90 || angle === -90 || angle === 270) {
            this.sdk.reportLog('orientationchange', `Landscape:${angle},${screen?.orientation?.type}`);
            this.sdk.setDeviceOrientation(TCIC.TDeviceOrientation.Landscape);
          }
        }
      }, false);
    }
  }
}


// 通过协议消息调用 apaas 接口
class TCICUI {
  constructor() {
    this.Constant = Constant;
    this.notifyEvents = {};
    this.notifyStates = {};
    this.startPassThroughListener();
  }
  async onReceiveMessage(msg) {
    TCIC.SDK.instance.reportLog('WSMessage', JSON.stringify(msg));
    if (!this.isMessageValid(msg)) {
      // 协议校验不通过
      const cbMsg = this.getCallbackMessage(msg, {
        errorCode: -1,
        errorMsg: 'invalid message',
      }, {});
      await window.Electron.onReceiveMessage(cbMsg);
      return;
    }
    const func = msg.func; // 调用函数
    let error = null; // 调用结果
    let ret = null; // 返回值
    try {
      switch (func) {
        case 'startClass':
        case 'endClass':
        case 'unInitialize':
        case 'stopScreenShare':
          await TCIC.SDK.instance[func]();
          break;
        case 'leaveClass':
          await TCIC.SDK.instance.unInitialize();
          break;
        case 'getUserId': {
          const userId = TCIC.SDK.instance.getUserId();
          ret = { userId };
          break;
        }
        case 'getClassInfo': {
          const classInfo = TCIC.SDK.instance.getClassInfo();
          ret = { classInfo };
          break;
        }
        case 'getSchoolInfo': {
          const schoolInfo = TCIC.SDK.instance.getSchoolInfo();
          ret = { schoolInfo };
          break;
        }
        case 'startScreenShare': {
          const params = msg.params.params;
          let type = 0;
          if (Object.prototype.hasOwnProperty.call(params || {}, 'type')) {
            type = params.type;
          }
          if (Object.prototype.hasOwnProperty.call(params || {}, 'includeWindows')) {
            const sources = [];
            params.includeWindows.forEach((sourceId) => {
              sources.push({
                type,
                sourceId,
                sourceName: '',
              });
            });
            await TCIC.SDK.instance.selectScreenCaptureTarget(sources, true, false);
          }
          if (Object.prototype.hasOwnProperty.call(params || {}, 'excludeWindows')) {
            await TCIC.SDK.instance.addExcludedShareWindows(params.excludeWindows);
          }
          TCIC.SDK.instance.reportLog('startScreenShare', `[class] onReceiveMessage ${func}`);
          await TCIC.SDK.instance.startScreenShare();
          break;
        }
        case 'startLocalVideo':
        case 'stopLocalVideo':
        case 'startLocalAudio':
        case 'stopLocalAudio': {
          const videoComponent = TCIC.SDK.instance.getComponent('teacher-videowrap-component');
          if (videoComponent) {
            videoComponent.getVueInstance()[func]();
          }
          break;
        }
        case 'memberAction': {
          const params = msg.params.params;
          const videoComponent = TCIC.SDK.instance.getComponent('teacher-videowrap-component');
          if (videoComponent) {
            videoComponent.getVueInstance()[func](params);
          }
          break;
        }
        case 'toggleFullScreen': {
          const userId = msg.params.userId;
          const videoComponent = TCIC.SDK.instance.getComponent('teacher-videowrap-component');
          if (videoComponent) {
            videoComponent.getVueInstance().toggleFullScreen(userId);
          }
          break;
        }
        case 'muteLocalAudio':
        case 'muteLocalVideo':
        case 'muteAll':
        case 'muteVideoAll':
        case 'muteAllRemoteAudio': {
          const mute = msg.params.mute;
          await TCIC.SDK.instance[func](mute);
          break;
        }
        case 'getCameras':
        case 'getMics': {
          const devices = await TCIC.SDK.instance[func]();
          ret = { devices };
          break;
        }
        case 'getCameraDeviceId':
        case 'getMicDeviceId': {
          const deviceId = await TCIC.SDK.instance[func]();
          ret = { deviceId };
          break;
        }
        case 'switchCamera':
        case 'switchMic': {
          const deviceId = msg.params.deviceId;
          await TCIC.SDK.instance[func](deviceId);
          break;
        }
        case 'getClassMemberList': {
          const filter = msg.params.filter;
          const result = await TCIC.SDK.instance.getClassMemberList(filter);
          ret = { result };
          break;
        }
        case 'getPermission': {
          const userId = msg.params.userId;
          const result = TCIC.SDK.instance.getPermission(userId);
          ret = { result };
          break;
        }
        case 'getPermissionList': {
          const result = TCIC.SDK.instance.getPermissionList();
          ret = { result };
          break;
        }
        case 'on': {
          const event = msg.params.event;
          const innerEvent = this.getInnerEvent(event);
          if (innerEvent) {
            switch (event) {
              case 'onToastMessage': {
                this.onEvent(event, (toast) => {
                  const cbMsg = this.getNotifyMessage(event, null, { toast });
                  window.Electron.onReceiveMessage(cbMsg);
                });
                break;
              }
              case 'onDeviceChanged': {
                this.onEvent(event, (device) => {
                  const cbMsg = this.getNotifyMessage(event, null, { device });
                  window.Electron.onReceiveMessage(cbMsg);
                });
                break;
              }
              case 'onNetworkStatistics': {
                this.onEvent(event, (statistics) => {
                  console.log('trtcStatistics in classJs onNetworkStatistics::', statistics);
                  const cbMsg = this.getNotifyMessage(event, null, { statistics }, false);
                  window.Electron.onReceiveMessage(cbMsg);
                });
                break;
              }
              case 'onPermissionUpdate': {
                this.onEvent(event, (permissionList) => {
                  const cbMsg = this.getNotifyMessage(event, null, { permissionList });
                  window.Electron.onReceiveMessage(cbMsg);
                });
                break;
              }
              case 'onKickOutByAnother': {
                this.onEvent(event, () => {
                  const cbMsg = this.getNotifyMessage(event, null, {});
                  window.Electron.onReceiveMessage(cbMsg);
                });
                break;
              }
              default:
                break;
            }
          } else {
            // 不存在的
            error = {
              errorCode: -1,
              errorMsg: 'invalid event',
            };
          }
          break;
        }
        case 'off': {
          const event = msg.params.event;
          const innerEvent = this.getInnerEvent(event);
          if (innerEvent) {
            this.offEvent(innerEvent);
          } else {
            error = {
              errorCode: -1,
              errorMsg: 'invalid event',
            };
          }
          break;
        }
        case 'subscribeState': {
          const state = msg.params.state;
          const options = msg.params.options;
          const innerState = this.getInnerState(state);
          if (innerState) {
            switch (state) {
              case 'onJoinedTRTC':
              case 'onJoinedClass': {
                this.onState(state, (joined) => {
                  const cbMsg = this.getNotifyMessage(state, null, { joined });
                  window.Electron.onReceiveMessage(cbMsg);
                }, options);
                break;
              }
              case 'onAudioCapture':
              case 'onVideoCapture': {
                this.onState(state, (enable) => {
                  const cbMsg = this.getNotifyMessage(state, null, { enable });
                  window.Electron.onReceiveMessage(cbMsg);
                }, options);
                break;
              }
              case 'onFullScreenChanged': {
                this.onState(state, (userId) => {
                  const cbMsg = this.getNotifyMessage(state, null, { userId });
                  window.Electron.onReceiveMessage(cbMsg);
                }, options);
                break;
              }
              case 'onDeviceDetect': {
                this.onState(state, (isDetect) => {
                  const cbMsg = this.getNotifyMessage(state, null, { isDetect });
                  window.Electron.onReceiveMessage(cbMsg);
                }, options);
                break;
              }
              case 'enableStudentMicRequest': {
                this.onState(state, (enable) => {
                  const cbMsg = this.getNotifyMessage(state, null, { enable });
                  window.Electron.onReceiveMessage(cbMsg);
                }, options);
                break;
              }
              default:
                break;
            }
          } else {
            // 不存在的
            error = {
              errorCode: -1,
              errorMsg: 'invalid state',
            };
          }
          break;
        }
        case 'unsubscribeState': {
          const state = msg.params.state;
          const innerState = this.getInnerState(state);
          if (innerState) {
            this.offState(innerState);
          } else {
            error = {
              errorCode: -1,
              errorMsg: 'invalid state',
            };
          }
          break;
        }
        case 'setState': {
          const state = msg.params.state;
          const value = msg.params.value;
          const innerState = this.getInnerState(state);
          if (innerState) {
            TCIC.SDK.instance.setState(innerState, value);
          } else {
            error = {
              errorCode: -1,
              errorMsg: 'invalid state',
            };
          }
          break;
        }
        case 'getWindowId': {
          const windowId = await window?.Electron?.getWindowId();
          console.log('%c [ windowId ]-1138', 'font-size:13px; background:pink; color:#bf2c9f;', windowId);
          ret = { windowId };
          break;
        }
        case 'updateLayout': {
          // normal: 默认布局, combo: 组合布局, wall: 宫格布局, double-combo: 双屏-组合, double-wall: 双屏-宫格
          if (msg.params.layout === Constant.TConstantCollegeLayoutNormal) {
            TCIC.SDK.instance.setState(Constant.TStateCollegeDoubleScreenMode, false);
            TCIC.SDK.instance.setState(Constant.TStateCollegeVideoLayout, Constant.TConstantCollegeLayoutNormal);
          } else if (msg.params.layout === Constant.TConstantCollegeLayoutCombo) {
            TCIC.SDK.instance.setState(Constant.TStateCollegeDoubleScreenMode, false);
            TCIC.SDK.instance.setState(Constant.TStateCollegeVideoLayout, Constant.TConstantCollegeLayoutCombo);
          } else if (msg.params.layout === Constant.TConstantCollegeLayoutWall) {
            TCIC.SDK.instance.setState(Constant.TStateCollegeDoubleScreenMode, false);
            TCIC.SDK.instance.setState(Constant.TStateCollegeVideoLayout, Constant.TConstantCollegeLayoutWall);
          } else if (msg.params.layout === 'double-combo') {
            TCIC.SDK.instance.setState(Constant.TStateCollegeDoubleScreenMode, true);
            TCIC.SDK.instance.setState(Constant.TStateCollegeVideoLayout, Constant.TConstantCollegeLayoutCombo);
          } else if (msg.params.layout === 'double-wall') {
            TCIC.SDK.instance.setState(Constant.TStateCollegeDoubleScreenMode, true);
            TCIC.SDK.instance.setState(Constant.TStateCollegeVideoLayout, Constant.TConstantCollegeLayoutWall);
          } else {
            error = {
              errorCode: -1,
              errorMsg: 'invalid layout',
            };
          }
          break;
        }
        case 'enableSystemAudioLoopback': {
          const enable = msg.params.enable;
          await TCIC.SDK.instance.enableSystemAudioLoopback(enable);
          break;
        }
        default:
          error = {
            errorCode: -1,
            errorMsg: 'invalid function',
          };
          break;
      }
    } catch (e) {
      error = e;
    } finally {
      const cbMsg = this.getCallbackMessage(msg, error, ret);
      await window.Electron.onReceiveMessage(cbMsg);
    }
  }
  onState(state, listener, options) {
    this.offState(state);
    this.notifyStates[state] = {
      listener,
      options,
    };
    const innerState = this.getInnerState(state);
    TCIC.SDK.instance.subscribeState(innerState, listener, options);
  }
  offState(state) {
    const stateParams = this.notifyStates[state];
    if (stateParams) {
      // 删除旧监听
      const innerState = this.getInnerState(state);
      TCIC.SDK.instance.unsubscribeState(innerState, stateParams.listener, stateParams.options);
    }
  }
  onEvent(event, listener) {
    this.offEvent(event);
    this.notifyEvents[event] = listener;
    const innerEvent = this.getInnerEvent(event);
    TCIC.SDK.instance.on(innerEvent, listener);
  }
  offEvent(event) {
    const listener = this.notifyEvents[event];
    if (listener) {
      // 删除旧监听
      const innerEvent = this.getInnerEvent(event);
      TCIC.SDK.instance.off(innerEvent, listener);
    }
  }
  isMessageValid(msg) {
    return Object.prototype.hasOwnProperty.call(msg || {}, 'seq')
      && Object.prototype.hasOwnProperty.call(msg || {}, 'type')
      && Object.prototype.hasOwnProperty.call(msg || {}, 'func');
  }
  getInnerEvent(event) {
    switch (event) {
      case 'onToastMessage':
        return Constant.TEventToastMessage;
      case 'onDeviceChanged':
        return TCIC.TTrtcEvent.Device_Changed;
      case 'onNetworkStatistics':
        return TCIC.TTrtcEvent.Network_Statistis;
      case 'onPermissionUpdate':
        return TCIC.TMainEvent.Permission_Update;
      case 'onKickOutByAnother':
        return TCIC.TMainEvent.Kick_Out_By_Another;
      default:
        return null;
    }
  }
  getInnerState(state) {
    switch (state) {
      case 'onJoinedClass':
        return TCIC.TMainState.Joined_Class;
      case 'onJoinedTRTC':
        return TCIC.TMainState.Joined_TRTC;
      case 'onAudioCapture':
        return TCIC.TMainState.Audio_Capture;
      case 'onVideoCapture':
        return TCIC.TMainState.Video_Capture;
      case 'onFullScreenChanged':
        return Constant.TStateCollClassVodFullScreen;
      case 'onDeviceDetect':
        return Constant.TStateDeviceDetect;
      case 'enableStudentMicRequest':
        return Constant.TStateCollegeEnableStudentMicRequest;
      case 'hookToastMessage':
        return Constant.TStateHookToastMessage;
      default:
        return null;
    }
  }
  getNotifyMessage(event, error, ret, report = true) {
    const cbMsg = {};
    cbMsg.seq = Date.now();
    cbMsg.func = event;
    cbMsg.type = 3;
    cbMsg.error = {
      code: 0,
      msg: '',
    };
    cbMsg.params = {
    };
    if (error) {
      cbMsg.error.code = error.errorCode;
      cbMsg.error.msg = error.errorMsg;
    }
    if (ret) {
      cbMsg.params = ret;
    }
    if (report) {
      TCIC.SDK.instance.reportLog('WSNotify', JSON.stringify(cbMsg));
    }
    return cbMsg;
  }
  getCallbackMessage(msg, error, ret, report = true) {
    const cbMsg = {};
    Object.assign(cbMsg, msg);
    cbMsg.type = 2;
    cbMsg.error = {
      code: 0,
      msg: '',
    };
    cbMsg.params = {
    };
    if (error) {
      cbMsg.error.code = error.errorCode;
      cbMsg.error.msg = error.errorMsg;
    }
    if (ret) {
      cbMsg.params = ret;
    }
    if (report) {
      TCIC.SDK.instance.reportLog('WSCallback', JSON.stringify(cbMsg));
    }
    return cbMsg;
  }
  startPassThroughListener() {
    if (TCIC.SDK.instance.isCollegeClass() && TCIC.SDK.instance.isElectron()) {
      // 只有大教学在electron启动透明点击
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Joined_Class, true).then(() => {
        TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
          // 设备检测完成后可操作桌面
          // TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, true);
        });
        const elements = [];
        // 视频列表组件
        const wrapElement = TCIC.SDK.instance.getComponent('teacher-videowrap-component');
        if (wrapElement) {
          elements.push(wrapElement);
        }
        const comboElement = TCIC.SDK.instance.getComponent('college-combo-layout-component');
        if (comboElement) {
          elements.push(comboElement);
        }
        const cellElement = TCIC.SDK.instance.getComponent('college-cell-layout-component');
        if (cellElement) {
          elements.push(cellElement);
        }
        // 视频控制栏组件
        const ctrlElement = TCIC.SDK.instance.getComponent('collclass-vod-ctrl-component');
        if (ctrlElement) {
          elements.push(ctrlElement);
        }
        elements.forEach((el) => {
          el.addEventListener('mouseenter', () => {
            TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, false);
          });
          el.addEventListener('mouseleave', () => {
            const fullScreenUserId = TCIC.SDK.instance.getState(Constant.TStateCollClassVodFullScreen);
            const enable = fullScreenUserId === '';
            TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, enable);
          });
        });
      });
    }
  }
}

const pageInit = () => {
  // 页面初始化
  const classMainEntry = new ClassMainEntry();
  classMainEntry.init();
  // 手动进房
  let params = '';
  if (window.manualJoinClass && window.manualJoinClass.paramsString) {
    params = window.manualJoinClass.paramsString;
    classMainEntry.sdk.reportLog('manualJoinClass_params', params);
  }
  window.manualJoinClass = function (paramsString) {
    classMainEntry.sdk.reportLog('manualJoinClass_', params);
    classMainEntry.sdk.initParams(paramsString);
    if (classMainEntry.isBoardLoaded) {
      classMainEntry.joinClass();
    } else {
      classMainEntry.isNeedJoinClass = true;
    }
  };
  window.TCICUI = new TCICUI();

  window.TCICCustomUI = TCICCustomUI;
  const domTlc = document.getElementById('temp-loading-cover');
  domTlc.style.display = 'none';
  if (params) {
    window.manualJoinClass(params);
  }

  function getAtr(dom) {
    if (!dom || !dom.getAttribute) {
      return;
    }
    return dom.getAttribute('data-user-event');
  }

  /**
   * 用户事件规则：
   * param里面带有action取自元素的data-user-event属性，表示关键交互行为。没有的为非关键行为
   * 事件名命名规范：[功能名/模块]-[操作行为]，示例：DeviceDetect-Enter,表示设备检测，进入课堂操作
   * [功能/模块]定义为用户能理解的名.有些可能和组件名相同，有些可能是位置信息描述
   * @param {*} type
   * @returns
   */
  const userEventHandler = function (type) {
    return function (event) {
      TCIC.SDK.instance.notify(TCIC.TTrtcEvent.AUTOPLAY_CONFIRM, 'all', false);
      if (event.target.nodeType === 1) {
        let currentEl = event.target;
        let targetEventName = getAtr(currentEl);
        let findDeepth = 10;
        try {
          /**
           * 如果当前节点找不到 data-user-event 属性，则向上寻找findDeepth层数
           */
          while (!targetEventName) {
            currentEl = currentEl?.parentNode;
            if (!currentEl) {
              break;
            }
            targetEventName = getAtr(currentEl);
            findDeepth = findDeepth - 1;
            if (findDeepth < 0) {
              break;
            }
          }
          let result = {};
          if (targetEventName) {
            result = {
              type,
              param: {
                action: targetEventName,
              },
            };
          } else {
            /**
             * 没有targetEventName，说明可能点击非重点关注的交互元素
             * 则取能获取className或者text值的父级元素
             * 优先保障有className的元素出现,className能协助定位元素位置
             */
            let counter = 10;
            let targetEl = event.target;
            while (!targetEl.className) {
              targetEl = targetEl?.parentNode;
              if (!targetEl) {
                break;
              }
              counter = counter - 1;
              if (counter < 0) {
                break;
              }
            }
            result = {
              type,
              param: {
                text: targetEl.innerText,
                className: targetEl.className,
              },
            };
          }
          TCIC.SDK.instance.notify('user-event', result);
        } catch (err) {
          TCIC.SDK.instance.reportLog('userEventError', `${err}`);
        }
      }
    };
  };
  function onMessage(e) {
    try {
      if (e.data) {
        if (e.data.getTCICReportDataAPIVersion === '1.0.0') {
          TCIC.SDK.instance.reportLog('onBoardPagePostMessage', JSON.stringify(e.data));
          console.log('onBoardPagePostMessage', e.data);
        }
      }
    } catch (e) {

    }
  }
  /**
   * 记录用户点击等事件
   */
  window.addEventListener('click', userEventHandler('click'));
  window.addEventListener('touchstart', userEventHandler('touchstart'));
  window.addEventListener('message', onMessage);
  TCIC.SDK.instance.on(TCIC.TTrtcEvent.Speaker_Device_Changed, (data) => {
      window.showToast(i18next.t('当前扬声器为：{{name}}', { name: data.deviceLabel }));
  });
  TCIC.SDK.instance.on(TCIC.TTrtcEvent.Mic_Device_Changed, (data) => {
    window.showToast(i18next.t('当前麦克风为：{{name}}', { name: data.deviceLabel }));
  });
  TCIC.SDK.instance.on(TCIC.TTrtcEvent.Camera_Device_Changed, (data) => {
    window.showToast(i18next.t('当前摄像头为：{{name}}', { name: data.deviceLabel }));
    TCIC.TTrtc.setLocalStorageDeviceName(TCIC.TTrtcDeviceType.Camera, data.deviceLabel);
  });
};

pageInit();

