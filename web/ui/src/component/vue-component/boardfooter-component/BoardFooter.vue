<template>
  <div>
    <BoardFooterPC
      v-if="!isPhone"
      ref="footer"
      :file-info-list="fileInfoList"
      :thumbnail-active="thumbnailActive"
      :drag-active="dragActive"
      :current-board-file-id="currentBoardFileId"
      :current-scale="currentScale"
      :min-scale="minScale"
      :max-scale="maxScale"
      :scale-step="scaleStep"
      :current-page="currentPage"
      :total-page="totalPage"
      :is-pdf-or-doc-file="isPdfOrDocFile"
      :is-page-file="isPageFile"
      :list-active="listActive"
    />
    <BoardFooterMobile
      v-if="isPhone & !isShowScreenPlayer & !isShowVodPlayer"
      ref="mobilefooter"
      :file-info-list="fileInfoList"
      :current-page="currentPage"
      :current-board-file-id="currentBoardFileId"
      :total-page="totalPage"
      :is-pdf-or-doc-file="isPdfOrDocFile"
      :is-page-file="isPageFile"
      :list-active="listActive"
    />
    <!-- 根本没有用到.. 小尺寸 暂定小于1080时启用 -->
    <!-- <BoardFooterPCSmall v-if="isPCSmall" /> -->
  </div>
</template>

<script>
import Constant from '@/util/Constant';
import Util from '../../../util/Util';
import BoardFooterPC from './BoardFooterPC.vue';
import BoardFooterMobile from './BoardFooterMobile.vue';
// import BoardFooterPCSmall from './BoardFooterPCSmall.vue';
import BaseComponent from '../../core/BaseComponent';
import i18next from 'i18next';
import DocumentUtil from '@/util/Document';
const TEduBoard = window.TEduBoard;

export default {
  components: {
    BoardFooterPC,
    BoardFooterMobile,
    // BoardFooterPCSmall,
  },
  extends: BaseComponent,
  data() {
    return {
      isPhone: TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isPad(),
      isPCSmall: false, // 是否为小尺寸显示
      isShowScreenPlayer: false,    // 是否在观看屏幕共享
      isShowVodPlayer: false,       // 是否在观看视频课件
      thumbnailActive: false,       // 是否显示缩略图
      showThumbnailForPageFile: true, // 对于可以翻页的课件，是否显示缩略图
      // 白板实例
      teduBoard: null,
      // 文件列表
      fileInfoList: [],
      listActive: false,
      // 是否是可以翻页的课件，决定显示哪些功能
      isPageFile: false,
      // 是否是PDF或Doc，决定显示哪些功能
      isPdfOrDocFile: false,
      dragActive: false,
      // 当前所在白板ID或文件ID
      currentBoardFileId: '',
      // 上一次的白板工具
      lastToolType: - 1,
      // 最小缩放比例
      minScale: 100,
      // 最大缩放比例
      maxScale: 1600,
      // 缩放单步系数
      scaleStep: 10,
      // 当权缩放比例
      currentScale: 100,
      // 当前所在页
      currentPage: 0,
      // 总页数
      totalPage: 0,
      boardPermission: false,       // 白板权限
    };
  },
  watch: {
    isPageFile(val) {
      console.log(`BoardFooter isPageFile changed ${val}, showThumbnailForPageFile ${this.showThumbnailForPageFile}`);
      this.toggleThumbnail(val && this.showThumbnailForPageFile && TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT'));
      this.$emit('change-is-page-file', val);
    },
  },
  mounted() {
    const ownerTcicComponent = this.getOwnerTCICComponent();
    console.log('BoardFooter mounted, ownerTcicComponent', ownerTcicComponent?.theTCICComponentName);
    // 课件模式是否显示缩略图
    TCIC.SDK.instance.registerState(Constant.TStateShowThumbnailForCourseware, '课件模式是否显示缩略图', true);
    this.addLifecycleTCICStateListener(Constant.TStateShowThumbnailForCourseware, (flag) => {
      this.showThumbnailForPageFile = !!flag;
    });
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Create, true).then(() => {
      this.teduBoard = TCIC.SDK.instance.getBoard();
      // 白板已经创建
      this.initEvent();
      this.updatePage();
    });
    this.addLifecycleTCICStateListener(Constant.TStateBoardToolType, (toolType) => {
      console.log('lastToolType', toolType);
      if (!this.isPhone) {
        this.dragActive = (toolType === TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);  // 可拖动按钮状态和工具类型联动
        if (!this.dragActive) {
          this.lastToolType = toolType;
        }
      }
    });
    // 注册缩略图组件显示状态
    TCIC.SDK.instance.registerState(Constant.TStateIsShowThumbnailComponent, '缩略图是否显示', false);
    this.addLifecycleTCICStateListener(Constant.TStateIsShowThumbnailComponent, (flag) => {
      this.thumbnailActive = !!flag;
    });
    this.addLifecycleTCICStateListener(Constant.TStateScreenPlayerVisible, flag => this.isShowScreenPlayer = flag);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, (flag) => {
      this.boardPermission = !!flag;
    });
    this.addLifecycleTCICStateListener(Constant.TStateVodPlayerVisible, flag => this.isShowVodPlayer = flag);
  },
  beforeDestroy() {
    this.unInitEvent();
  },
  methods: {
    toggleFullscreen(isFull, isSync = false) {
      if (!this.isPhone) {
        this.$refs.footer.toggleFullscreen(isFull, isSync);
      }
    },
    toggleThumbnail(val) {
      let isShow = val;
      if (typeof isShow !== 'boolean') {
        isShow = !this.thumbnailActive;
      }
      if (!this.isPageFile && isShow) {
        console.warn('BoardFooter toggleThumbnail true when not pagefile, change to false');
        isShow = false;
      }
      console.log('BoardFooter toggleThumbnail', isShow);
      if (this.isPageFile && this.showThumbnailForPageFile !== isShow) {
        // 可以翻页的课件才修改这个
        console.log('BoardFooter set showThumbnailForPageFile', isShow);
        this.showThumbnailForPageFile = isShow;
        TCIC.SDK.instance.setState(Constant.TStateShowThumbnailForCourseware, this.showThumbnailForPageFile);
      }
      if (this.thumbnailActive !== isShow) {
        console.log('BoardFooter set thumbnailActive', isShow);
        if (TCIC.SDK.instance.getPermission(TCIC.SDK.instance.getUserId()).board) {
          TCIC.SDK.instance.setState(Constant.TStateIsShowThumbnailComponent, isShow);
        }
      }
    },
    updateFile(fileId) {
      const fileInfo = this.teduBoard.getFileInfo(fileId);
      this.isPageFile = this.isFileHasPage(fileInfo);
      this.isPdfOrDocFile = this.isPdfOrDoc(fileInfo);
      // 切换文件则需要隐藏列表
      this.listActive = false;
      this.updatePage();
      this.updateSelectedFile();
    },
    updatePage() {
      const fileId = this.teduBoard.getCurrentFile();
      if (!fileId) {
        return;
      }
      const fileInfo = this.teduBoard.getFileInfo(fileId);
      this.currentScale = this.teduBoard.getBoardScale();
      this.onDrag();
      this.isPageFile = this.isFileHasPage(fileInfo);
      this.isPdfOrDocFile = this.isPdfOrDoc(fileInfo);
      if (this.isPageFile) {
        // 静态转码文件或动态转码文件
        this.currentPage = fileInfo.currentPageIndex + 1;
        this.totalPage = fileInfo.pageCount;
      }
      this.updateSelectedFile();
    },
    onScale(step) {
      const oldScale = this.teduBoard.getBoardScale();
      if (oldScale === this.minScale && step < 0) {
        // 已经是最小放大倍数
        return;
      }
      if (oldScale === this.maxScale && step > 0) {
        // 已经是最大放大倍数
        return;
      }
      this.currentScale = Math.min(Math.max(oldScale + step, this.minScale), this.maxScale);
      // this.teduBoard.setFileScale(this.currentBoardFileId, this.currentScale);
      this.teduBoard.setBoardScale(this.currentScale);
      this.onDrag();
    },
    updateSelectedFile() {
      // 当前所在白板页或者文件
      if (this.teduBoard.getCurrentFile() === '#DEFAULT') {
        this.currentBoardFileId = this.teduBoard.getCurrentBoard();
      } else {
        this.currentBoardFileId = this.teduBoard.getCurrentFile();
      }

      if (this.isPdfOrDocFile) {
        const boardWidth = document.getElementById('white-board').clientWidth;
        const boardHeight = document.getElementById('white-board').clientHeight;

        if (boardWidth !== 0 && boardHeight !== 0 && !isNaN(boardWidth) && !isNaN(boardHeight)) {
          this.currentScale = this.teduBoard.getBoardScale();
        } else {
          // 延时刷新下，获取下isPdfOrDocFill的状态
          setTimeout(() => {
            this.updateSelectedFile();
          }, 500);
        }
      }
    },
    unInitEvent() {
      if (this.teduBoard) {
        this.teduBoard.off(TEduBoard.EVENT.TEB_ADDBOARD, this.refreshFileInfoList);
        this.teduBoard.off(TEduBoard.EVENT.TEB_DELETEBOARD, this.refreshFileInfoList);
        this.teduBoard.off(TEduBoard.EVENT.TEB_DELETEFILE, this.refreshFileInfoList);
        this.teduBoard.off(TEduBoard.EVENT.TEB_ADDH5PPTFILE, this.refreshFileInfoList);
        this.teduBoard.off(TEduBoard.EVENT.TEB_ADDFILE, this.refreshFileInfoList);
        this.teduBoard.off(TEduBoard.EVENT.TEB_SWITCHFILE, this.updateFile);
        this.teduBoard.off(TEduBoard.EVENT.TEB_GOTOBOARD, this.updatePage);
        this.teduBoard.off(TEduBoard.EVENT.TEB_ZOOM_DRAG_STATUS, this.onDragStatusChange);
        // const isPad = TCIC.SDK.instance.isPad();
        // if (isPad) {
        //   this.teduBoard.off(TEduBoard.EVENT.TEB_BOARD_SCROLL_CHANGED, this.onDragScroll);
        // }
      }
      // clearTimeout(this.scrollTimer);
    },
    initEvent() {
      // 监听白板翻页
      this.teduBoard = TCIC.SDK.instance.getBoard();
      this.teduBoard.on(TEduBoard.EVENT.TEB_SWITCHFILE, this.updateFile);
      this.teduBoard.on(TEduBoard.EVENT.TEB_GOTOBOARD, this.updatePage);
      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDBOARD, this.refreshFileInfoList);
      this.teduBoard.on(TEduBoard.EVENT.TEB_DELETEBOARD, this.refreshFileInfoList);
      this.teduBoard.on(TEduBoard.EVENT.TEB_DELETEFILE, this.refreshFileInfoList);
      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDH5PPTFILE, this.refreshFileInfoList);
      this.teduBoard.on(TEduBoard.EVENT.TEB_ADDFILE, this.refreshFileInfoList);
      this.teduBoard.on(TEduBoard.EVENT.TEB_ZOOM_DRAG_STATUS, this.onDragStatusChange);
      this.teduBoard.on(TEduBoard.EVENT.TEB_BOARD_SCALE_CHANGE, this.onDragStatusChange);

      // const isPad = TCIC.SDK.instance.isPad();
      // if (isPad) {
      //   this.teduBoard.on(TEduBoard.EVENT.TEB_BOARD_SCROLL_CHANGED, this.onDragScroll);
      // }
      // pdf/doc 去掉自适应 默认缩放
      // this.teduBoard.on(TEduBoard.EVENT.TEB_ADDTRANSCODEFILE, (fileId) => {
      //   console.log('======================:  ', 'TEB_ADDTRANSCODEFILE', fileId);
      //   const fileInfo = TCIC.SDK.instance.getBoard().getFileInfo(fileId);
      //   if (this.isPdfOrDoc(fileInfo)) {
      //     // 1.5.0需求：pdf/doc打开自适应宽度
      //     const boardWidth = document.getElementById('white-board').clientWidth;
      //     const boardHeight = document.getElementById('white-board').clientHeight;
      //     // 获取pdf显示的宽度
      //     const ratioStr = fileInfo.ratio;
      //     const ratios = ratioStr.split(':');
      //     // 获取pdf显示的宽度
      //     const pdfWidth = parseInt(ratios[0], 10);
      //     const pdfHeight = parseInt(ratios[1], 10);
      //     const scale = (boardWidth  * pdfHeight * 1.0) / (boardHeight * pdfWidth);
      //     // eslint-disable-next-line no-mixed-operators
      //     const fillScale = (scale * 100) - (scale * 100) % 10;
      //     setTimeout(() => {
      //       TCIC.SDK.instance.getBoard().setFileScale(fileId, fillScale);
      //       this.currentScale = fillScale;
      //       // 修复：首次进入横向横向铺满时，滑到页顶，同时onDrag设置boardToolType为TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG
      //       this.teduBoard.setScaleAnchor(0.5, 0);
      //       this.onDrag();
      //       console.log(`===>>> DocumentFooterPC.vue : AddPDF DONE : ${this.currentScale}`);
      //     }, 0);
      //   }
      // });
    },
    onDragStatusChange(data) {
      // fix： 输入文本自动换行居然会触发TEB_BOARD_SCALE_CHANGE，导致文字换行后失焦, see https://tapd.woa.com/tapd_fe/70068217/story/detail/1070068217123521894
      if (data.scale === this.teduBoard.getBoardScale()) {
        return;
      }
      this.currentScale = this.teduBoard.getBoardScale();
      this.onDrag();
    },
    onDrag(dragActive) {
      if (typeof dragActive !== 'boolean') {
        // 没有指定拖动工具，则由放大比例决定
        dragActive = (this.currentScale !== this.minScale);
      }

      if (dragActive) {
        const oldScale = this.teduBoard.getBoardScale();
        if (oldScale === this.minScale) {
          // 已经是最小放大倍数
          return;
        }
        // // 设置工具类型为拖动, alexi: 为解决 ：滑动翻页时，直接跳到新顶底部加上，但引起其他的问题：放大时，pdf会变换位置 : http://tapd.oa.com/20426766/bugtrace/bugs/view?bug_id=1020426766094467245
        // if (this.isPdfOrDocFile) {
        //   this.teduBoard.setScaleAnchor(0, 0);
        // }
        this.teduBoard.setToolType(TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);
        TCIC.SDK.instance.setState(Constant.TStateBoardToolType, TEduBoard.TOOL_TYPE.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);
      } else {
        // 还原工具类型
        if (this.lastToolType >= 0) {
          this.teduBoard.setToolType(this.lastToolType);
          TCIC.SDK.instance.setState(Constant.TStateBoardToolType, this.lastToolType);
        }
      }
    },
    isFileHasPage(fileInfo) {
      return Util.isFileHasPage(fileInfo);
    },
    isPdfOrDoc(fileInfo) {
      return Util.isPdfOrDoc(fileInfo);
    },
    pdfOrDocFillScale(curBoardId) {
      const boardWidth = document.getElementById('white-board').clientWidth;
      const boardHeight = document.getElementById('white-board').clientHeight;

      const fileInfo = this.teduBoard.getFileInfo(curBoardId);
      const ratioStr = fileInfo.ratio;
      const ratios = ratioStr.split(':');
      // 获取pdf显示的宽度
      const pdfWidth = parseInt(ratios[0], 10);
      const pdfHeight = parseInt(ratios[1], 10);
      const scale = (boardWidth  * pdfHeight) / (boardHeight * pdfWidth);
      return scale * 100 - ((scale * 100) % 10);
    },
    togglePdfFillBoard(fill) {
      if (this.isPdfOrDocFile) {
        if (fill) {
          this.currentScale = this.pdfOrDocFillScale(this.currentBoardFileId);
        } else {
          this.currentScale = this.minScale;
        }
        this.teduBoard.setFileScale(this.currentBoardFileId, this.currentScale);
        this.onDrag();
      }
    },
    onDeleteBoard(fileId, boardId) {
      const isStudent = TCIC.SDK.instance.isStudent();
      TCIC.SDK.instance.showMessageBox('', i18next.t('白板删除后将不可恢复，确定要删除吗？'), [i18next.t('删除'), i18next.t('取消')], (index) => {
        if (index === 0) {
          if (boardId.includes('#DEFAULT')) {
            // 删除白板
            this.teduBoard.deleteBoard(boardId);
          } else {
            // 删除文件
            this.teduBoard.deleteFile(fileId);
          }
          if (isStudent) {
            this.teduBoard.syncAndReload();
          }
        }
      });
    },
    async onAddBoard() {
      await DocumentUtil.addSnapshotMark('addboard');
      this.teduBoard.addBoard();
      // 添加白板隐藏掉缩略图
      TCIC.SDK.instance.setState(Constant.TStateIsShowThumbnailComponent, false);
    },
    async onGotoBoardFile(fileId, boardId) {
      // 切换后隐藏白板列表
      this.listActive = false;
      if (this.currentBoardFileId === fileId) {
        // 如果选中当前页或者当前文件，什么都不做
        return;
      }
      TCIC.SDK.instance.reportLog('white-board:switchfile', `fileId:${fileId}, boardId:${boardId}`);
      await DocumentUtil.addSnapshotMark('switchfile');
      if (fileId.indexOf('#DEFAULT') !== - 1) {
        // 默认文件的白板跳转
        // 从其他文件跳转到白板默认文件没有SWITCH事件，需要主动调一次switchFile
        this.teduBoard.switchFile('#DEFAULT');
        this.teduBoard.gotoBoard(boardId);
        // 白板文件隐藏掉thumbnail
        TCIC.SDK.instance.setState(Constant.TStateIsShowThumbnailComponent, false);
      } else {
        // 文件跳转
        this.teduBoard.switchFile(fileId);
      }
    },
    refreshFileInfoList() {
      if (!this.listActive) {
        return;
      }
      const fileInfoList = this.teduBoard.getFileInfoList();
      const tmpFileInfoList = [];
      const isStudent = TCIC.SDK.instance.isStudent();
      let onlineElIdMap = localStorage.getItem('onlineElIdMap');
      onlineElIdMap = onlineElIdMap ? JSON.parse(onlineElIdMap) : {};
      let tmpFileInfoItem = {};
      let type = 'board-tool__list-board';
      let title = '';
      let canDel = true;
      fileInfoList.forEach((fileItem) => {
        if (fileItem.fid === '#DEFAULT') {
          // 如果是默认文件，则将白板页全部展示在列表中
          fileItem.boardInfoList.forEach((boardItem, index) => {
            type = 'board-tool__list-board';
            title = '';
            if (boardItem.boardId === '#DEFAULT') {
              // 第一页白板为默认白板，不可删除
              title = i18next.t('白板{{pageIndex}}', { pageIndex: 1 });
              canDel = false;
            } else {
              canDel = !isStudent;
              // 老师学生同时添加白板，存在页码碰撞的情况
              // 由于1.3.2需要先按数组列表索引递增处理，可能会存在老师白板3对应学生白板2的问题
              // let pageIndex = parseInt(boardItem.boardId.split('_')[3]) + 1;
              const pageIndex = index + 1;
              title = i18next.t('白板{{pageIndex}}', { pageIndex });
              if (onlineElIdMap[boardItem.boardId]) {
                title = onlineElIdMap[boardItem.boardId];
                if (title.includes('#default-title#')) title = title.replace('#default-title#', String(pageIndex));
              } else if (boardItem.backgroundUrl === '') {
                title = i18next.t('白板{{pageIndex}}', { pageIndex });
              } else {
                type = 'board-tool__list-picture';
                title = boardItem.backgroundUrl.substring(boardItem.backgroundUrl.lastIndexOf('/') + 1);
                if (title !== undefined) {
                  title = decodeURI(title);
                }
              }
            }
            // 默认文件的每一页白板都属于列表的一项，和文件同级，所以将fileId也置为boardId
            tmpFileInfoItem = {
              title,
              fileId: boardItem.boardId,
              boardId: boardItem.boardId,
              type,
              canDel,
            };
            tmpFileInfoList.push(tmpFileInfoItem);
          });
        } else {
          // 如果是其他文件
          const suffix = fileItem.title.substring(fileItem.title.lastIndexOf('.') + 1).toLowerCase();
          type = 'board-tool__list-board';
          title = fileItem.title;
          if (suffix === 'pdf') {
            type = 'board-tool__list-pdf';
          } else if (suffix === 'ppt' || suffix === 'pptx') {
            type = 'board-tool__list-ppt';
          } else if (suffix === 'doc' || suffix === 'docx') {
            type = 'board-tool__list-word';
          }

          tmpFileInfoItem = {
            title,
            fileId: fileItem.fid,
            boardId: fileItem.fid,
            type,
            canDel: !isStudent,
          };
          tmpFileInfoList.push(tmpFileInfoItem);
        }
      });
      this.fileInfoList = tmpFileInfoList;
      this.$nextTick(() => {
        if (this.isPhone) {
          this.$refs.mobilefooter?.$refs?.boardListPopper?.updatePopper();
        } else {
          this.$refs.footer?.$refs?.boardListPopper?.updatePopper();
        }
        this.updateSelectedFile();
      });
    },
    onBoardList() {
      this.listActive = !this.listActive;
      if (this.listActive) {
        this.refreshFileInfoList();
      }
      if (this.listActive) {
        this.addHideBoardListEvent();
      } else {
        this.removeHideBoardListEvent();
      }
    },
    addHideBoardListEvent(e) {
      document.addEventListener('mousedown', this.hideBoardList, true);
      document.addEventListener('touchstart', this.hideBoardList, true);
    },
    removeHideBoardListEvent() {
      document.removeEventListener('mousedown', this.hideBoardList, true);
      document.removeEventListener('touchstart', this.hideBoardList, true);
    },
    hideBoardList(e) {
      let refs = this.$refs.footer;
      if (this.isPhone) {
        refs = this.$refs.mobilefooter;
      };
      if (refs.$refs['board-tool__list'].contains(e.target)
          || refs.$refs['board-tool__list-popper'].contains(e.target)) {
        // 如果点击在列表图标，跳过操作
        // 如果在列表中点击页，跳过操作
        return;
      }
      this.listActive = false;
      this.removeHideBoardListEvent();
    },
    onComponentVisibilityChange(visible) {
      if (!visible) {
        // 控件不可见，收起白板列表
        this.listActive = false;
      }
    },
  },
};
</script>
