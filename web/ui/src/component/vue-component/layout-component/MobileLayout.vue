<template>
  <div
    class="layout-mobile-component"
    :class="{
      'layout-mobile-videoonly': isVideoOnly,
    }"
  >
    <MobileBigClassLayout v-if="isBigClass" />
    <MobileSmallClassLayout v-else />
  </div>
</template>
<script>
import MobileBigClassLayout from './mobile/bigClass.vue';
import MobileSmallClassLayout from './mobile/smallClass.vue';
export default {
  name: 'MobileLayout',
  components: {
    MobileBigClassLayout,
    MobileSmallClassLayout,
  },
  data() {
    return {
      isBigClass: TCIC.SDK.instance.isBigRoom(),
      isVideoOnly: TCIC.SDK.instance.isVideoOnlyClass(),
    };
  },
  mounted() {
    this.initLayout();
    window.addEventListener('orientationchange', () => {
      if (window.orientation === 90 || window.orientation === -90) {
        TCIC.SDK.instance.setDeviceOrientation(TCIC.TDeviceOrientation.Landscape);
      }
      if (window.orientation === 0 || window.orientation === 180) {
        TCIC.SDK.instance.setDeviceOrientation(TCIC.TDeviceOrientation.Portrait);
      }
    }, false);
  },
  methods: {
    initLayout() {
      // 移动端布局必然需要这个东西
      this.$nextTick(() => {
        TCIC.SDK.instance.updateComponent('mobile-im-input-bar-component', {
          top: '0',
          left: '0',
          width: '100%',
          height: '100%',
          display: 'block',
        });
      });
    },
  },
};
</script>

<style lang="less">

.layout-mobile-component {
  height: 100%;
  width: 100%;
  padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
  padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
}
@media screen and (orientation:landscape) {
  .layout-mobile-component.layout-mobile-videoonly {
    padding-bottom: 0;
  }
}
</style>
