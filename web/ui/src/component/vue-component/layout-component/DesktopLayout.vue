<template>
  <div class="layout-desktop-component">
    <DesktopBigClassLayout
      v-if="isBigClass"
      v-bind="{teacherVideo, studentVideos}"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch } from 'vue';
import i18next from 'i18next';
import Constant from '@/util/Constant';
import DesktopBigClassLayout from './desktop/bigClass.vue';

const isBigClass = ref(false);
const teacherVideo = ref(null);
const studentVideos = ref([]);
const headerPadding = ref('64px');

const initLayout = () => {
  nextTick(() => {
    if (TCIC.SDK.instance.isPad()) {
      TCIC.SDK.instance.updateComponent('mobile-im-input-bar-component', {
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        display: 'block',
      });
    }
    TCIC.SDK.instance.updateComponent('quickmsg-show-component', {
      display: 'block',
      height: 'calc(100% - 130px)',
      zIndex: 900,
    });
    TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = true;
  });
};

const handleAVAdd = (info) => {
  const classInfo = TCIC.SDK.instance.getClassInfo();
  if (classInfo.teacherId === info.userId) {
    TCIC.SDK.instance.loadComponent('teacher-component', {
      left: '0',
      top: '0',
      display: 'block',
      position: 'relative',
    }).then(() => {
      setTimeout(() => {
        teacherVideo.value = info;
      }, 0);
    });
  } else {
    TCIC.SDK.instance.loadComponent('student-component', {
      left: '0',
      top: '0',
      display: 'block',
      position: 'relative',
    }, null, info.userId).then((ele) => {
      if (ele) {
        setTimeout(() => {
          studentVideos.value.push(info);
        }, 0);
      }
    });
  }
};

const handleAVRemove = (info) => {
  const classInfo = TCIC.SDK.instance.getClassInfo();
  if (info.userId === TCIC.SDK.instance.getUserId()) {
    window.showToast(i18next.t('你已下台，暂时无法参与音视频互动~'));
  }
  if (classInfo.teacherId === info.userId) {
    if (TCIC.SDK.instance.isBigRoom()) {
      return;
    }
    const teacherDom = TCIC.SDK.instance.getComponent('teacher-component');
    if (teacherDom) {
      TCIC.SDK.instance.removeComponent('teacher-component');
      teacherVideo.value = null;
    }
  } else {
    const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
    if (studentDom) {
      const idx = studentVideos.value.findIndex(student => student.userId === info.userId);
      TCIC.SDK.instance.removeComponent('student-component', info.userId);
      studentVideos.value.splice(idx, 1);
    }
  }
};

watch(isBigClass, (val) => {
  if (val) {
    // 大班课无论老师在不在都要显示 teacher-component
    handleAVAdd({
      userId: TCIC.SDK.instance.getClassInfo().teacherId,
      userName: TCIC.SDK.instance.getClassInfo().teacherName,
    });
  }
});

onMounted(() => {
  isBigClass.value = TCIC.SDK.instance.isBigRoom();
  initLayout();
  TCIC.SDK.instance.on(TCIC.TMainEvent.AV_Add, handleAVAdd);
  TCIC.SDK.instance.on(TCIC.TMainEvent.AV_Remove, handleAVRemove);
  // 暂时去掉，有需要再放出来
  // TCIC.SDK.instance.subscribeState(Constant.TStateImmerseMode, (flag) => {
  //   if (flag) {
  //     headerPadding.value = '0';
  //   } else {
  //     headerPadding.value = '64px';
  //   }
  // });
});

onBeforeUnmount(() => {
  TCIC.SDK.instance.off(TCIC.TMainEvent.AV_Add, handleAVAdd);
  TCIC.SDK.instance.off(TCIC.TMainEvent.AV_Remove, handleAVRemove);
});
</script>

<style lang="less">
.layout-desktop-component {
  width: 100%;
  height: 100%;
  padding-top: v-bind(headerPadding);
}
</style>
