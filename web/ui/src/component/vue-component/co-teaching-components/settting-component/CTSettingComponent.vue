<template>
  <div class="device-detect-wrapper ct-device-detect-wrapper">
    <div
      class="detect-enter"
    >
      <div class="setting">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--  摄像头  -->
            <section class="section">
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <i
                    v-if="isCameraOpen"
                    class="icon-video device-icon"
                  />
                  <i
                    v-else
                    class="icon-video-disabled device-icon"
                  />
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('摄像头') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="isCameraOpen"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-able"
                      @click="enableCamera(true)"
                    />
                    <div
                      class="info"
                      :class="[ !detection.camera.available ? 'info-disable' : '']"
                    >
                      {{ isCameraOpen ? $t('打开') : $t('关闭') }}
                    </div>
                    <i
                      v-if="!isCameraOpen"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-if="isCameraOpen"
                      class="base-icon arrows-right-able"
                      @click="enableCamera(false)"
                    />
                  </div>
                </div>
                <el-tooltip
                  v-if="!isMobile"
                  effect="dark"
                  :content="detection.camera.available === null
                    ? detection.camera.tip.detecting
                    : (cameras.length === 0
                      ? detection.camera.tip.nodevice
                      : ( detection.camera.available ? detection.camera.tip.available : detection.camera.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.camera.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.camera.available, 'warning-icon': detection.camera.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div
                v-if="!isMobile"
                class="section-item"
              >
                <div class="sub-item">
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('摄像头设备') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="cameras.length > 1 && detection.camera.currentDevice !== 0 && detection.camera.available"
                      class="base-icon arrows-left-able"
                      @click="onCameraChange('left')"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-disable"
                    />
                    <div
                      class="info"
                      :class="[isCameraOpen && detection.camera.available ? '' : 'disabled']"
                    >
                      <template v-if="camera">
                        {{ camera.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-if="!camera && detection.camera.available !== null">
                        {{ $t(detection.camera.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-if="cameras.length > 1 && detection.camera.currentDevice < cameras.length -1 && detection.camera.available"
                      class="base-icon arrows-right-able"
                      @click="onCameraChange('right')"
                    />
                    <i
                      v-else
                      class="base-icon arrows-right-disable"
                    />
                  </div>
                </div>
                <div
                  v-if="!isMobile"
                  class="base-icon"
                />
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('镜像模式') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="mirror || !isCameraOpen"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-able"
                      @click="changeMirrorStatus"
                    />
                    <div
                      class="info"
                      :class="[ !detection.camera.available ? 'info-disable' : '' ,isCameraOpen ? '' : 'disabled']"
                    >
                      {{ mirror ? $t('打开') : $t('关闭') }}
                    </div>

                    <i
                      v-if="mirror && detection.camera.available && isCameraOpen"
                      class="base-icon arrows-right-able"
                      @click="changeMirrorStatus"
                    />
                    <i
                      v-else
                      class="base-icon arrows-right-disable"
                    />
                  </div>
                </div>
                <div
                  v-if="!isMobile"
                  class="base-icon"
                />
              </div>
            </section>
            <!--  摄像头/end  -->
            <!--  麦克风  -->
            <section
              v-if="detectDevices.includes('microphone')"
              class="section"
            >
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <i
                    v-if="isMicOpen"
                    class="icon-outline device-icon"
                  />
                  <i
                    v-else
                    class="icon-outline-disabled device-icon"
                  />
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('麦克风') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="isMicOpen"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-able"
                      @click="enableMic(true)"
                    />
                    <div
                      class="info"
                      :class="[ !detection.camera.available ? 'info-disable' : '']"
                    >
                      {{ isMicOpen ? $t('打开') : $t('关闭') }}
                    </div>
                    <i
                      v-if="!isMicOpen"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-if="isMicOpen"
                      class="base-icon arrows-right-able"
                      @click="enableMic(false)"
                    />
                  </div>
                </div>
                <el-tooltip
                  v-if="!isMobile"
                  effect="dark"
                  :content="detection.microphone.available === null
                    ? detection.microphone.tip.detecting
                    : (microphones.length === 0
                      ? detection.microphone.tip.nodevice
                      : ( detection.microphone.available ? detection.microphone.tip.available : detection.microphone.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.microphone.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.microphone.available, 'warning-icon': detection.microphone.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div
                v-if="!isMobile"
                class="section-item"
              >
                <div class="sub-item">
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('麦克风设备') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="microphones.length <= 1 || detection.microphone.currentDevice === 0 || !isMicOpen"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-show="microphones.length > 1 && detection.microphone.currentDevice !== 0 && isMicOpen"
                      class="base-icon arrows-left-able"
                      @click="onMicrophoneChange('left')"
                    />
                    <div
                      class="info"
                      :class="isMicOpen && detection.microphone.available ? '' : 'disabled'"
                    >
                      <template v-if="microphone">
                        {{ microphone.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-if="!microphone && detection.microphone.available !== null">
                        {{ $t(detection.microphone.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-show="microphones.length <= 1 || detection.microphone.currentDevice === microphones.length - 1 || !isMicOpen"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-show="microphones.length > 1 && detection.microphone.currentDevice < microphones.length -1 && isMicOpen"
                      class="base-icon arrows-right-able"
                      @click="onMicrophoneChange('right')"
                    />
                  </div>
                </div>
                <div
                  v-if="!isMobile"
                  class="base-icon"
                />
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('音量') }}
                  </div>
                  <div class="volume">
                    <div class="mic-icon" />
                    <ul class="capacity">
                      <li
                        v-for="item in 12"
                        :key="item"
                        class="item"
                        :class="{active: item < detection.microphone.volumeLevel }"
                      />
                    </ul>
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="detection.microphone.volume <= 0 || !isMicOpen"
                      class="base-icon icon-sub-disable"
                    />
                    <i
                      v-show="detection.microphone.volume > 5 && isMicOpen"
                      class="base-icon icon-sub-able"
                      @click="onMicrophoneVolumeChange('sub')"
                    />
                    <div
                      class="info volume-info"
                      :class="isMicOpen && detection.microphone.available ? '' : 'disabled'"
                    >
                      {{ detection.microphone.volume }}%
                    </div>
                    <i
                      v-if="detection.microphone.volume < 100 && isMicOpen && detection.microphone.available"
                      class="base-icon icon-add-able"
                      @click="onMicrophoneVolumeChange('add')"
                    />
                    <i
                      v-else
                      class="base-icon icon-add-disable"
                    />
                  </div>
                </div>
                <div
                  v-if="!isMobile"
                  class="base-icon"
                />
              </div>
              <p class="sub-text">
                {{ $t('对着麦克风说话可以看到波动效果') }}
              </p>
            </section>
            <!--  麦克风/end  -->
            <!--  扬声器  -->
            <section
              v-if="detectDevices.includes('speaker')"
              class="section"
            >
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <i
                    v-if="isMobile || (detection.speaker.available && detection.speaker.volume > 0)"
                    class="icon-volume device-icon"
                  />
                  <i
                    v-else
                    class="icon-volume-disabled device-icon"
                  />
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('扬声器') }}
                  </div>
                  <div
                    v-if="!isMobile"
                    class="decice-info"
                  >
                    <i
                      v-if="speakers.length > 1 && detection.speaker.currentDevice !== 0 && detection.speaker.available"
                      class="base-icon arrows-left-able"
                      @click="onSpeakerChange('left')"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-disable"
                    />
                    <div
                      class="info"
                      :class="detection.speaker.available ? '' : 'disabled'"
                    >
                      <template v-if="speaker">
                        {{ speaker.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-else>
                        {{ $t(detection.speaker.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-show="speakers.length <= 1 || detection.speaker.currentDevice === speakers.length - 1 || !detection.speaker.available"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-show="speakers.length > 1 && detection.speaker.currentDevice < speakers.length -1 && detection.speaker.available"
                      class="base-icon arrows-right-able"
                      @click="onSpeakerChange('right')"
                    />
                  </div>
                </div>
                <el-tooltip
                  v-if="!isMobile"
                  effect="dark"
                  :content="detection.speaker.available === null
                    ? detection.speaker.tip.detecting
                    : (speakers.length === 0
                      ? detection.speaker.tip.nodevice
                      : ( detection.speaker.available ? detection.speaker.tip.available : detection.speaker.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.speaker.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.speaker.available, 'warning-icon': detection.speaker.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div
                    class="label"
                    :style="{'width': 'auto'}"
                  >
                    {{ $t('音量') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="detection.speaker.volume > 0"
                      class="base-icon icon-sub-able"
                      @click="onSpeakerVolumeChange('sub')"
                    />
                    <i
                      v-else
                      class="base-icon icon-sub-disable"
                    />
                    <div
                      class="info volume-info"
                      :class="detection.speaker.available ? '' : 'disabled'"
                    >
                      {{ detection.speaker.volume }}%
                    </div>
                    <i
                      v-if="detection.speaker.volume < 100"
                      class="base-icon icon-add-able"
                      @click="onSpeakerVolumeChange('add')"
                    />
                    <i
                      v-else
                      class="base-icon icon-add-disable"
                    />
                  </div>
                </div>
                <div
                  v-if="!isMobile"
                  class="base-icon"
                />
              </div>
              <p
                v-if="!isMobile"
                class="sub-section pt10"
              >
                <el-button
                  class="audition-button"
                  :style="{'width': 'auto'}"
                  @click="toggleAudioPlay"
                >
                  <span>
                    {{ audioPlayStatus ? $t('暂停') : $t('试听') }}
                  </span>
                </el-button>
              </p>
            </section>
            <!--  扬声器/end  -->
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
/*
*  Web 与 Electron 的检测流程有所不同
*  Electron的检测可以通过 TCIC.TTrtcEvent.Status_Update 事件通知知道流是否正常
*  Web通过接口调用是否报错保证
* */
import i18next from 'i18next';
import Constant from '@util/Constant';
import BaseComponent from '@core/BaseComponent';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import Util from '@util/Util';
import Media from '@/util/Media';
import Lodash from 'lodash';

const detectVolumeInterval = 200;

export default {
  name: 'CTSetting',
  filters: {
    formatClassStatus: (key) => {
      const map = {
        0: i18next.t('待开始'),
        1: i18next.t('进行中'),
        2: i18next.t('已结束'),
      };
      return map[key];
    },
  },
  components: {},
  extends: BaseComponent,

  data() {
    return {
      userId: TCIC.SDK.instance.getUserId(),
      isMobile: TCIC.SDK.instance.isMobile(),
      detectDevices: [],
      mirror: null,
      isElectron: false,
      microphones: [],
      microphone: null,
      speaker: null,
      speakers: [],
      camera: null,
      cameras: [],
      audioPlayStatus: false,
      cameraMode: 0,
      speakerTestMediaUrl: 'https://res.qcloudclass.com/assets/detect.mp3',
      speakerTestMediaDuration: 9000,
      speakerTestSto: null,
      suggestionMessage: null,
      detection: {
        microphone: {
          volume: 0,
          volumeLevel: 0,
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请说话测试麦克风音量大小'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('麦克风打开失败，请确认是否未被授权') : i18next.t('麦克风打开失败，请确认是否被其他应用程序占用'),
            nodevice: i18next.t('无可用麦克风'),
          },
          available: true,
          currentDevice: 0,
        },
        camera: {
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请确认是否看到摄像头画面'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('摄像头打开失败，请确认是否未被授权') : i18next.t('摄像头打开失败，请确认是否被其他应用程序占用'),
            nodevice: i18next.t('无可用摄像头'),
          },
          available: true,
          currentDevice: 0,
        },
        speaker: {
          volume: 0,
          tip: {
            detecting: i18next.t('检测中...'),
            available: i18next.t('请点击试听确认扬声器正常工作'),
            unavailable: TCIC.SDK.instance.isMac() ? i18next.t('扬声器打开失败，请确认是否未被授权') : i18next.t('扬声器打开失败，请确认是否被其他应用程序占用'),
            nodevice: i18next.t('无可用扬声器'),
          },
          available: null,
          currentDevice: 0,
        },
      },
      classData: null,
      callback: null,
      isTeacher: false,
      isCameraOpen: true,  // 摄像头是否打开
      isMicOpen: true, // 麦克风是否打开
      result: {
        browser: {
          available: false,
          reason: null,
        },
        camera: {
          available: false,
          reason: null,
        },
        microphone: {
          available: false,
          reason: null,
        },
        speaker: {
          available: false,
          reason: null,
        },
      },
      status: 'active', // 当前的设备检测状态
      roleInfo: null,
      roomInfo: null,
    };
  },

  computed: {
    isDetecting() {
      return !!this.detectDevices.find(item => this.detection[item].available === null);
    },
  },

  watch: {
    isCameraOpen(val) {
      this.$emit('on-button-update');
    },
    isMicOpen(val) {
      this.$emit('on-button-update');
    },
    'detection.camera.currentDevice'() {
      this.$emit('on-button-update');
    },
    'detection.microphone.currentDevice'() {
      this.$emit('on-button-update');
    },
    mirror(val) {
      localStorage.setItem('mirror', val);
      const { Enable, Disable } = TCIC.TTrtcVideoMirrorType;
      DetectUtil.setLocalVideoParams({ mirror: val ? Enable : Disable });
      this.$emit('on-button-update');
      return val;
    },
    audioPlayStatus(val) {
      clearTimeout(this.speakerTestSto);
      if (val) {
        this.detectSpeaker();
      } else {
        this.stopDetectSpeaker();
      }
    },
  },

  async created() {
  },
  async mounted() {
    try {
      const checkResult = await TCIC.SDK.instance.checkSystemRequirements();
      console.log('%c [ checkResult ]-636', 'font-size:13px; background:pink; color:#bf2c9f;', checkResult);
      if (!checkResult.result || !checkResult.detail.isMediaDevicesSupported) {
        let msg = i18next.t('您的运行环境不支持webRTC,请升级系统至最新版本');
        if (TCIC.SDK.instance.isIOS()) {
          msg += `,${i18next.t('ios请升级系统至ios14.3及以上版本以获得完整支持')}`;
        }
        TCIC.SDK.instance.showErrorMsgBox({
          message: msg,
        });
        // TODO: 退出课堂?
      }
    } catch (error) {
      console.error('%c [ error ]-643', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }

    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    console.debug(' device-detect mounted >>>>> mirror =>', this.mirror);
    const devices = ['screen-capture', 'browser', 'microphone', 'camera', 'speaker'];
    this.start({
      flag: true,
      devices,
    });
    const resizeObserver = new ResizeObserver((entries) => {
      this.onBoundingClientRectChange(entries[0].contentRect);
    });
    resizeObserver.observe(this.$el);
  },

  methods: {
    // 将设备检测中的值同步至setting组件中
    async init() {
      this.detection.microphone.volume = TCIC.SDK.instance.getState(Constant.TStateCoMicVolume);
      this.detection.speaker.volume = await DetectUtil.getSpeakerVolume();
      this.mirror = localStorage.getItem('mirror') === 'true';
    },
    async reset() {
      this.status = 'reset';
      await this.stopDetectVolume();
      await this.stopDetectSpeaker();
    },
    async stop() {
      this.callback = null;
      await this.reset();
      this.doHide();
      if (this.suggestionMessage) {
        this.suggestionMessage.close();
      }
    },
    start(action) {
      if (!action.devices || action.devices.length === 0) {
        console.error('params error', action);
        return;
      }
      if (action.callback) {
        this.callback = action.callback;
      }
      if ((TCIC.SDK.instance.isWeb() && DetectUtil.isSafari()) || TCIC.SDK.instance.isMobile() || TCIC.SDK.instance.isPad()) {
        action.devices = action.devices.filter(item => item !== 'speaker');
      }
      // 初始化事件监听
      this.initListener();
      // 获取要检测的设备
      this.detectDevices = action.devices;
      // 开始检测
      this.startDetect(action.devices);

      this.show();
    },
    onBoundingClientRectChange(rect) {
      if (rect.width > 0 && rect.height > 0 && !TCIC.SDK.instance.isMobile()) {
        this.updateCurrentCamera();
        this.updateCurrentMicrophone();
        this.updateCurrentSpeaker();
      }
    },

    deviceChangeListener(result) {
      const { deviceId, type, state } = result;
      console.debug('TCIC.TTrtcEvent.Device_Changed', result);
      this.deviceChangeHandler({ deviceId, type, state });
    },

    volumeUpdateListener: Lodash.debounce(function (result) {
      // console.debug('>>>>>>>>>>. volume =>', result.userId, result.volume)
      if (this.detection.microphone.volume) {
        this.detection.microphone.volumeLevel = Media.amplifier(result.volume, 13);
      } else {
        this.detection.microphone.volumeLevel = 0;
      }
    }, 50),

    deviceStatusUpdateListener(result) {
      const { state, device } = result;
      let detection = null;
      switch (device) {
        case 'mic':
          detection = this.detection.microphone;
          break;
        case 'camera':
          detection = this.detection.camera;
          break;
        default:
          break;
      }
      if (!detection) {
        console.error('Status_Update => ', result);
        return;
      }
      switch (state) {
        case -1001: // 打开失败
        case -1002: // 找不到设备
        case -1003: // 未授权
        case -1004: // 设备被占用
        case 2: // 已关闭
          detection.available = false;
          break;
        case 1: // 已打开
          if (device === 'mic' && this.microphones.length > 0) {
            detection.available = true;
          } else if (device === 'camera' && this.cameras.length > 0) {
            detection.available = true;
          }
          break;
        default:
          break;
      }
      // 收到麦克风的通知，不处理重试逻辑。
      if (device === 'mic') {
        this.unbindRetryMicrophoneTest();
      }
    },

    initListener() {
      // 设备断通事件
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Device_Changed, this.deviceChangeListener);
      // 音量检测
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, (result) => {
        // 先过滤id，避免因防抖导致音量不展示
        if (result && result.userId === TCIC.SDK.instance.getUserId()) {
          this.volumeUpdateListener(result);
        }
      });
      // 监听视频设备状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Video_Publish, (flag) => {
        this.isCameraOpen = flag;
        // this.updateCtrlStatus();
      });
      // 监听麦克风设备状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Publish, (flag) => {
        this.isMicOpen = flag;
      });
    },

    doHide() {
      this.modal = false;
      // 显示导航条
      this.hide();
    },

    startDetect(objects) {
      objects.forEach((item) => {
        setTimeout(() => {
          this.detect(item);
        }, 0);
      });
    },

    async detect(object) {
      switch (object) {
        case 'microphone':
          await this.enumerateMicrophone(true);
          break;
        case 'speaker':
          await this.enumerateSpeaker(true);
          break;
        case 'camera':
          await this.enumerateCamera(true);
          break;
      }
    },

    /**
     * 检测麦克风
     */
    async enumerateMicrophone(init = false) {
      const { microphone } = this.detection;
      try {
        this.microphones = await DetectUtil.getMicrophones();
        if (this.microphones.length === 0) {
          microphone.available = false;
          return;
        }
        microphone.available = true;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [succ] , ${JSON.stringify(this.microphones)}`,
        );
      } catch (error) {
        // 获取默认设备不阻塞其他流程
        this.microphones = [];
        this.microphone = null;
        microphone.available = false;
        console.error(error);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      if (Util.isArray(this.microphones) && this.microphones.length) {
        try {
          const deviceId = await DetectUtil.getMicDeviceId();
          if (!this.microphone) {
            this.microphone = this.microphones.find(item => item.deviceId === deviceId) || this.microphones[0];
          } else {
            const currentIndex = (this.microphones || []).findIndex(item => item.deviceId === this.microphone.deviceId);
            microphone.currentDevice = currentIndex;
            this.microphone = this.microphones[currentIndex];
          }
        } catch (e) {
          if (!this.microphone) {
            this.microphone = this.microphones[0];
          }
        }
      }
    },

    /**
     * 检测摄像头
     */
    async enumerateCamera(init = false) {
      const { camera } = this.detection;
      try {
        this.cameras = await DetectUtil.getCameras();
        if (this.cameras.length === 0) {
          camera.available = false;
          return;
        }
        camera.available = true;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [succ] , ${JSON.stringify(this.cameras)}`,
        );
      } catch (error) {
        // 获取默认设备不阻塞其他流程
        this.cameras = [];
        this.camera = null;
        camera.available = false;
        console.error(error);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      if (Util.isArray(this.cameras) && this.cameras.length) {
        try {
          const deviceId = await DetectUtil.getCameraDeviceId();
          if (!this.camera) {
            this.camera = this.cameras.find(item => item.deviceId === deviceId) || this.cameras[0];
            if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          } else {
            const currentIndex = (this.cameras || []).findIndex(item => item.deviceId === this.camera.deviceId);
            camera.currentDevice = currentIndex;
            this.camera = this.cameras[currentIndex];
          }
        } catch (e) {
          if (!this.camera) {
            this.camera = this.cameras[0];
            if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          }
        }
      }
    },

    /**
     * 切换镜像模式
     */
    changeMirrorStatus() {
      this.mirror = !this.mirror;
    },

    /**
     * 检测扬声器
     */
    async enumerateSpeaker(init = false) {
      const { speaker } = this.detection;
      speaker.available = null;
      try {
        const devices = await DetectUtil.getSpeakers();
        if (Util.isArray(devices) && devices.length) {
          this.speakers = devices;
          if (!this.speaker) {
            DetectUtil.getSpeakerDeviceId().then((deviceId) => {
              this.speaker = devices.find(item => item.deviceId === deviceId) || devices[0];
            });
          } else {
            const currentIndex = (this.speakers || []).findIndex(item => item.deviceId === this.speaker.deviceId);
            speaker.currentDevice = currentIndex;
            this.speaker = this.speakers[currentIndex];
          }
          TCIC.SDK.instance.reportLog(
            'device-detect',
            `[enumerateCamera] [succ] , ${JSON.stringify(devices)}`,
          );
          speaker.available = true;
          this.result.speaker = {
            available: true,
            reason: null,
          };
          try {
            speaker.volume = await DetectUtil.getSpeakerVolume();
          } catch (error) {
            console.error('getSpeakerVolume', error);
            this.$message.error(i18next.t('获取扬声器音频失败，请重试'));
          }
        } else {
          speaker.available = false;
          this.result.speaker = {
            available: false,
            reason: null,
          };
        }
      } catch (error) {
        speaker.available = false;
        this.result.speaker = {
          available: false,
          reason: `${error.name || ''} : ${error.message || ''}` || error,
        };
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateSpeaker] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
    },

    async updateCurrentCamera() {   // 更新当前摄像头设备
      const { camera } = this.detection;
      const deviceId = await DetectUtil.getCameraDeviceId();
      const currentIndex = (this.cameras || []).findIndex(item => item.deviceId === deviceId);
      camera.currentDevice = currentIndex;
      this.camera = this.cameras[currentIndex];
    },
    async updateCurrentMicrophone() {   // 更新当前麦克风设备
      const { microphone } = this.detection;
      const deviceId = await DetectUtil.getMicDeviceId();
      const currentIndex = (this.microphones || []).findIndex(item => item.deviceId === deviceId);
      microphone.currentDevice = currentIndex;
      this.microphone = this.microphones[currentIndex];
    },
    async updateCurrentSpeaker() {   // 更新当前扬声器设备
      const { speaker } = this.detection;
      const deviceId = await DetectUtil.getSpeakerDeviceId();
      const currentIndex = (this.speakers || []).findIndex(item => item.deviceId === deviceId);
      speaker.currentDevice = currentIndex;
      this.speaker = this.speakers[currentIndex];
    },
    /**
     * @description 检测音量
     */
    async detectVolume() {
      if (this.status === 'reset') {
        return;
      }
      const { microphone } = this.detection;
      try {
        microphone.volume = TCIC.SDK.instance.getState(Constant.TStateCoMicVolume);
      } catch (e) {
        console.error('detectVolume', e);
        this.$message.error(i18next.t('获取麦克风音频失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[detectVolume] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      return DetectUtil.enableVolumeEvaluation(detectVolumeInterval);
    },

    /**
     * @description 停止检测音量
     */
    stopDetectVolume() {
      // 降低间隔，用于展示音量
      return DetectUtil.enableVolumeEvaluation(500);
    },

    /**
     * @description 开始检测扬声器
     */
    async detectSpeaker() {
      try {
        await DetectUtil.stopSpeakerTest();
        await DetectUtil.startSpeakerTest(this.speakerTestMediaUrl);
        this.speakerTestSto = setTimeout(() => {
          this.audioPlayStatus = false;
        }, this.speakerTestMediaDuration);
      } catch (e) {
        console.error('detectSpeaker', e);
        this.$message.error(i18next.t('检测扬声器失败，请检查设备并重试'));
        this.audioPlayStatus = false;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[detectSpeaker] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },

    /**
     * @description 停止检测扬声器
     */
    stopDetectSpeaker() {
      try {
        DetectUtil.stopSpeakerTest();
      } catch (e) {
        console.error('stopDetectSpeaker', e);
      }
    },

    /**
     * @description 检测麦克风的audio播放/暂停切换
     */
    toggleAudioPlay() {
      this.audioPlayStatus = !this.audioPlayStatus;
    },

    /**
     * 扬声器变化
     */
    async onSpeakerChange(type) {
      const currentIndex = (this.speakers || []).findIndex(item => item.deviceId === this.speaker.deviceId);
      let changeIndex;
      let device;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;
        ;
      }
      device = this.speakers[changeIndex];
      const { speaker } = this.detection;
      speaker.available = null;
      console.log('>>>>>>>>>>>>>>  device speaker', device);
      device = device || this.speaker;
      try {
        if (this.audioPlayStatus) {
          this.toggleAudioPlay();
        }
        await this.stopDetectSpeaker();
        DetectUtil.switchSpeaker(device.deviceId);
        // this.$message.success('切换成功');
        speaker.available = true;
        speaker.currentDevice = changeIndex;
        this.speaker = device;
      } catch (e) {
        console.error('switch speaker error', e);
        this.$message.error(i18next.t('切换扬声器失败，请重试'));
        speaker.available = false;
        // 停止测试
        this.stopDetectSpeaker();
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onSpeakerChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },

    /**
     * 麦克风
     */
    async onMicrophoneChange(type) {
      const currentIndex = (this.microphones || []).findIndex(item => item.deviceId === this.microphone.deviceId);
      let changeIndex;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;
        ;
      }
      const device = this.microphones[changeIndex];
      const { microphone } = this.detection;
      const detectDevice = device || this.microphone;
      microphone.available = null;
      microphone.currentDevice = changeIndex;
      this.microphone = device;
      console.log('>>>>>>>>>>>>>>  onMicrophoneChange ', detectDevice);
      try {
        // Electron必须先切换设备，再调用检测接口，否则流不会变。
        DetectUtil.switchMic(detectDevice.deviceId);
        microphone.available = true;
      } catch (e) {
        console.error('switch microphone error', e);
        this.$message.error(i18next.t('切换麦克风失败，请重试'));
        microphone.available = false;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onMicrophoneChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },

    /**
     * 麦克风音量调整
     */
    async onMicrophoneVolumeChange(type) {
      const step = 5;
      let value;
      // 减音量
      if (type === 'sub' && this.detection.microphone.volume > 0) {
        value = this.detection.microphone.volume - step;
      } else if (type === 'add' && this.detection.microphone.volume < 100) { // 加音量
        value = this.detection.microphone.volume + step;
      } else {
        return ;
      }
      console.log('>>>>>>>>>>>>>>  set microphone volume', value);
      try {
        await DetectUtil.setMicrophoneVolume(value);
        TCIC.SDK.instance.setState(Constant.TStateCoMicVolume, value);
        this.detection.microphone.volume = value;
        this.$emit('on-button-update');
      } catch (e) {
        console.error('set microphone volume error', e);
        this.$message.error(i18next.t('调整麦克风音量失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onMicrophoneVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },

    /**
     * 扬声器音量调整
     */
    async onSpeakerVolumeChange(type) {
      const step = 5;
      let value;
      // 减音量
      if (type === 'sub' && this.detection.speaker.volume > 0) {
        value = this.detection.speaker.volume - step;
      } else if (type === 'add' && this.detection.speaker.volume < 100) { // 加音量
        value = this.detection.speaker.volume + step;
      } else {
        return ;
      }
      console.log('>>>>>>>>>>>>>>  set microphone volume', value);
      try {
        await DetectUtil.setSpeakerVolume(value);
        this.detection.speaker.volume = value;
        this.$emit('on-button-update');
      } catch (e) {
        console.error('set microphone volume error', e);
        this.$message.error(i18next.t('调整扬声器音量失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onSpeakerVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
    },

    /**
     * 切换摄像头开关
     */
    async enableCamera(flag) {
      console.log('VideoComponent::enableCamera=>', this.userId, flag);
      const userId = TCIC.SDK.instance.getUserId();
      const myPermission = TCIC.SDK.instance.getPermission(userId);
      if (!TCIC.SDK.instance.isTeacherOrAssistant() && !myPermission.camera) {
        // 学生如果没有权限，则直接返回
        window.showToast(i18next.t('{{arg_0}}关闭了你的摄像头权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher }), 'error');
        return;
      }
      this.isCameraOpen = flag;
      const videoWrap = document.getElementById(`video-${this.userId}`);
      if (flag) {
        if (this.isScreenShareOpen
            && this.isCollegeClass
            && TCIC.SDK.instance.isWeb()) {
          window.showToast(i18next.t('你正在进行屏幕共享，暂时无法开启摄像头'));
          return;
        }
        try {
          // if (TCIC.SDK.instance.isWeb()) {
          //   // 避免开启摄像头后再开启音频导致视频闪一下的问题
          //   const isAudioCapture = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
          //   if (flag && !isAudioCapture) {
          //     TCIC.SDK.instance.reportLog('setMicVolume', `[CTSettingComponent] enableCamera ${flag} on web, volume 0`);
          //     await TCIC.SDK.instance.setMicVolume(0);
          //     TCIC.SDK.instance.reportLog('startLocalAudio', `[CTSettingComponent] enableCamera ${flag} on web`);
          //     await TCIC.SDK.instance.startLocalAudio(this.$refs.audio);
          //   }
          // }

          if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
            if (!myPermission.camera) {
              await TCIC.SDK.instance.memberAction({
                userId,
                actionType: TCIC.TMemberActionType.Camera_Open,
              });
            }
          }
          TCIC.SDK.instance.reportLog('startLocalVideo', `[CTSettingComponent] enableCamera ${flag}`);
          await TCIC.SDK.instance.startLocalVideo(videoWrap);
          this.detection.camera.available = true;
          return Promise.resolve();
        } catch (err) {
          this.detection.camera.available = false;
          if (err && err.errorMsg) {
            window.showToast(`${err.errorMsg || err.errorDetail}`, 'error');
          } else {
            window.showToast(i18next.t('打开本地视频采集遇到一些问题'), 'error');
          }
          return Promise.reject(err);
        }
      } else {
        try {
          TCIC.SDK.instance.reportLog('stopLocalVideo', `[CTSettingComponent] enableCamera ${flag}`);
          await TCIC.SDK.instance.stopLocalVideo();
          if (this.isTeacherVideo) {
            this.destroyWaterMark();
          }
          return Promise.resolve();
        } catch (err) {
          this.detection.camera.available = false;
          if (err && err.errorDetail) {
            window.showToast(`${err.errorDetail}`, 'error');
          } else {
            window.showToast(i18next.t('关闭本地视频采集遇到一些问题'), 'error');
          }
          TCIC.SDK.instance.reportLog(
            'device-detect',
            `[enableCamera] [error] name: ${err?.name}, message: ${err?.message}`,
          );
          return Promise.reject(err);
        }
      }
    },

    /**
     * 切换麦克风开关
     */
    async enableMic(flag) {
      console.log('VideoComponent::enableMic=>', this.userId, flag);
      if (!TCIC.SDK.instance.isTeacherOrAssistant() && TCIC.SDK.instance.isCoTeachingClass()) {  // 双师课学生端打开麦克风前要先申请资源
        if (flag) {
          try {
            const userId = TCIC.SDK.instance.getUserId();
            const myPermission = TCIC.SDK.instance.getPermission(userId);
            // 学生如果没有权限，则直接返回
            if (!myPermission.mic) {
              window.showToast(i18next.t('{{arg_0}}关闭了你的麦克风权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher }), 'error');
              return;
            }
            await TCIC.SDK.instance.requireResource('mic');
          } catch (err) {
            window.showToast(i18next.t(
              '{{arg_0}}内打开麦克风数量超过限制，出于性能考虑，请您先联系{{arg_1}}，让其关闭部分{{arg_2}}的麦克风',
              { arg_0: this.roomInfo.name, arg_1: this.roleInfo.teacher, arg_2: this.roleInfo.student },
            ), 'error');
            return;
          }
        } else {
          await TCIC.SDK.instance.releaseResource('mic');
        }
      }
      TCIC.SDK.instance.reportLog('setMicVolume', `[CTSettingComponent] enableMic ${flag}, volume ${flag ? this.detection.microphone.volume : 0}`);
      await TCIC.SDK.instance.setMicVolume(flag ? this.detection.microphone.volume : 0);
      const audio = document.getElementById(`audio-${this.userId}`);
      if (flag && !TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture)) {
        return new Promise(async (resolve, reject) => {
          try {
            if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
              const userId = TCIC.SDK.instance.getUserId();
              const myPermission = TCIC.SDK.instance.getPermission(userId);
              if (!myPermission.mic) {
                await TCIC.SDK.instance.memberAction({
                  userId,
                  actionType: TCIC.TMemberActionType.Mic_Open,
                });
              }
            }
            TCIC.SDK.instance.reportLog('startLocalAudio', `[CTSettingComponent] enableMic ${flag}`);
            await TCIC.SDK.instance.startLocalAudio(audio);
            this.isMicOpen = flag;
            this.detection.microphone.available = true;
            resolve();
          } catch (err) {
            this.detection.microphone.available = false;
            console.error('startLocalAudio error => ', err);
            if (err && err.errorMsg) {
              window.showToast(`${err.errorMsg || err.errorDetail}`, 'error');
            } else {
              window.showToast(i18next.t('打开本地音频采集遇到一些问题'), 'error');
            }
            if (!TCIC.SDK.instance.isTeacher() && TCIC.SDK.instance.isCoTeachingClass()) {
              TCIC.SDK.instance.releaseResource('mic');
            }
            reject(err); // 按需启动本地音频采集
          }
        });
      }
      return Promise.resolve();
    },

    /**
     * 摄像头变化
     */
    async onCameraChange(type) {
      const currentIndex = (this.cameras || []).findIndex(item => item.deviceId === this.camera.deviceId);
      let changeIndex;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;
        ;
      }
      const device = this.cameras[changeIndex];
      const { camera } = this.detection;
      const detectDevice = device || this.camera;
      camera.available = null;
      camera.currentDevice = changeIndex;
      this.camera = device;
      console.log('>>>>>>>>>>>>>>  onCameraChange ', detectDevice);
      try {
        await DetectUtil.switchCamera(detectDevice.deviceId);
        camera.available = true;
      } catch (e) {
        console.error('switch camera error', e);
        camera.available = false;
        this.$message.error(i18next.t('切换摄像头失败，请重试'));
      }
    },


    enter() {
      if (this.callback) {
        this.callback(this.result);
      }
      // TCIC.SDK.instance.notify('device-detect-completed', {});    // 设备检测完成
      this.stop();

      // 设备检测完成后取消静音
      TCIC.SDK.instance.muteAllRemoteAudio(false);
      this.$destroy();
      const enterTime = new Date().getTime();
      TCIC.SDK.instance.reportEvent('handle_enter_class', {}, 0, enterTime);
    },

    deviceChangeHandler({ deviceId, type, state }) {
      const {
        Add,
        Remove,
        Active,
      } = TCIC.TTrtcDeviceState;
      switch (state) {
        case Add:
          this.deviceAddHandler({ deviceId, type });
          break;
        case Remove:
          this.deviceRemoveHandler({ deviceId, type });
          break;
        case Active:
          this.deviceActiveHandler({ deviceId, type });
          break;
      }
    },

    /*
    * 设备新增
    * 如果当前没有此类型设备：
    *   1.设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceAddHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          await this.enumerateMicrophone();
          break;
        case Camera:
          await this.enumerateCamera();
          break;
        case Speaker:
          // 如果当前有设备，只重新枚举设备
          await this.enumerateSpeaker();
          break;
      }
    },
    /*
    * 设备可用
    * 如果是当前设备：
    *   1.设备检测
    * 如果不是当前设备：
    *   do nothing
    * */
    async deviceActiveHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          // 如果当前设备切换成可用，重新检测
          if (this.microphone.deviceId === deviceId) {
            await this.detect('microphone');
          }
          break;
        case Camera:
          if (this.camera.deviceId === deviceId) {
            await this.detect('camera');
          }
          break;
        case Speaker:
          if (this.speaker.deviceId === deviceId) {
            await this.detect('speaker');
          }
          break;
      }
    },
    /*
    * 设备移除
    * 如果是当前设备：
    *   1.先复位当前选中设备 2.重新设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceRemoveHandler({ deviceId, type }) {
      // const { camera, microphone, speaker } = this.detection;
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          console.debug('deviceChange | deviceRemoveHandler | mic => ', this.microphone.deviceId, deviceId);
          if (this.microphone.deviceId === deviceId) {
            this.microphone = null;
          }
          await this.enumerateMicrophone();
          break;
        case Camera:
          console.debug('deviceChange | deviceRemoveHandler | camera => ', this.camera.deviceId, deviceId);
          if (this.camera.deviceId === deviceId) {
            this.camera = null;
          }
          await this.enumerateCamera();
          break;
        case Speaker:
          console.debug('deviceChange | deviceRemoveHandler | speaker => ', this.speaker.deviceId, deviceId);
          if (this.speaker.deviceId === deviceId) {
            this.speaker = null;
          }
          if (this.audioPlayStatus) {
            this.toggleAudioPlay();
          }
          await this.enumerateSpeaker();
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "./theme/co-teaching.less";
</style>
