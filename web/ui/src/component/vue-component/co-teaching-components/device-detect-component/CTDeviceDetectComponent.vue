<template>
  <div class="device-detect-wrapper ct-device-detect-wrapper">
    <div
      v-if="modal"
      class="detect-enter"
    >
      <div class="detect-item-camera">
        <div
          ref="camera"
          class="detect-preview__video"
        />
      </div>
      <div class="detect-item-microphone">
        <div
          v-show="false"
          ref="microphone"
        />
      </div>
      <div class="setting">
        <el-row :gutter="20">
          <el-col :span="24">
            <section class="section">
              <div class="main-text title-text">
                {{ $t('设备检测') }}
              </div>
              <!-- <a
                class="link-primary"
                href="javascript:"
                @click="getHelp('device-detect')"
              ><i
                class="question-icon ml10 mr10"
              />{{ $t('获取帮助') }}</a> -->
            </section>
            <section
              v-if="detectDevices.includes('screen-capture')"
              class="section"
            >
              <div class="section-item">
                <div class="sub-item">
                  <div class="label">
                    {{ $t('屏幕共享') }}
                  </div>
                  <template v-if="detection['screen-capture'].available === null">
                    <p class="pt10 main-text">
                      {{ detection['screen-capture'].tip.detecting }}
                    </p>
                  </template>
                  <template v-else-if="detection['screen-capture'].available">
                    <p class="pt10 main-text">
                      {{ detection['screen-capture'].tip.available }}
                    </p>
                  </template>
                  <template v-else>
                    <p class="pt10 pb10 main-text">
                      {{ detection['screen-capture'].tip.unavailable }}
                    </p>
                    <el-button
                      class="plain"
                      plain
                      @click="showScreenCaptureGuide"
                    >
                      {{ $t('开始配置屏幕共享权限') }}
                    </el-button>
                  </template>
                </div>
                <template v-if="detection['screen-capture'].available">
                  <i class="base-icon success-icon" />
                </template>
                <template v-else>
                  <i class="base-icon warning-icon" />
                </template>
              </div>
            </section>
            <section
              v-if="detectDevices.includes('browser')"
              class="section"
            >
              <p class="sub-title">
                {{ $t('浏览器') }}
              </p>
              <div class="section-item">
                <div class="sub-item">
                  <div class="label">
                    {{ $t('浏览器') }}
                  </div>
                  <template v-if="detection.browser.available === null">
                    <p class="pt10 pb10">
                      {{ detection.browser.tip.detecting }}
                    </p>
                  </template>
                  <template v-else-if="detection.browser.available">
                    <p class="pt10 pb10">
                      {{ detection.browser.tip.available }}
                    </p>
                  </template>
                  <template v-else>
                    <p class="pt10 pb10">
                      {{ detection.browser.tip.unavailable }}
                    </p>
                    <el-button
                      class="plain"
                      plain
                      @click="getHelp('download-browser')"
                    >
                      {{ $t('下载浏览器') }}
                    </el-button>
                  </template>
                </div>
                <template v-if="detection.browser.available">
                  <i class="base-icon success-icon" />
                </template>
                <template v-else>
                  <i class="base-icon warning-icon" />
                </template>
              </div>
            </section>
            <!--  摄像头  -->
            <section class="section">
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <div class="label">
                    {{ $t('摄像头') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="cameras.length <= 1 || detection.camera.currentDevice === 0"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-show="cameras.length > 1 && detection.camera.currentDevice !== 0"
                      class="base-icon arrows-left-abled"
                      @click="onCameraChange('left')"
                    />
                    <div class="info">
                      <template v-if="camera">
                        {{ camera.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-if="!camera && detection.camera.available !== null">
                        {{ $t(detection.camera.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-show="cameras.length <= 1 || detection.camera.currentDevice === cameras.length - 1"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-show="cameras.length > 1 && detection.camera.currentDevice < cameras.length -1"
                      class="base-icon arrows-right-abled"
                      @click="onCameraChange('right')"
                    />
                  </div>
                </div>
                <el-tooltip
                  effect="dark"
                  :content="detection.camera.available === null
                    ? detection.camera.tip.detecting
                    : (cameras.length === 0
                      ? detection.camera.tip.nodevice
                      : ( detection.camera.available ? detection.camera.tip.available : detection.camera.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.camera.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.camera.available, 'warning-icon': detection.camera.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div class="label">
                    {{ $t('镜像模式') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-if="mirror"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-else
                      class="base-icon arrows-left-abled"
                      @click="changeMirrorStatus"
                    />
                    <div
                      class="info"
                      :class="[ !detection.camera.available ? 'info-disable' : '']"
                    >
                      {{ mirror ? $t('打开') : $t('关闭') }}
                    </div>
                    <i
                      v-if="!mirror"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-if="mirror && detection.camera.available"
                      class="base-icon arrows-right-abled"
                      @click="changeMirrorStatus"
                    />
                  </div>
                </div>
                <div class="base-icon" />
              </div>
            </section>
            <!--  摄像头/end  -->
            <!--  麦克风  -->
            <section
              v-if="detectDevices.includes('microphone')"
              class="section"
            >
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <div class="label">
                    {{ $t('麦克风') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="microphones.length <= 1 || detection.microphone.currentDevice === 0"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-show="microphones.length > 1 && detection.microphone.currentDevice !== 0"
                      class="base-icon arrows-left-abled"
                      @click="onMicrophoneChange('left')"
                    />
                    <div class="info">
                      <template v-if="microphone">
                        {{ microphone.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-if="!microphone && detection.microphone.available !== null">
                        {{ $t(detection.microphone.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-show="microphones.length <= 1 || detection.microphone.currentDevice === microphones.length - 1"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-show="microphones.length > 1 && detection.microphone.currentDevice < microphones.length -1"
                      class="base-icon arrows-right-abled"
                      @click="onMicrophoneChange('right')"
                    />
                  </div>
                </div>
                <el-tooltip
                  effect="dark"
                  :content="detection.microphone.available === null
                    ? detection.microphone.tip.detecting
                    : (microphones.length === 0
                      ? detection.microphone.tip.nodevice
                      : ( detection.microphone.available ? detection.microphone.tip.available : detection.microphone.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.microphone.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.microphone.available, 'warning-icon': detection.microphone.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div class="label">
                    {{ $t('音量') }}
                  </div>
                  <div class="volume">
                    <div class="mic-icon" />
                    <ul class="capacity">
                      <li
                        v-for="item in 12"
                        :key="item"
                        class="item"
                        :class="{active: item < detection.microphone.volumeLevel }"
                      />
                    </ul>
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="detection.microphone.volume <= 0"
                      class="base-icon icon-sub-disable"
                    />
                    <i
                      v-show="detection.microphone.volume > 0"
                      class="base-icon icon-sub-abled"
                      @click="onMicrophoneVolumeChange('sub')"
                    />
                    <div class="info volume-info">
                      {{ detection.microphone.volume }}%
                    </div>
                    <i
                      v-show="detection.microphone.volume >= 100"
                      class="base-icon icon-add-disable"
                    />
                    <i
                      v-show="detection.microphone.volume < 100"
                      class="base-icon icon-add-abled"
                      @click="onMicrophoneVolumeChange('add')"
                    />
                  </div>
                </div>
                <div class="base-icon" />
              </div>
              <p class="sub-text">
                {{ $t('对着麦克风说话可以看到波动效果') }}
              </p>
            </section>
            <!--  麦克风/end  -->
            <!--  扬声器  -->
            <section
              v-if="detectDevices.includes('speaker')"
              class="section"
            >
              <div class="section-item">
                <div class="sub-item sub-item-radius-top">
                  <div class="label">
                    {{ $t('扬声器') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="speakers.length <= 1 || detection.speaker.currentDevice === 0"
                      class="base-icon arrows-left-disable"
                    />
                    <i
                      v-show="speakers.length > 1 && detection.speaker.currentDevice !== 0"
                      class="base-icon arrows-left-abled"
                      @click="onSpeakerChange('left')"
                    />
                    <div class="info">
                      <template v-if="speaker">
                        {{ speaker.deviceName }}
                      </template>
                      <!-- 没有可用设备 -->
                      <template v-else>
                        {{ $t(detection.speaker.tip.nodevice) }}
                      </template>
                    </div>
                    <i
                      v-show="speakers.length <= 1 || detection.speaker.currentDevice === speakers.length - 1"
                      class="base-icon arrows-right-disable"
                    />
                    <i
                      v-show="speakers.length > 1 && detection.speaker.currentDevice < speakers.length -1"
                      class="base-icon arrows-right-abled"
                      @click="onSpeakerChange('right')"
                    />
                  </div>
                </div>
                <el-tooltip
                  effect="dark"
                  :content="detection.speaker.available === null
                    ? detection.speaker.tip.detecting
                    : (speakers.length === 0
                      ? detection.speaker.tip.nodevice
                      : ( detection.speaker.available ? detection.speaker.tip.available : detection.speaker.tip.unavailable))"
                  placement="top-start"
                >
                  <i
                    v-loading="detection.speaker.available === null"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="transparent"
                    :class="{'success-icon': detection.speaker.available, 'warning-icon': detection.speaker.available === false}"
                    class="base-icon"
                  />
                </el-tooltip>
              </div>
              <div class="cut-line" />
              <div class="section-item">
                <div class="sub-item sub-item-radius-bottom">
                  <div class="label">
                    {{ $t('音量') }}
                  </div>
                  <div class="decice-info">
                    <i
                      v-show="detection.speaker.volume <= 0"
                      class="base-icon icon-sub-disable"
                    />
                    <i
                      v-show="detection.speaker.volume > 0"
                      class="base-icon icon-sub-abled"
                      @click="onSpeakerVolumeChange('sub')"
                    />
                    <div class="info volume-info">
                      {{ detection.speaker.volume }}%
                    </div>
                    <i
                      v-show="detection.speaker.volume >= 100"
                      class="base-icon icon-add-disable"
                    />
                    <i
                      v-show="detection.speaker.volume < 100"
                      class="base-icon icon-add-abled"
                      @click="onSpeakerVolumeChange('add')"
                    />
                  </div>
                </div>
                <div class="base-icon" />
              </div>
              <p class="sub-section pt10">
                <el-button
                  class="plain"
                  plain
                  size="mini"
                  @click="toggleAudioPlay"
                >
                  {{ audioPlayStatus ? $t('暂停') : $t('试听') }}
                </el-button>
              </p>
            </section>
            <!--  扬声器/end  -->
            <section class="enter-section">
              <el-button
                v-loading="isDetecting"
                type="primary"
                size="mini"
                class="enter mt10"
                element-loading-spinner="el-icon-loading"
                element-loading-background="transparent"
                element-loading-text=""
                :disabled="isDetecting"
                @click="enter"
              >
                {{ $t(isDetecting ? $t('设备检测中...') : roomInfo.enterRoom) }}
              </el-button>
            </section>
          </el-col>
        </el-row>
      </div>
    </div>
    <screen-capture-dialog
      ref="screen-capture"
      :title="$t('配置指引')"
      @status-update="screenCaptureStatusUpdate"
    />
  </div>
</template>

<script>
/*
*  Web 与 Electron 的检测流程有所不同
*  Electron的检测可以通过 TCIC.TTrtcEvent.Status_Update 事件通知知道流是否正常
*  Web通过接口调用是否报错保证
* */
import i18next from 'i18next';
import Constant from '@util/Constant';
import BaseComponent from '@core/BaseComponent';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import ScreenCaptureDialog from '@vueComponent/device-detect-component/ScreenCaptureDialog.vue';
import Util from '@util/Util';
import Media from '@/util/Media';
import Lodash from 'lodash';

const detectVolumeInterval = 200;

export default {
  name: 'CTDetect',
  filters: {
    formatClassStatus: (key) => {
      const map = {
        0: i18next.t('待开始'),
        1: i18next.t('进行中'),
        2: i18next.t('已结束'),
      };
      return map[key];
    },
  },
  components: {
    ScreenCaptureDialog,
  },
  extends: BaseComponent,

  data() {
    return {
      modal: false,
      detectDevices: [],
      mirror: null,
      isElectron: false,
      microphones: [],
      microphone: null,
      speaker: null,
      speakers: [],
      camera: null,
      cameras: [],
      audioPlayStatus: false,
      cameraMode: 0,
      speakerTestMediaUrl: 'https://res.qcloudclass.com/assets/detect.mp3',
      speakerTestMediaDuration: 9000,
      speakerTestSto: null,
      suggestionMessage: null,
      detection: DetectUtil.detection,
      classData: null,
      callback: null,
      isTeacher: false,
      result: {
        browser: {
          available: false,
          reason: null,
        },
        camera: {
          available: false,
          reason: null,
        },
        microphone: {
          available: false,
          reason: null,
        },
        speaker: {
          available: false,
          reason: null,
        },
      },
      status: 'active', // 当前的设备检测状态
      isDestroyed: false,
      roomInfo: {},
    };
  },

  computed: {
    isDetecting() {
      return !!this.detectDevices.find(item => this.detection[item].available === null);
    },
  },

  watch: {
    mirror(val) {
      localStorage.setItem('mirror', val);
      const { Enable, Disable } = TCIC.TTrtcVideoMirrorType;
      DetectUtil.setLocalVideoParams({ mirror: val ? Enable : Disable });
      return val;
    },
    audioPlayStatus(val) {
      clearTimeout(this.speakerTestSto);
      if (val) {
        this.detectSpeaker();
      } else {
        this.stopDetectSpeaker();
      }
    },
  },

  async created() {
  },
  async mounted() {
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
    if (localStorage.getItem('mirror')) {
      this.mirror = JSON.parse(localStorage.getItem('mirror'));
    } else {
      this.mirror = true;
    }
    console.log(' device-detect mounted >>>>> mirror =>', this.mirror);
    // 加入课堂事件
    // this.addLifecycleTCICEventListener(TCIC.TMainEvent.After_Enter, this.onJoinClass);
    this.makeSureClassJoined(this.onJoinClass);
  },

  methods: {
    async onJoinClass() {
      const userId = TCIC.SDK.instance.getUserId();
      const classData = TCIC.SDK.instance.getClassInfo();
      const teachersInfo = await TCIC.SDK.instance.getUserInfo(classData.teacherId);
      classData.teacherName = teachersInfo.nickname;
      this.isTeacher = userId === classData.teacherId;
      this.classData = classData;
      this.isElectron = TCIC.SDK.instance.isElectron();
      // 设备检测阶段静音，若已退出设备检测不再禁音
      if (!this.isDestroyed) TCIC.SDK.instance.muteAllRemoteAudio(true);
    },
    async reset() {
      this.status = 'reset';
      await this.stopDetectCamera();
      await this.stopDetectMicrophone();
      await this.stopDetectVolume();
      await this.stopDetectSpeaker();
    },
    async stop() {
      this.callback = null;
      await this.reset();
      this.doHide();
      if (this.suggestionMessage) {
        this.suggestionMessage.close();
      }
    },
    start(action) {
      if (!action.devices || action.devices.length === 0) {
        console.error('params error', action);
        return;
      }
      if (action.callback) {
        this.callback = action.callback;
      }
      // 桌面端不进行浏览器检测 ， 浏览器端不进行屏幕分享检测
      if (TCIC.SDK.instance.isElectron()) {
        // Mac 才检测屏幕分享
        if (!TCIC.SDK.instance.isMac()) {
          action.devices = action.devices.filter(item => item !== 'screen-capture');
        }
        // Electron 不检测浏览器
        action.devices = action.devices.filter(item => item !== 'browser');
        console.debug('detect devices =>', action.devices);
      } else if (TCIC.SDK.instance.isWeb()) {
        action.devices = action.devices.filter(item => item !== 'screen-capture');
      }
      if ((TCIC.SDK.instance.isWeb() && DetectUtil.isSafari()) || TCIC.SDK.instance.isMobile() || TCIC.SDK.instance.isPad()) {
        action.devices = action.devices.filter(item => item !== 'speaker');
      }
      // 初始化事件监听
      this.initListener();
      // 获取要检测的设备
      this.detectDevices = action.devices;
      // 开始检测
      this.startDetect(action.devices);

      this.show();
      setTimeout(() => {
        this.suggestionMessage = this.$message({
          message: i18next.t('建议您佩戴好耳机，打开摄像头和麦克风，以便获得更好的{{arg_0}}体验', { arg_0: this.roomInfo.name }),
          duration: 3000,
          type: 'warning',
          showClose: false,
          offset: 48,
        });
      }, 500);
    },

    deviceChangeListener(result) {
      const { deviceId, type, state } = result;
      console.debug('TCIC.TTrtcEvent.Device_Changed', result);
      this.deviceChangeHandler({ deviceId, type, state });
    },

    volumeUpdateListener: Lodash.debounce(function (result) {
      // console.debug('>>>>>>>>>>. volume =>', result.userId, result.volume)
      if (this.detection.microphone.volume) {
        this.detection.microphone.volumeLevel = Media.amplifier(result.volume, 13);
      } else {
        this.detection.microphone.volumeLevel = 0;
      }
    }, 50),

    deviceStatusUpdateListener(result) {
      const { state, device } = result;
      let detection = null;
      switch (device) {
        case 'mic':
          detection = this.detection.microphone;
          break;
        case 'camera':
          detection = this.detection.camera;
          break;
        default:
          break;
      }
      if (!detection) {
        console.error('Status_Update => ', result);
        return;
      }
      switch (state) {
        case - 1001: // 打开失败
        case - 1002: // 找不到设备
        case - 1003: // 未授权
        case - 1004: // 设备被占用
        case 2: // 已关闭
          detection.available = false;
          break;
        case 1: // 已打开
          if (device === 'mic' && this.microphones.length > 0) {
            detection.available = true;
          } else if (device === 'camera' && this.cameras.length > 0) {
            detection.available = true;
          }
          break;
        default:
          break;
      }
      // 收到麦克风的通知，不处理重试逻辑。
      if (device === 'mic') {
        this.unbindRetryMicrophoneTest();
      }
      this.updateTbmTarget();
    },

    initListener() {
      // 设备断通事件
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Device_Changed, this.deviceChangeListener);
      // 音量检测
      this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, (result) => {
        // 先过滤id，避免因防抖导致音量不展示
        if (result && result.userId === TCIC.SDK.instance.getUserId()) {
          this.volumeUpdateListener(result);
        }
      });
    },

    show() {
      this.modal = true;
      // 隐藏导航条
      TCIC.SDK.instance.setState(Constant.TStateDeviceDetect, true);
      this.updateTbmTarget();
    },
    doHide() {
      this.modal = false;
      // 显示导航条
      TCIC.SDK.instance.setState(Constant.TStateDeviceDetect, false);
      this.hide();
      this.updateTbmTarget();
    },

    getHelp(detection) {
      let url = '';
      switch (detection) {
        case 'device-detect': // 浏览器
          if (TCIC.SDK.instance.isWeb()) {
            url = 'https://docs.qq.com/doc/DS2tCQlBmWmpGU3lM';
          } else if (TCIC.SDK.instance.isElectron()) {
            url = TCIC.SDK.instance.isMac() ? 'https://docs.qq.com/doc/DS0F5d2xXTlhYWEJn' : 'https://docs.qq.com/doc/DS0hsTFZMQURXdUJV';
          }
          break;
        case 'download-browser':
          url = 'https://pc.qq.com/detail/1/detail_2661.html';
          break;
      }
      Util.openURL(url);
    },

    startDetect(objects) {
      objects.forEach((item) => {
        setTimeout(() => {
          this.detect(item);
        }, 0);
      });
    },

    async detect(object) {
      switch (object) {
        case 'screen-capture':
          this.detectScreenCapture();
          break;
        case 'browser':
          this.detectBrowser();
          break;
        case 'microphone':
          await this.enumerateMicrophone(true);
          await this.detectMicrophone();
          break;
        case 'speaker':
          await this.enumerateSpeaker(true);
          break;
        case 'camera':
          await this.enumerateCamera(true);
          await this.detectCamera();
          break;
      }
    },

    /**
     * 检测浏览器
     */
    detectBrowser() {
      const { browser } = this.detection;
      DetectUtil.supportDetect().then((checkResult) => {
        console.log('checkResult', checkResult.result, 'checkDetail', checkResult.detail);
        browser.available = checkResult.result;
        this.result.browser = {
          available: !!checkResult.result,
          reason: `${JSON.stringify(checkResult.detail)}`,
        };
      });
    },
    /**
     * 检测屏幕分享
     */
    detectScreenCapture() {
      const screenCapture = this.detection['screen-capture'];
      DetectUtil.detectScreenCaptureSupported().then((support) => {
        console.debug('detectScreenCaptureSupported', support);
        screenCapture.available = support;
        this.result['screen-capture'] = {
          available: screenCapture.available,
          reason: '',
        };
        this.updateTbmTarget();
      });
    },

    /**
     * 检测麦克风
     */
    async enumerateMicrophone(init = false) {
      const { microphone } = this.detection;
      try {
        this.microphones = await DetectUtil.getMicrophones();
        if (this.microphones.length === 0) {
          microphone.available = false;
          return;
        }
        microphone.available = true;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [succ] , ${JSON.stringify(this.microphones)}`,
        );
      } catch (error) {
        // 获取默认设备不阻塞其他流程
        this.microphones = [];
        this.microphone = null;
        microphone.available = false;
        console.error(error);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateMicrophone] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      if (Util.isArray(this.microphones) && this.microphones.length) {
        try {
          const deviceId = await DetectUtil.getMicDeviceId();
          if (!this.microphone) {
            this.microphone = this.microphones.find(item => item.deviceId === deviceId) || this.microphones[0];
          } else {
            const currentIndex = (this.microphones || []).findIndex(item => item.deviceId === this.microphone.deviceId);
            microphone.currentDevice = currentIndex;
            this.microphone = this.microphones[currentIndex];
          }
        } catch (e) {
          if (!this.microphone) {
            this.microphone = this.microphones[0];
          }
        }
      }
    },

    /**
     * 检测摄像头
     */
    async enumerateCamera(init = false) {
      const { camera } = this.detection;
      try {
        this.cameras = await DetectUtil.getCameras();
        if (this.cameras.length === 0) {
          camera.available = false;
          return;
        }
        camera.available = true;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [succ] , ${JSON.stringify(this.cameras)}`,
        );
      } catch (error) {
        // 获取默认设备不阻塞其他流程
        this.cameras = [];
        this.camera = null;
        camera.available = false;
        console.error(error);
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateCamera] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
      if (Util.isArray(this.cameras) && this.cameras.length) {
        try {
          const deviceId = await DetectUtil.getCameraDeviceId();
          if (!this.camera) {
            this.camera = this.cameras.find(item => item.deviceId === deviceId) || this.cameras[0];
            if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          } else {
            const currentIndex = (this.cameras || []).findIndex(item => item.deviceId === this.camera.deviceId);
            camera.currentDevice = currentIndex;
            this.camera = this.cameras[currentIndex];
          }
        } catch (e) {
          if (!this.camera) {
            this.camera = this.cameras[0];
            if (!init && this.$refs['camera-select']) {
              this.$refs['camera-select'].$emit('change');
            }
          }
        }
      }
    },

    /**
     * 切换镜像模式
     */
    changeMirrorStatus() {
      this.mirror = !this.mirror;
      this.updateTbmTarget();
    },

    /**
     * 检测扬声器
     */
    async enumerateSpeaker(init = false) {
      const { speaker } = this.detection;
      speaker.available = null;
      try {
        const devices = await DetectUtil.getSpeakers();
        if (Util.isArray(devices) && devices.length) {
          this.speakers = devices;
          if (!this.speaker) {
            DetectUtil.getSpeakerDeviceId().then((deviceId) => {
              this.speaker = devices.find(item => item.deviceId === deviceId) || devices[0];
            });
          } else {
            const currentIndex = (this.speakers || []).findIndex(item => item.deviceId === this.speaker.deviceId);
            speaker.currentDevice = currentIndex;
            this.speaker = this.speakers[currentIndex];
          }
          TCIC.SDK.instance.reportLog(
            'device-detect',
            `[enumerateSpeaker] [succ] , ${JSON.stringify(devices)}`,
          );
          speaker.available = true;
          this.result.speaker = {
            available: true,
            reason: null,
          };
          try {
            speaker.volume = await DetectUtil.getSpeakerVolume();
          } catch (error) {
            console.error('getSpeakerVolume', error);
            this.$message.error(i18next.t('获取扬声器音频失败，请重试'));
          }
        } else {
          speaker.available = false;
          this.result.speaker = {
            available: false,
            reason: null,
          };
        }
      } catch (error) {
        speaker.available = false;
        this.result.speaker = {
          available: false,
          reason: `${error.name || ''} : ${error.message || ''}` || error,
        };
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[enumerateSpeaker] [error] name: ${error.name}, message: ${error.message}`,
        );
      }
    },


    /**
     * @description 检测麦克风
     */
    async detectMicrophone() {
      if (this.status === 'reset') {
        return;
      }
      if (this.microphones.length === 0) {
        console.warn('没有枚举到麦克风设备');
        return;
      }
      const { microphone } = this.detection;
      microphone.available = null;
      try {
        await DetectUtil.startMicTest(this.$refs.microphone);
        this.result.microphone = {
          available: true,
          reason: null,
        };
        microphone.available = true;
        this.detectVolume();
      } catch (e) {
        if (e && e.getCode && (e.getCode() === 0x1001 || e.getCode() === - 2)) {
          console.debug('detectMicrophone warning', e);
        } else {
          microphone.available = false;
          this.result.microphone = {
            available: false,
            reason: `${e.name || ''} : ${e.message || ''}` || e,
          };
          throw e;
        }
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[detectMicrophone] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * @description 停止检测麦克风
     */
    async stopDetectMicrophone() {
      await DetectUtil.stopDetectMicrophone();
      this.stopDetectVolume();
    },

    /**
     * @description 检测摄像头
     */
    async detectCamera() {
      if (this.status === 'reset') {
        return;
      }
      if (this.cameras.length === 0) {
        console.warn('没有枚举到摄像头设备');
        return;
      }
      const { camera } = this.detection;
      camera.available = null;
      try {
        await DetectUtil.startCameraTest(this.$refs.camera);
        camera.available = true;
        this.result.camera = {
          available: true,
          reason: null,
        };
      } catch (e) {
        camera.available = false;
        this.result.camera = {
          available: false,
          reason: `${e.name || ''} : ${e.message || ''}` || e,
        };
      }
      this.updateTbmTarget();
    },

    /**
     * @description 停止检测摄像头
     */
    stopDetectCamera() {
      return DetectUtil.stopCameraTest();
    },

    /**
     * @description 检测音量
     */
    async detectVolume() {
      if (this.status === 'reset') {
        return;
      }
      const { microphone } = this.detection;
      try {
        microphone.volume = TCIC.SDK.instance.getState(Constant.TStateCoMicVolume);
      } catch (e) {
        console.error('detectVolume', e);
        this.$message.error(i18next.t('获取麦克风音频失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[detectVolume] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      return DetectUtil.enableVolumeEvaluation(detectVolumeInterval);
    },

    /**
     * @description 停止检测音量
     */
    stopDetectVolume() {
      // 降低间隔，用于展示音量
      return DetectUtil.enableVolumeEvaluation(500);
    },

    /**
     * @description 开始检测扬声器
     */
    async detectSpeaker() {
      try {
        await DetectUtil.stopSpeakerTest();
        await DetectUtil.startSpeakerTest(this.speakerTestMediaUrl);
        this.speakerTestSto = setTimeout(() => {
          this.audioPlayStatus = false;
        }, this.speakerTestMediaDuration);
      } catch (e) {
        console.error('detectSpeaker', e);
        this.$message.error(i18next.t('检测扬声器失败，请检查设备并重试'));
        this.audioPlayStatus = false;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[detectSpeaker] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * @description 停止检测扬声器
     */
    stopDetectSpeaker() {
      try {
        DetectUtil.stopSpeakerTest();
      } catch (e) {
        console.error('stopDetectSpeaker', e);
      }
    },

    /**
     * @description 检测麦克风的audio播放/暂停切换
     */
    toggleAudioPlay() {
      this.audioPlayStatus = !this.audioPlayStatus;
    },

    /**
     * 扬声器变化
     */
    async onSpeakerChange(type) {
      const currentIndex = (this.speakers || []).findIndex(item => item.deviceId === this.speaker.deviceId);
      let changeIndex;
      let device;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;;
      }
      device = this.speakers[changeIndex];
      const { speaker } = this.detection;
      speaker.available = null;
      console.log('>>>>>>>>>>>>>>  device speaker', device);
      device = device || this.speaker;
      try {
        if (this.audioPlayStatus) {
          this.toggleAudioPlay();
        }
        await this.stopDetectSpeaker();
        DetectUtil.switchSpeaker(device.deviceId);
        // this.$message.success('切换成功');
        speaker.available = true;
        speaker.currentDevice = changeIndex;
        this.speaker = device;
      } catch (e) {
        console.error('switch speaker error', e);
        this.$message.error(i18next.t('切换扬声器失败，请重试'));
        speaker.available = false;
        // 停止测试
        this.stopDetectSpeaker();
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onSpeakerChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * 麦克风
     */
    async onMicrophoneChange(type) {
      const currentIndex = (this.microphones || []).findIndex(item => item.deviceId === this.microphone.deviceId);
      let changeIndex;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;;
      }
      const device = this.microphones[changeIndex];
      const { microphone } = this.detection;
      const detectDevice = device || this.microphone;
      microphone.available = null;
      microphone.currentDevice = changeIndex;
      this.microphone = device;
      console.log('>>>>>>>>>>>>>>  onMicrophoneChange ', detectDevice);
      try {
        await this.stopDetectMicrophone();
        // Electron必须先切换设备，再调用检测接口，否则流不会变。
        DetectUtil.switchMic(detectDevice.deviceId);
        await this.detectMicrophone();
        microphone.available = true;
      } catch (e) {
        console.error('switch microphone error', e);
        this.$message.error(i18next.t('切换麦克风失败，请重试'));
        microphone.available = false;
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onMicrophoneChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * 麦克风音量调整
     */
    async onMicrophoneVolumeChange(type) {
      const step = 5;
      let value;
      // 减音量
      if (type === 'sub' && this.detection.microphone.volume > 0) {
        value = this.detection.microphone.volume - step;
      } else if (type === 'add' && this.detection.microphone.volume < 100) { // 加音量
        value = this.detection.microphone.volume + step;
      } else {
        return ;
      }
      console.log('>>>>>>>>>>>>>>  set microphone volume', value);
      try {
        await DetectUtil.setMicrophoneVolume(value);
        TCIC.SDK.instance.setState(Constant.TStateCoMicVolume, value);
        this.detection.microphone.volume = value;
      } catch (e) {
        console.error('set microphone volume error', e);
        this.$message.error(i18next.t('调整麦克风音量失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onMicrophoneVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * 扬声器音量调整
     */
    async onSpeakerVolumeChange(type) {
      const step = 5;
      let value;
      // 减音量
      if (type === 'sub' && this.detection.speaker.volume > 0) {
        value = this.detection.speaker.volume - step;
      } else if (type === 'add' && this.detection.speaker.volume < 100) { // 加音量
        value = this.detection.speaker.volume + step;
      } else {
        return ;
      }
      console.log('>>>>>>>>>>>>>>  set microphone volume', value);
      try {
        await DetectUtil.setSpeakerVolume(value);
        this.detection.speaker.volume = value;
      } catch (e) {
        console.error('set microphone volume error', e);
        this.$message.error(i18next.t('调整扬声器音量失败，请重试'));
        TCIC.SDK.instance.reportLog(
          'device-detect',
          `[onSpeakerVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
        );
      }
      this.updateTbmTarget();
    },

    /**
     * 摄像头变化
     */
    async onCameraChange(type) {
      const currentIndex = (this.cameras || []).findIndex(item => item.deviceId === this.camera.deviceId);
      let changeIndex;
      if (type === 'left') {
        changeIndex = currentIndex - 1;
      } else if (type === 'right') {
        changeIndex = currentIndex + 1;;
      }
      const device = this.cameras[changeIndex];
      const { camera } = this.detection;
      const detectDevice = device || this.camera;
      camera.available = null;
      camera.currentDevice = changeIndex;
      this.camera = device;
      console.log('>>>>>>>>>>>>>>  onCameraChange ', detectDevice);
      try {
        await this.stopDetectCamera();
        await DetectUtil.switchCamera(detectDevice.deviceId);
        // windows 端会有闪一下的情况，如果不做这个处理，如果原来是没有流的情况，就看不到视频了
        await this.detectCamera();
        camera.available = true;
      } catch (e) {
        console.error('switch camera error', e);
        camera.available = false;
        this.$message.error(i18next.t('切换摄像头失败，请重试'));
      }
      this.updateTbmTarget();
    },


    enter() {
      if (this.callback) {
        this.callback(this.result);
      }
      // TCIC.SDK.instance.notify('device-detect-completed', {});    // 设备检测完成
      this.stop();
      this.isDestroyed = true;
      // 设备检测完成后取消静音
      TCIC.SDK.instance.muteAllRemoteAudio(false);
      this.$destroy();
      const enterTime = new Date().getTime();
      TCIC.SDK.instance.reportEvent('handle_enter_class', {}, 0, enterTime);
      window.tbm.removeTarget('deviceDetect');
    },

    deviceChangeHandler({ deviceId, type, state }) {
      const {
        Add,
        Remove,
        Active,
      } = TCIC.TTrtcDeviceState;
      switch (state) {
        case Add:
          this.deviceAddHandler({ deviceId, type });
          break;
        case Remove:
          this.deviceRemoveHandler({ deviceId, type });
          break;
        case Active:
          this.deviceActiveHandler({ deviceId, type });
          break;
      }
    },

    /*
    * 设备新增
    * 如果当前没有此类型设备：
    *   1.设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceAddHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          await this.enumerateMicrophone();
          break;
        case Camera:
          await this.enumerateCamera();
          break;
        case Speaker:
          // 如果当前有设备，只重新枚举设备
          await this.enumerateSpeaker();
          break;
      }
    },
    /*
    * 设备可用
    * 如果是当前设备：
    *   1.设备检测
    * 如果不是当前设备：
    *   do nothing
    * */
    async deviceActiveHandler({ deviceId, type }) {
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          // 如果当前设备切换成可用，重新检测
          if (this.microphone.deviceId === deviceId) {
            await this.detect('microphone');
          }
          break;
        case Camera:
          if (this.camera.deviceId === deviceId) {
            await this.detect('camera');
          }
          break;
        case Speaker:
          if (this.speaker.deviceId === deviceId) {
            await this.detect('speaker');
          }
          break;
      }
    },
    /*
    * 设备移除
    * 如果是当前设备：
    *   1.先复位当前选中设备 2.重新设备枚举，设备检测
    * 如果不是当前设备：
    *   2.枚举设备
    * */
    async deviceRemoveHandler({ deviceId, type }) {
      // const { camera, microphone, speaker } = this.detection;
      const {
        Mic,
        Speaker,
        Camera,
      } = TCIC.TTrtcDeviceType;
      switch (type) {
        case Mic:
          console.debug('deviceChange | deviceRemoveHandler | mic => ', this.microphone.deviceId, deviceId);
          if (this.microphone.deviceId === deviceId) {
            this.microphone = null;
          }
          await this.enumerateMicrophone();
          break;
        case Camera:
          console.debug('deviceChange | deviceRemoveHandler | camera => ', this.camera.deviceId, deviceId);
          if (this.camera.deviceId === deviceId) {
            this.camera = null;
          }
          await this.enumerateCamera();
          break;
        case Speaker:
          console.debug('deviceChange | deviceRemoveHandler | speaker => ', this.speaker.deviceId, deviceId);
          if (this.speaker.deviceId === deviceId) {
            this.speaker = null;
          }
          if (this.audioPlayStatus) {
            this.toggleAudioPlay();
          }
          await this.enumerateSpeaker();
          break;
      }
    },

    showScreenCaptureGuide() {
      this.$refs['screen-capture'].show();
    },
    screenCaptureStatusUpdate() {
      this.detect('screen-capture');
      this.$refs['screen-capture'].hide();
    },
    getSectionButtons(dom) {
      return Array.from(dom.querySelectorAll('i.base-icon'))
        .filter(item => this.checkValid(item));
    },
    checkValid(dom) {   // 检测按钮是否可用
      const validClassArr = ['arrows-left-abled', 'arrows-right-abled', 'icon-sub-abled', 'icon-add-abled'];
      if (dom) {
        if (dom.style.display === 'none') return false;     // 不可见时直接返回不可用
        const domClasses = Array.from(dom.classList);
        for (let i = 0; i < domClasses.length; i ++) {
          if (validClassArr.includes(domClasses[i])) return true;
        }
      }
      return false;
    },
    updateTbmTarget: Lodash.throttle(function () {
      // 注册按键事件
      this.$nextTick(() => {
        if (!this.modal) {     // 不显示时重置
          window.tbm.removeTarget('deviceDetect');
          return ;
        };
        const subTargets = ['camera', 'cameraSelect', 'cameraMirror', 'mic', 'micSelect', 'micVolume', 'speaker', 'speakerVolumes', 'test', 'join'];
        const sections = Array.from(this.$el.querySelectorAll('div.section-item'));
        const count = Math.min(subTargets.length, sections.length);
        window.tbm.pushTarget('deviceDetect', subTargets);
        for (let i = 0; i < count; i ++) {
          window.tbm.updateTarget(
            'deviceDetect',
            this.getSectionButtons(sections[i])
              .map(item => window.tbm.generateNode(item)),
            subTargets[i],
          );
        }
        window.tbm.updateTarget(
          'deviceDetect',
          Array.from(this.$el.querySelectorAll('button.audition-button'))
            .map(item => window.tbm.generateNode(item)),
          'button',
        );
        window.tbm.updateTarget('deviceDetect', [
          window.tbm.generateNode(this.$el.querySelector('button.el-button.plain')),      // 试听
        ], 'test');
        window.tbm.updateTarget('deviceDetect', [
          window.tbm.generateNode(this.$el.querySelector('button.el-button.enter')),      // 进入课堂
        ], 'join');
        window.tbm.lockTarget('deviceDetect');
      });
    }, 500, {
      leading: false,
      trailing: true,
    }),
  },
};
</script>

<style lang="less" scoped>
  @import "./theme/co-teaching.less";
</style>
