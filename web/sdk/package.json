{"name": "@tencent/tcic-sdk", "version": "1.8.17", "description": "", "main": "src/tcic.ts", "scripts": {"tsc": "tsc", "build": "npm run prod", "version": "npm run scan-check && node setVersion.js", "dev": "webpack --watch --config webpack.dev.js", "test": "webpack --config webpack.dev.js", "prod": "webpack --config webpack.prod.js", "jsdoc": "jsdoc -c ./jsdoc/config.json", "docs": "typedoc --readme ./readme-doc.md", "mp-version": "npm run scan-check && node setMpVersion.js", "mp-dev": "webpack --watch --config webpack.mp-dev.js", "mp": "npm run mp-version && webpack --config webpack.mp.js", "clean": "find ./src -name '*.js*' -exec rm -f \\;", "pre-commit": "npm run scan-check && npm run eslint-nofix", "eslint": "eslint ./src --ext .ts . --fix", "eslint-nofix": "eslint ./src --ext .ts .", "scan": "i18next-scanner --config i18next-scanner.config.js", "scan-check": "npm run scan && node i18next-check.js"}, "repository": {"type": "git", "url": "https://git.woa.com/kennethmiao/apaas-sdk.git"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "7.14.8", "@babel/polyfill": "7.4.4", "@babel/preset-env": "7.14.8", "@tencent/eslint-config-tencent": "0.13.3", "@types/babel-types": "7.0.0", "@types/lodash": "^4.14.191", "@types/node": "^16.6.2", "@types/pako": "^1.0.2", "@types/wechat-miniprogram": "3.4.1", "@typescript-eslint/eslint-plugin": "^6.7.2", "@typescript-eslint/parser": "^6.7.2", "aegis-web-sdk": "^1.38.6", "babel-loader": "8.0.6", "better-docs": "^2.7.2", "clean-webpack-plugin": "^4.0.0", "docdash-blue": "1.1.3", "eslint": "7.27.0", "eslint-loader": "4.0.2", "eslint-webpack-plugin": "2.6.0", "i18next-scanner": "4.2.0", "jsdoc": "^3.6.7", "moment": "^2.29.4", "terser-webpack-plugin": "^4.2.3", "trtc-electron-sdk": "12.2.701", "trtc-js-sdk": "4.15.21", "ts-loader": "6.0.3", "typedoc": "^0.25.1", "typedoc-plugin-missing-exports": "^2.1.0", "typescript": "5.5.2", "webpack": "4.35.0", "webpack-bundle-analyzer": "^4.4.2", "webpack-cli": "3.3.12", "webpack-preprocessor-loader": "^1.2.0"}, "dependencies": {"@tencentcloud/chat": "^3.4.2", "aegis-mp-sdk": "^1.36.5", "axios": "0.27.2", "core-js": "2", "cos-js-sdk-v5": "^1.8.2", "decode-uri-component": "^0.2.2", "hacktimer": "^1.1.3", "i18next": "^23.5.1", "i18next-http-backend": "^1.4.5", "lodash": "^4.17.21", "md5-typescript": "1.0.5", "minimist": "^1.2.8", "pako": "^2.0.4", "rxjs": "^7.8.1", "sha256": "^0.2.0", "tencentcloud-webar": "1.0.25-3", "tim-upload-plugin": "^1.0.6", "tim-wx-sdk": "2.22.0", "trtc-sdk-v5": "5.10.0", "vconsole": "^3.15.1"}, "volta": {"extends": "../../package.json"}}