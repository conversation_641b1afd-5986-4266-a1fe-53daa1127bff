import { TErrorInfo } from './module/business/tbusiness';
import {
  TClassListInfo,
  TClassStatus,
  TClassSubType,
  TClassInfo,
  TClassSilenceMode,
} from './module/business/tbusiness_class';
import {
  TCreateDocumentResult,
  TDocumentInfo,
  TGetDocumentListParam,
  TGetDocumentListResult,
} from './module/business/tbusiness_document';
import {
  TAnswerQuestionParam,
  TAnswerQuestionResult,
  TCreateQuestionParam,
  TCreateQuestionResult,
  TGetQuestionInfoResult,
  TGetQuestionResultResult,
  TGetQuestionsResult,
  TQuestionAnswer,
  TQuestionStatus,
  TQuestionType,
} from './module/business/tbusiness_exam';
import {
  TCommandID,
  TCommandStatus,
  TCommandStatusItem,
  TBaseCommandItem,
  TCommandList,
  TCommandReq,
  TCommandReqResult,
  TGetUserCommandReqResult,
  TGetAllCommandReqResult,
  TStageCommandParam,
  TStageCommandItem,
} from './module/business/tbusiness_command';
import { TResourcePath } from './module/business/tbusiness_rbac';
import {
  TClassMember,
  TGetMemberListResult,
  TMemberActionType,
  TMemberInfo,
  TMemberStatus,
  TMemberType,
  TStreamType,
  TScreenState,
} from './module/business/tbusiness_member';
import { TSchoolInfo } from './module/business/tbusiness_school';
import { TUserInfo } from './module/business/tbusiness_user';
import {
  TIMConvType,
  TIMEvent,
  TIMMsgType,
  TMainEvent,
  TMainState,
  TPermissionFlag,
  TPackageType,
  TPermissionUpdateReason,
  TResourceType,
  TPlatform,
  TDevice,
  TClassLayout,
  TDeviceStatus,
} from './constants';
import {
  TMain as SDK,
  TDeviceOrientation,
} from './module/tmain';
import {
  TScreenCaptureSourceType,
  TTrtcDeviceState,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcVideoFillMode,
  TTrtcVideoMirrorType,
  TTrtcVideoRotation,
  TTrtcVideoStreamType,
  TTrtcVideoResolution,
} from './module/trtc/ttrtc_base';
import {
  TStatisticsEvent,
  TNetworkStatistics,
  TRemoteNetworkStatistics,
  TNetworkQuality,
} from './module/statistics/tstatistics_base';
import { TLiveEvent } from './module/live/live_base';
import { getAegis } from './utils/aegis';

const exposed = {
  /**
   * SDK主要方法类
   * @type TMain
   * @todo 文档生成有问题，TMainEvent和TMainState等一些值会缺失
   */
  SDK,
  /**
   * 业务层相关事件
   * @type TMainEvent
   */
  TMainEvent,
  /**
   * 业务层相关状态
   * @type TMainState
   */
  TMainState,
  /**
   * 资源类型
   * @type TResourceType
   */
  TResourceType,
  /**
   * 设备方向
   * @type TDeviceOrientation
   */
  TDeviceOrientation,
  /**
   * 课堂布局
   * @type TClassLayout
   */
  TClassLayout,
  /**
   * 成员相关操作
   * @type TMemberActionType
   */
  TMemberActionType,
  /**
   * 成员类型
   * @type TMemberType
   */
  TMemberType,
  /**
   * 成员在线状态
   * @type TMemberStatus
   */
  TMemberStatus,
  /**
   * 课堂发言模式
   * @type TClassSilenceMode
   */
  TClassSilenceMode,
  /**
   * 成员信息
   * @type TMemberInfo
   */
  TMemberInfo,
  /**
   * 设备状态
   * @type TDeviceStatus
   */
  TDeviceStatus,
  /**
   * 屏幕分享状态
   * @type TScreenState
   */
  TScreenState,
  /**
   * 学校信息
   * @type TSchoolInfo
   */
  TSchoolInfo,
  /**
   * 套餐包类型
   * @type TPackageType
   */
  TPackageType,
  /**
   * 课堂状态
   * @type TClassStatus
   */
  TClassStatus,
  /**
   * 课堂状态
   * @type TClassStatus
   */
  TClassSubType,
  /**
   * 课堂信息
   * @type TClassListInfo
   */
  TClassListInfo,
  /**
   * 互动课堂信息
   * @type TClassInfo
   */
  TClassInfo,
  /**
   * 用户信息
   * @type TUserInfo
   */
  TUserInfo,
  /**
   * 权限变更原因
   * @type TPermissionUpdateReason
   */
  TPermissionUpdateReason,
  /**
   * 课堂成员
   * @type TClassMember
   */
  TClassMember,
  /**
   * 平台类型
   * @type TPlatform
   */
  TPlatform,
  /**
   * 设备类型
   * @type TPlatform
   */
  TDevice,
  /**
   * 错误信息
   * @type TErrorInfo
   */
  TErrorInfo,
  /**
   * TLive事件
   * @type TLiveEvent
   */
  TLiveEvent,
  /**
   * TRTC事件
   * @type TTrtcEvent
   */
  TTrtcEvent,
  /**
   * TRTC设备类型
   * @type TTrtcDeviceType
   */
  TTrtcDeviceType,
  /**
   * TRTC设备状态
   * @type TTrtcDeviceState
   */
  TTrtcDeviceState,
  /**
   * TRTC视频填充模式
   * @type TTrtcVideoFillMode
   */
  TTrtcVideoFillMode,
  /**
   * TRTC视频旋转模式
   * @type TTrtcVideoRotation
   */
  TTrtcVideoRotation,
  /**
   * TRTC视频镜像类型
   * @type TTrtcVideoMirrorType
   */
  TTrtcVideoMirrorType,
  /**
   * TRTC视频流类型
   * @type TTrtcVideoStreamType
   */
  TTrtcVideoStreamType,
  /**
   * TRTC视频流分辨率
   * @type TTrtcVideoResolution
   */
  TTrtcVideoResolution,
  /**
   * 屏幕分享数据源类型
   * @type TScreenCaptureSourceType
   */
  TScreenCaptureSourceType,
  /**
   * IM事件
   * @type TIMEvent
   */
  TIMEvent,
  /**
   * IM会话类型
   * @type TIMConvType
   */
  TIMConvType,
  /**
   * IM消息类型
   * @type TIMMsgType
   */
  TIMMsgType,
  /**
   * 文档信息
   * @type TDocumentInfo
   */
  TDocumentInfo,
  /**
   * 获取文档列表参数
   * @type TGetDocumentListParam
   */
  TGetDocumentListParam,
  /**
   * 获取文档列表结果
   * @type TGetDocumentListResult
   */
  TGetDocumentListResult,
  /**
   * 创建文档结果
   * @type TCreateDocumentResult
   */
  TCreateDocumentResult,
  /**
   * 获取成员列表结果
   * @type TGetMemberListResult
   */
  TGetMemberListResult,
  /**
   * 问题类型
   * @type TQuestionType
   */
  TQuestionType,
  /**
   * 问题状态
   * @type TQuestionStatus
   */
  TQuestionStatus,
  /**
   * 用户的答题结果
   * @type TQuestionAnswer
   */
  TQuestionAnswer,
  /**
   * 创建答题参数
   * @type TCreateQuestionParam
   */
  TCreateQuestionParam,
  /**
   * 创建答题结果
   * @type TCreateQuestionResult
   */
  TCreateQuestionResult,
  /**
   * 回答问题参数
   * @type TAnswerQuestionParam
   */
  TAnswerQuestionParam,
  /**
   * 回答问题结果
   * @type TAnswerQuestionResult
   */
  TAnswerQuestionResult,
  /**
   * 获取答题信息
   * @type TGetQuestionInfoResult
   */
  TGetQuestionInfoResult,
  /**
   * 获取答题记录
   * @type TGetQuestionResultResult
   */
  TGetQuestionResultResult,
  /**
   * 获取答题列表
   * @type TGetQuestionsResult
   */
  TGetQuestionsResult,
  /**
   * 网络统计数据
   * @type TNetworkStatistics
   */
  TNetworkStatistics,
  /**
   * 网络质量
   * @type TNetworkQuality
   */
  TNetworkQuality,
  /**
   * 远端网络质量
   * @type TRemoteNetworkStatistics
   */
  TRemoteNetworkStatistics,
  /**
   * 网络统计数据事件
   * @type TStatisticsEvent
   */
  TStatisticsEvent,
  /**
   * 快直播流类型
   * @type TStreamType
   */
  TStreamType,
  /**
   * Command类型枚举
   * @type TCommandID
   */
  TCommandID,
  /**
   * Command类型状态枚举
   * @type TCommandStatus
   */
  TCommandStatus,
  /**
   * sendCommand时需要的类型
   * @type TCommandReq
   */
  TCommandReq,
  /**
   * 请求上麦时附带的自定义参数类型
   * @type TStageCommandParam
   */
  TStageCommandParam,
  /**
   * 权限标识
   * @type TPermissionFlag
   */
  TPermissionFlag,
  /**
   * 权限资源路径
   * @type TResourcePath
   */
  TResourcePath,

  get aegis()  {
    return getAegis();
  },

};

declare global {
  const TCIC: typeof exposed;
}

/**
 * SDK所有功能入口
 * @namespace
 * @alias TCIC
 */
export default exposed;
