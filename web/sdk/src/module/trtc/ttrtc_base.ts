import i18next from 'i18next';
import { TMainState, TDeviceStatus } from '../../constants';
import { TPromiseModule } from '../../base/tmodule';
import { TState } from '../tstate';
import { TStreamType } from '../business/tbusiness_member';
import { TSession } from '../tsession';
import { TMain } from '../tmain';

/**
 * 音视频相关事件
 * @enum {string}
 * @property {'tcic@trtc@video-changed'} Video_Changed 视频流可用状态变化
 * @property {'tcic@trtc@audio-changed'} Audio_Changed 音频流可用状态变化
 * @property {'tcic@trtc@audio-mute'} Audio_Mute 强制设置音视频静音状态
 * @property {'tcic@trtc@sub-stream-changed'} SubStream_Changed 辅路流可用状态变化
 * @property {'tcic@trtc@sub-stream-loaded'} SubStream_Loaded 辅路流加载成功回调
 * @property {'tcic@trtc@stream-added'} Stream_Added 新视频流
 * @property {'tcic@trtc@play-stream-changed'} PlayStream_Changed 音视频课件流可用状态变化
 * @property {'tcic@trtc@volume-update'} Volume_Update 监控-音量回调
 * @property {'tcic@trtc@network-quality'} Network_Quality 监控-网络状态
 * @property {'tcic@trtc@network-statistis'} Network_Statistis 监控-网络统计数据
 * @property {'tcic@trtc@device-changed'} Device_Changed 设备变化事件
 * @property {'tcic@trtc@screen-share-stopped'} Screen_Share_Stopped 屏幕分享已结束
 * @property {'tcic@trtc@music-progress'} Music_Progress 音乐进度回调 [current, duration]
 * @property {'tcic@trtc@music-complete'} Music_Complete 音乐 [id, errorCode]
 * @property {'tcic@trtc@music-download-progress'} Music_Download_Progress 音乐下载进度 [current, total]
 *
 */
export enum TTrtcEvent {
  /**
   * 麦克风设备切换
   */
  Mic_Device_Changed = 'tcic@trtc@mic-device-changed',
  /**
   * 扬声器设备切换
   */
  Speaker_Device_Changed = 'tcic@trtc@speaker-device-changed',
  /**
   * 摄像头设备切换
   */
  Camera_Device_Changed = 'tcic@trtc@camera-device-changed',
  /**
   * 视频流可用状态变化
   * @param userId      视频流所属用户
   * @param available   是否可用
   */
  Video_Changed = 'tcic@trtc@video-changed',

  /**
   * 音频流可用状态变化
   * @param userId      视频流所属用户
   * @param available   是否可用
   */
  Audio_Changed = 'tcic@trtc@audio-changed',

  /**
   * 强制设置音视频静音状态
   * @param userId      视频流所属用户
   * @param mute   是否静音
   */
  Audio_Mute = 'tcic@trtc@audio-mute',

  /**
   * 辅路流可用状态变化
   * @param userId      视频流所属用户
   * @param available   是否可用
   */
  SubStream_Changed = 'tcic@trtc@sub-stream-changed',

  /**
   * 辅路流加载成功回调
   * @param userId      视频流所属用户
   * @param available   是否可用
   */
  SubStream_Loaded = 'tcic@trtc@sub-stream-loaded',

  /**
   * 新视频流
   * @param userId      视频流所属用户
   */
  Stream_Added = 'tcic@trtc@stream-added',

  /**
   * 音视频课件流可用状态变化
   * @param userId      视频流所属用户
   * @param status     状态
   */
  PlayStream_Changed = 'tcic@trtc@play-stream-changed',

  /**
   * 监控-音量回调
   */
  Volume_Update = 'tcic@trtc@volume-update',

  /**
   * 监控-网络状态
   */
  Network_Quality = 'tcic@trtc@network-quality',

  /**
   * 监控-网络统计数据
   */
  Network_Statistis = 'tcic@trtc@network-statistis',

  /**
   * 设备变化事件
   */
  Device_Changed = 'tcic@trtc@device-changed',
  /**
   * 屏幕分享已结束
   */
  Screen_Share_Stopped = 'tcic@trtc@screen-share-stopped',
  /**
   * 音乐进度回调 [current, duration]
   */
  Music_Progress = 'tcic@trtc@music-progress',
  /**
   * 音乐 [id, errorCode]
   */
  Music_Complete = 'tcic@trtc@music-complete',
  /**
   * 音乐下载进度 [current, total]
   */
  Music_Download_Progress = 'tcic@trtc@music-download-progress',

  /**
 * 监控-自动播放失败
 */
  AUTOPLAY_FAILED = 'tcic@trtc@autoplay-failed',
  /**
 * 监控-自动播放失败
 */
  AUTOPLAY_CONFIRM = 'tcic@trtc@autoplay-confirm',
}

/**
 * TRTC模式
 */
export enum TTrtcMode {
  /**
   * 实时音视频模式
   */
  RTC = 'rtc',

  /**
   * 直播模式
   */
  Live = 'live',
}

/**
 * 设备类型
 * @enum {number}
 * @readonly
 * @property {number}  Unknown  值为（-1），Device_Change 事件回调专用
 * @property {0} Mic 麦克风，Device_Change 事件回调专用
 * @property {1} Speaker 扬声器，Device_Change 事件回调专用
 * @property {2} Camera 摄像头，Device_Change 事件回调专用
 */
export enum TTrtcDeviceType {
  Unknown = -1,
  Mic = 0,
  Speaker = 1,
  Camera = 2
}

const deviceKindMap = {
  audioinput: TTrtcDeviceType.Mic,
  audiooutput: TTrtcDeviceType.Speaker,
  videoinput: TTrtcDeviceType.Camera,
};
export function convertMediaDeviceInfo(device: MediaDeviceInfo) {
  const newDevice: TTrtcDeviceInfo = {
    type: TTrtcDeviceType.Unknown,
    deviceId: device.deviceId,
    deviceName: device.label,
  };
  if (device.kind in deviceKindMap) {
    newDevice.type = deviceKindMap[device.kind];
  }
  return newDevice;
}

/**
 * 设备插拔事件类型
 * @enum {number}
 * @property {0} Add 添加
 * @property {1} Remove 移除
 * @property {2} Active 可使用
 * @property {3} Update 更新
 */
export enum TTrtcDeviceState {
  Add = 0,
  Remove = 1,
  Active = 2,
  Update = 3
}

/**
 * 视频流类型
 * @enum {number}
*  @property {0} Big 摄像头大流
*  @property {1} Small 摄像头小流
*  @property {2} Sub 辅流，例如桌面端屏幕分享
*  @property {3} Vod 点播视频
*  @property {10} QLive 快直播流
**/
export enum TTrtcVideoStreamType {
  Big = 0,
  Small = 1,
  Sub = 2,
  Vod = 3,
  QLive = 10,
}

/**
 * 视频分辨率
 * @enum {number}
 */
export enum TTrtcVideoResolution {
  // 宽高比1:1
  Resolution_120_120   = 1,     // [C] 建议码率80kbps
  Resolution_160_160   = 3,     // [C] 建议码率100kbps
  Resolution_270_270   = 5,     // [C] 建议码率200kbps
  Resolution_480_480   = 7,     // [C] 建议码率350kbps

  // 宽高比4:3
  Resolution_160_120   = 50,    // [C] 建议码率100kbps
  Resolution_240_180   = 52,    // [C] 建议码率150kbps
  Resolution_280_210   = 54,    // [C] 建议码率200kbps
  Resolution_320_240   = 56,    // [C] 建议码率250kbps
  Resolution_400_300   = 58,    // [C] 建议码率300kbps
  Resolution_480_360   = 60,    // [C] 建议码率400kbps
  Resolution_640_480   = 62,    // [C] 建议码率600kbps
  Resolution_960_720   = 64,    // [C] 建议码率1000kbps

  // 宽高比16:9
  Resolution_160_90    = 100,   // [C] 建议码率150kbps
  Resolution_256_144   = 102,   // [C] 建议码率200kbps
  Resolution_320_180   = 104,   // [C] 建议码率250kbps
  Resolution_480_270   = 106,   // [C] 建议码率350kbps
  Resolution_640_360   = 108,   // [C] 建议码率550kbps
  Resolution_960_540   = 110,   // [C] 建议码率850kbps
  Resolution_1280_720  = 112,   // [C] 摄像头采集 - 建议码率1200kbps

  // [S] 屏幕分享   - 建议码率：低清：1000kbps 高清：1600kbps
  Resolution_1920_1080 = 114,   // [S] 屏幕分享   - 建议码率2000kbps
}

export enum TRTCVideoQosPreference {
  TRTC_VIDEO_QOS_PREFERENCE_SMOOTH = 1, // 流畅度优先
  TRTC_VIDEO_QOS_PREFERENCE_CLEAR = 2 // 清晰度优先
}

export interface VideoSize {
  width: number;
  height: number;
}

export function getVideoSizeFromResolution(resolution: TTrtcVideoResolution, resolutionMode: 0|1 = 0): VideoSize {
  const size: VideoSize = {
    width: 0,
    height: 0,
  };
  switch (resolution) {
    case TTrtcVideoResolution.Resolution_120_120:
      size.width = 120;
      size.height = 120;
      break;
    case TTrtcVideoResolution.Resolution_160_160:
      size.width = 160;
      size.height = 160;
      break;
    case TTrtcVideoResolution.Resolution_270_270:
      size.width = 270;
      size.height = 270;
      break;
    case TTrtcVideoResolution.Resolution_480_480:
      size.width = 480;
      size.height = 480;
      break;
    case TTrtcVideoResolution.Resolution_160_120:
      size.width = 160;
      size.height = 120;
      break;
    case TTrtcVideoResolution.Resolution_240_180:
      size.width = 240;
      size.height = 180;
      break;
    case TTrtcVideoResolution.Resolution_280_210:
      size.width = 280;
      size.height = 210;
      break;
    case TTrtcVideoResolution.Resolution_320_240:
      size.width = 320;
      size.height = 240;
      break;
    case TTrtcVideoResolution.Resolution_400_300:
      size.width = 400;
      size.height = 300;
      break;
    case TTrtcVideoResolution.Resolution_480_360:
      size.width = 480;
      size.height = 360;
      break;
    case TTrtcVideoResolution.Resolution_640_480:
      size.width = 640;
      size.height = 480;
      break;
    case TTrtcVideoResolution.Resolution_960_720:
      size.width = 960;
      size.height = 720;
      break;
    case TTrtcVideoResolution.Resolution_160_90:
      size.width = 160;
      size.height = 90;
      break;
    case TTrtcVideoResolution.Resolution_256_144:
      size.width = 256;
      size.height = 144;
      break;
    case TTrtcVideoResolution.Resolution_320_180:
      size.width = 320;
      size.height = 180;
      break;
    case TTrtcVideoResolution.Resolution_480_270:
      size.width = 480;
      size.height = 270;
      break;
    case TTrtcVideoResolution.Resolution_640_360:
      size.width = 640;
      size.height = 360;
      break;
    case TTrtcVideoResolution.Resolution_960_540:
      size.width = 960;
      size.height = 540;
      break;
    case TTrtcVideoResolution.Resolution_1280_720:
      size.width = 1280;
      size.height = 720;
      break;
    case TTrtcVideoResolution.Resolution_1920_1080:
      size.width = 1920;
      size.height = 1080;
      break;
  }
  let resolutionRes = size;
  if (resolutionMode === 1) {
    if (TSession.instance.isAndroidNative() && !TSession.instance.isX5Webview() && TSession.instance.
    isInFaithOrDemo()) {
       resolutionRes =  {
        height: size.height,
        width: size.width,
      };
    } else {
      resolutionRes =  {
        width: size.height,
        height: size.width,
      };
    }
  }
  TMain.instance.reportLog('getVideoSizeFromResolution', `${JSON.stringify(resolutionRes)}`);
  return resolutionRes;
}

export function getResolutionFromVideoSize(size: VideoSize): TTrtcVideoResolution {
  const widthArray = [120, 160, 240, 256, 270, 280, 320, 400, 480, 640, 960, 1280, 1920];
  const heightArray = [90, 120, 144, 160, 180, 210, 240, 270, 300, 360, 480, 540, 720, 1080];
  // eslint-disable-next-line max-len
  const width = widthArray.reduce((prev, curr) => (Math.abs(curr - size.width) < Math.abs(prev - size.width) ? curr : prev));
  // eslint-disable-next-line max-len
  const height = heightArray.reduce((prev, curr) => (Math.abs(curr - size.height) < Math.abs(prev - size.height) ? curr : prev));
  if (width === 120 && height === 120) {
    return TTrtcVideoResolution.Resolution_120_120;
  }
  if (width === 160 && height === 160) {
    return TTrtcVideoResolution.Resolution_160_160;
  }
  if (width === 270 && height === 270) {
    return TTrtcVideoResolution.Resolution_270_270;
  }
  if (width === 480 && height === 480) {
    return TTrtcVideoResolution.Resolution_480_480;
  }
  if (width === 160 && height === 120) {
    return TTrtcVideoResolution.Resolution_160_120;
  }
  if (width === 240 && height === 180) {
    return TTrtcVideoResolution.Resolution_240_180;
  }
  if (width === 280 && height === 210) {
    return TTrtcVideoResolution.Resolution_280_210;
  }
  if (width === 320 && height === 240) {
    return TTrtcVideoResolution.Resolution_320_240;
  }
  if (width === 400 && height === 300) {
    return TTrtcVideoResolution.Resolution_400_300;
  }
  if (width === 480 && height === 360) {
    return TTrtcVideoResolution.Resolution_480_360;
  }
  if (width === 640 && height === 480) {
    return TTrtcVideoResolution.Resolution_640_480;
  }
  if (width === 960 && height === 720) {
    return TTrtcVideoResolution.Resolution_960_720;
  }
  if (width === 160 && height === 90) {
    return TTrtcVideoResolution.Resolution_160_90;
  }
  if (width === 256 && height === 144) {
    return TTrtcVideoResolution.Resolution_256_144;
  }
  if (width === 320 && height === 180) {
    return TTrtcVideoResolution.Resolution_320_180;
  }
  if (width === 480 && height === 270) {
    return TTrtcVideoResolution.Resolution_480_270;
  }
  if (width === 640 && height === 360) {
    return TTrtcVideoResolution.Resolution_640_360;
  }
  if (width === 960 && height === 540) {
    return TTrtcVideoResolution.Resolution_960_540;
  }
  if (width === 1280 && height === 720) {
    return TTrtcVideoResolution.Resolution_1280_720;
  }
  return TTrtcVideoResolution.Resolution_1920_1080;
}

type TTrtcStreamType = 'main' | 'audio' | 'sub' | 'main_small';

/**
 * 视频流统计信息
 */
export class TTrtcRtcInfo {
  /**
   * 用户ID
   */
  UserId: string;

  /**
   * 流类型
   */
  StreamType: TTrtcStreamType;

  /**
   * 分辨率，格式为???x???
   */
  Resolution: string;
}

/**
 * 视频填充模式
 * @enum {number}
 */
export enum TTrtcVideoFillMode {
  /**
   * 等比缩放，填满视窗
   */
  Fill = 0,

  /**
   * 等比缩放，保留黑边
   */
  Fit = 1
}

/**
 * 视频旋转角度
 * @enum {number}
 */
export enum TTrtcVideoRotation {
  /**
   * 不旋转
   */
  Rotation_0 = 0,

  /**
   * 顺时针旋转90度
   */
  Rotation_90 = 1,

  /**
   * 旋转180度
   */
  Rotation_180 = 2,

  /**
   * 顺时针旋转270度
   */
  Rotation_270 = 3
}

/**
 * 镜像类型
 * @enum {number}
 */
export enum TTrtcVideoMirrorType {
  /**
   * 自动：前置摄像头镜像，后置摄像头不镜像
   */
  Auto = 0,

  /**
   * 打开镜像
   */
  Enable = 1,

  /**
   * 关闭镜像
   */
  Disable = 2
}

/**
 * 本地视频参数
 */
export class TTrtcLocalVideoParams {
  /**
   * 设置本地图像的渲染模式
   */
  mode?: TTrtcVideoFillMode;

  /**
   * 设置本地图像的顺时针旋转角度
   */
  rotation?: TTrtcVideoRotation;

  /**
   * 设置视频编码输出的（也就是远端用户观看到的，以及服务器录制下来的）画面方向
   */
  encoderRotation?: TTrtcVideoRotation;

  /**
   * 设置本地摄像头预览画面的镜像模式
   */
  mirror?: TTrtcVideoMirrorType;

  /**
   * 设置编码器输出的画面镜像模式
   */
  encoderMirror?: TTrtcVideoMirrorType;

  /** 设置重力感应的适应模式 : 0 : 关闭重力感应; 1 : 开启重力感应，需要您的 App 界面已适配重力感应。2: 开启重力感应，适用于您的 App 界面暂未适配重力感应的场景。 */
  sensorMode?: number;
}

export class TTrtcRemoteStatistics {
  /**
   * 用户 ID
   */
  userId: string;
  /**
   * 上行丢包率（％）
   */
  upLoss: number;
}

/**
 * 网络统计数据
 */
export class TTrtcStatistics {
  /**
   * 上行丢包率（％）
   */
  upLoss: number;
  /**
   * 下行丢包率（％）
   */
  downLoss: number;
  /**
   * 上行视频总像素
   */
  upVideoPixels: number;
  /**
   * 下行视频总像素
   */
  downVideoPixels: number;
  /**
   * 当前应用的 CPU 使用率（％）
   */
  appCpu: number;
  /**
   * 当前系统的 CPU 使用率（％）
   */
  systemCpu: number;
  /**
   * 延迟（毫秒）
   */
  rtt: number;
  /**
   * 远程网络统计数据
   */
  remoteStatisticsArray: TTrtcRemoteStatistics[];
}

/**
 * 设备信息
 */
export interface TTrtcDeviceInfo {
  /**
   * 设备类型
   */
  type: TTrtcDeviceType;

  /**
   * 设备 ID
   */
  deviceId: string;

  /**
   * 设备名字
   */
  deviceName: string;
}

/**
 * 截图结果
 */
export class TTrtcSnapshot {
  /**
   * 截图数据, base64 string
   */
  data: string;

  /**
   * 截图画面的宽度
   */
  width: number;

  /**
   * 截图画面的高度
   */
  height: number;
}


/**
 * 屏幕共享图缓存
 * @param {ArrayBuffer} buffer 图内容
 * @param {number}      length 图缓存大小
 * @param {number}      width  图宽
 * @param {number}      height 图高
 */
export class TImageBuffer {
  public buffer: ArrayBuffer = undefined;
  public length = 0;
  public width = 0;
  public height = 0;
}

/**
 * 屏幕共享源类型
 * @enum {number}
 */
export enum TScreenCaptureSourceType {
  /**
   * 未知
   */
  Unknown = -1,

  /**
   * 窗口
   */
  Window = 0,

  /**
   * 屏幕
   */
  Screen = 1,

  /**
   * 自定义
   */
  Custom = 2
}

/**
 * 屏幕采集源信息
 * @param {TScreenCaptureSourceType} type       采集源类型
 * @param {String}                   sourceId   采集源ID；对于窗口，该字段指示窗口句柄；对于屏幕，该字段指示屏幕ID
 * @param {String}                   sourceName 采集源名称，UTF8编码
 * @param {TImageBuffer}             thumbBGRA  缩略图内容
 * @param {TImageBuffer}             iconBGRA   图标内容
 */
export class TScreenCaptureSourceInfo {
  public type = TScreenCaptureSourceType.Unknown;
  public sourceId = '';
  public sourceName = '';
  public thumbBGRA: TImageBuffer = undefined;
  public iconBGRA: TImageBuffer = undefined;
  public isMinimizeWindow: Boolean = false;
  // 以下目前仅支持微软 Windows 操作系统
  public width: Number = 0; // 窗口宽度
  public height: Number = 0; // 窗口高度
  public isMainScreen: Boolean = false; // 是否为主显示屏
}

/**
 * 方法名列表，主要用于 PromiseExecutor 的 Action 参数
 * @enum {string}
 */
export enum TTrtcAction {
  CheckSystemRequirements = 'CheckSystemRequirements',
  Init = 'Init',
  UnInit = 'UnInit',
  Join = 'Join',
  Quit = 'Quit',
  SwitchRole = 'SwitchRole',
  GetRtcInfo = 'GetRtcInfo',
  SetLocalVideoParams = 'SetLocalVideoParams',
  SetVideoEncoderParam = 'SetVideoEncoderParam',
  StartLocalVideo = 'StartLocalVideo',
  StopLocalVideo = 'StopLocalVideo',
  MuteLocalVideo = 'MuteLocalVideo',
  StartRemoteVideo = 'StartRemoteVideo',
  StopRemoteVideo = 'StopRemoteVideo',
  SnapshotVideo = 'SnapshotVideo',
  StartLocalAudio = 'StartLocalAudio',
  StopLocalAudio = 'StopLocalAudio',
  MuteLocalAudio = 'MuteLocalAudio',
  MuteRemoteAudio = 'MuteRemoteAudio',
  EnableVolumeEvaluation = 'EnableVolumeEvaluation',
  StartCameraTest = 'StartCameraTest',
  StopCameraTest = 'StopCameraTest',
  StartMicTest = 'StartMicTest',
  StopMicTest = 'StopMicTest',
  StartSpeakerTest = 'StartSpeakerTest',
  StopSpeakerTest = 'StopSpeakerTest',
  GetCameras = 'GetCameras',
  SwitchCamera = 'SwitchCamera',
  GetCameraDeviceId = 'GetCameraDeviceId',
  GetMics = 'GetMics',
  SwitchMic = 'SwitchMic',
  GetMicDeviceId = 'GetMicDeviceId',
  SetMicVolume = 'SetMicVolume',
  GetMicVolume = 'GetMicVolume',
  GetRealMicVolume = 'GetRealMicVolume',
  GetSpeakers = 'GetSpeakers',
  SwitchSpeaker = 'SwitchSpeaker',
  GetSpeakerDeviceId = 'GetSpeakerDeviceId',
  SetSpeakerVolume = 'SetSpeakerVolume',
  GetSpeakerVolume = 'GetSpeakerVolume',
  IsScreenShareSupported = 'IsScreenShareSupported',
  HasScreenCapturePermission = 'HasScreenCapturePermission',
  GetScreenCaptureSources = 'GetScreenCaptureSources',
  SelectScreenCaptureTarget = 'SelectScreenCaptureTarget',
  AddExcludedShareWindows = 'AddExcludedShareWindows',
  AddIncludedShareWindows = 'AddIncludedShareWindows',
  StartScreenShare = 'StartScreenShare',
  PauseScreenShare = 'PauseScreenShare',
  ResumeScreenShare = 'ResumeScreenShare',
  StopScreenShare = 'StopScreenShare',
  EnableSystemAudioLoopback = 'EnableSystemAudioLoopback',
  CreateNativeDom = 'CreateNativeDom',
  StartMusic = 'StartMusic',
}

export interface TStreamStatus {
  join?: boolean,         // 是否已加入房间
  audio?: boolean,        // 音频推流
  video?: boolean,        // 视频推流
  screen?: boolean,       // 屏幕分享推流
  screenAudio?: boolean,  // 屏幕分享混入系统音量
  screenPaused?: boolean, // 屏幕分享已暂停
  mirror?: boolean,       // 是否使用镜像
}

export interface TCacheDeviceId {
  audioId?: null | string,        // 音频设备id
  videoId?: null | string,        // 本地主视频设备ID
  speakerId?: null | string,       // 扬声器设备ID
  subAudioId?: null | string,  // 辅路音频设备ID
  subVideoId?: null | string, // 辅路摄像头设备id,[屏幕分享无需设备ID]
}


/**
 * 参考 https://cloud.tencent.com/document/product/647/38552
 * @param errCode 错误码
 * @returns 错误信息
 */
export function getNativeErrorInfo(errCode: number) {
  let deviceStatus = TDeviceStatus.Fail;
  let errMsg = '';
  switch (errCode) {
    // 摄像头错误
    case -1301:
      errMsg = i18next.t('打开摄像头失败');
      break;
    case -1314:
      deviceStatus = TDeviceStatus.No_Permission;
      errMsg = i18next.t('没有权限打开摄像头');
      break;
    case -1316:
      deviceStatus = TDeviceStatus.Busy;
      errMsg = i18next.t('摄像头被占用');
      break;
    // 麦克风错误
    case -1302:
      errMsg = i18next.t('打开麦克风失败');
      break;
    case -1317:
      deviceStatus = TDeviceStatus.No_Permission;
      errMsg = i18next.t('没有权限打开麦克风');
      break;
    case -1319:
      deviceStatus = TDeviceStatus.Busy;
      errMsg = i18next.t('麦克风被占用');
      break;
    case -1320:
      errMsg = i18next.t('关闭麦克风失败');
      break;
    // 扬声器错误
    case -1321:
      errMsg = i18next.t('打开扬声器失败');
      break;
    case -1323:
      errMsg = i18next.t('关闭扬声器失败');
      break;
    // 屏幕共享错误
    case -1308:
    case -1309:
    case -102015:
    case -102016:
      errMsg = i18next.t('屏幕共享失败');
      break;
    case -7001:
      deviceStatus = TDeviceStatus.Busy;
      errMsg = i18next.t('屏幕共享失败，系统录制被占用');
      break;
    // 摄像头警告
    case 1111:
      deviceStatus = TDeviceStatus.Not_Found;
      errMsg = i18next.t('打开摄像头失败，未找到设备');
      break;
    case 1112:
      deviceStatus = TDeviceStatus.No_Permission;
      errMsg = i18next.t('没有权限打开摄像头');
      break;
    // 麦克风警告
    case 1201:
      deviceStatus = TDeviceStatus.Not_Found;
      errMsg = i18next.t('打开麦克风失败，未找到设备');
      break;
    case 1203:
      deviceStatus = TDeviceStatus.No_Permission;
      errMsg = i18next.t('没有权限打开麦克风');
      break;
    case 1204:
      deviceStatus = TDeviceStatus.Error_1204;
      errMsg = i18next.t('无法使用麦克风');
      break;
    // 扬声器警告
    case 1202:
      deviceStatus = TDeviceStatus.Not_Found;
      errMsg = i18next.t('打开扬声器失败，未找到设备');
      break;
    case 1205:
      deviceStatus = TDeviceStatus.Busy;
      errMsg = i18next.t('音频播放设备被占用或损坏');
      break;
  }
  return { deviceStatus, errMsg };
}

const abnormalStatusList = [
  TDeviceStatus.Not_Found,
  TDeviceStatus.No_Permission,
  TDeviceStatus.Busy,
  TDeviceStatus.Not_Supported,
];
export function isDeviceAbnormal(deviceStatus: TDeviceStatus) {
  return abnormalStatusList.includes(deviceStatus);
}

export abstract class TTrtcBase extends TPromiseModule {
  // 设备
  protected _devices: TTrtcDeviceInfo[] = [];
  protected _micVolume = 100;
  protected _micVolumeWhenOn = 100;
  protected _cameraTestStarted = false;
  protected _micTestStarted = false;
  protected _cameraStarted = false;
  protected _micStarted = false;
  // 设备缓存
  protected _cacheDevice: TCacheDeviceId = {
    audioId: null,
    videoId: null,
    speakerId: null,
    subAudioId: null,
    subVideoId: null,
  };

  protected _publishStatus: TStreamStatus = {
    join: false,
    audio: true,
    video: true,
    screen: false,
    screenAudio: false,
    screenPaused: false,
  };

  protected _updatePublishState(state: TMainState.Video_Publish | TMainState.Audio_Publish) {
    let stateValue = false;
    if (this._publishStatus.join) {  // 没有进房，无法推流
      if (state === TMainState.Video_Publish) {
        // 本地采集已经开启，并且mute=false，则处于推流状态
        stateValue = this._publishStatus.video
          && TState.instance.getState(TMainState.Video_Capture);
      } if (state === TMainState.Audio_Publish) {
        // 本地采集已经开启，并且mute=false，且麦克风音量大于0，则处于推流状态
        stateValue = this._publishStatus.audio
          && TState.instance.getState(TMainState.Audio_Capture)
          && this._micVolume > 0;
      }
    }
    TState.instance.setState(state, stateValue, this._getClassName());
  }

  protected _updateDeviceStatusByDeviceList(action: string) {
    const oldCameraStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const hasCamera = this._devices?.find(device => device.type === TTrtcDeviceType.Camera);
    if (oldCameraStatus === TDeviceStatus.Not_Found && hasCamera) {
      this._info(action, `has camera device, Video_Device_Status ${oldCameraStatus} -> ${TDeviceStatus.Closed}`);
      this._setState(TMainState.Video_Device_Status, TDeviceStatus.Closed);
    } else if (oldCameraStatus !== TDeviceStatus.Not_Found && !hasCamera) {
      this._info(action, `no camera device, Video_Device_Status ${oldCameraStatus} -> ${TDeviceStatus.Not_Found}`);
      this._setState(TMainState.Video_Device_Status, TDeviceStatus.Not_Found);
    }

    const oldMicStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const hasMic = this._devices?.find(device => device.type === TTrtcDeviceType.Mic);
    if (oldMicStatus === TDeviceStatus.Not_Found && hasMic) {
      this._info(action, `has mic device, Audio_Device_Status ${oldMicStatus} -> ${TDeviceStatus.Closed}`);
      this._setState(TMainState.Audio_Device_Status, TDeviceStatus.Closed);
    } else if (oldMicStatus !== TDeviceStatus.Not_Found && !hasMic) {
      this._info(action, `no mic device, Audio_Device_Status ${oldMicStatus} -> ${TDeviceStatus.Not_Found}`);
      this._setState(TMainState.Audio_Device_Status, TDeviceStatus.Not_Found);
    }
  }

  /**
   * 更新状态
   * @protected
   */
  protected _setState(state: TMainState, value: any) {
    TState.instance.setState(state, value, this._getClassName());
  }

  /**
   * 更新未定义在 TMainState 的状态
   * @protected
   */
  protected _setTrtcState(state: string, value: any) {
    TState.instance.setState(state, value, this._getClassName());
  }

  // 获取类名
  protected _getClassName() {
    return 'TTrtcBase';
  }

  // *******************************************主流程控制*******************************************
  /**
   * 检测当前环境是否支持
   */
  abstract checkSystemRequirements(): Promise<any>;

  /**
   * 模块初始化
   * @param sdkAppId  sdkAppId
   * @param userId    用户帐号
   * @param userSig   用户签名
   * @param mode      TRTC模式
   */
  abstract init(sdkAppId: number, userId: string, userSig: string, mode: TTrtcMode): Promise<void>;

  /**
   * 模块反初始化
   */
  abstract unInit(): Promise<void>;

  /**
   * 加入音视频房间
   * @param roomId    音视频房间 ID
   * @param isAnchor  是否主播角色
   */
  abstract join(roomId: number, isAnchor: boolean): Promise<void>;

  /**
   * 退出音视频房间
   */
  abstract quit(): Promise<void>;

  /**
   * 切换用户角色
   * @param isAnchor    是否主播角色
   */
  abstract switchRole(isAnchor: boolean): Promise<void>;

  /**
   * 获取上报用的是RTCInfo
   */
  abstract getRtcInfo(): any;

  // *******************************************视频控制*******************************************
  /**
   * 设置本地视频参数
   * @param option      本地视频参数
   */
  abstract setLocalVideoParams(option: TTrtcLocalVideoParams): Promise<void>;

  /**
   * 设置远端视频参数
   * @param option      本地视频参数
   */
  abstract setRemoteVideoParams(option: TTrtcLocalVideoParams, userId: string, streamType: TStreamType): Promise<void>;

  /**
   * 设置视频编码参数
   * @param resolution  编码分辨率
   * @param fps         编码FPS
   * @param bitrate     编码码率
   * @param resolutionMode 视频宽高比模式
   */
  abstract setVideoEncoderParam(
    resolution: TTrtcVideoResolution, fps: number, bitrate: number, resolutionMode: 0 | 1
  ): Promise<void>;

  /**
   * 设置
   */
  abstract setNetworkQosParam(preference: TRTCVideoQosPreference): Promise<void>;

  /**
   * 设置视频resolutionMode
   * @param resolutionMode  横屏画面还是竖屏画面
   */
  abstract setVideoResolutionMode(resolutionMode: 0 | 1): Promise<void>;

  /**
   * 开启本地视频采集及渲染
   * @param dom         用于渲染视频画面的DOM节点
   */
  abstract startLocalVideo(dom: HTMLElement): Promise<void>;

  /**
   * 关闭本地视频采集及渲染
   */
  abstract stopLocalVideo(): Promise<void>;

  /**
   * 控制是否屏蔽自己的视频画面，屏蔽后不推流
   * @param mute        是否屏蔽
   */
  abstract muteLocalVideo(mute: boolean): Promise<void>;

  /**
   * 开始接收并渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param isFitMode   是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  abstract startRemoteVideo(
    userId: string, type: TTrtcVideoStreamType, dom: HTMLElement, isFitMode?: boolean
  ): Promise<void>;

  /**
   * 更新远端视频画面渲染模式
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param isFitMode    是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  abstract updateRemoteVideoFitMode(userId: string, type: TTrtcVideoStreamType, isFitMode: boolean): Promise<void>;

  /**
   * 停止接收和渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  abstract stopRemoteVideo(userId: string, type: TTrtcVideoStreamType): Promise<void>;

  /**
   * 视频截图
   * @param userId      要处理的用户ID
   * @param streamType  要处理的视频流类型
   */
  abstract snapshotVideo(userId: string, streamType: TTrtcVideoStreamType): Promise<TTrtcSnapshot>;

  /**
   * 暂停渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  abstract pauseVideoRender(userId: string, streamType: TTrtcVideoStreamType): Promise<void>;

  /**
   * 恢复渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  abstract resumeVideoRender(userId: string, streamType: TTrtcVideoStreamType): Promise<void>;

  /**
   * 重置视频渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  abstract resetVideoRender(userId: string, type: TTrtcVideoStreamType): void;

  // *******************************************音频控制*******************************************
  /**
   * 开始本地音频采集
   * @param highAudioQuality   是否为高清音质
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  abstract startLocalAudio(highAudioQuality: boolean, dom?: HTMLElement): Promise<void>;

  /**
   * 停止本地音频采集
   */
  abstract stopLocalAudio(): Promise<void>;

  /**
   * 控制是否屏蔽自己的声音
   * @param mute        是否屏蔽
   */
  abstract muteLocalAudio(mute: boolean): Promise<void>;

  /**
   * 控制是否屏蔽远端的声音
   * @param userId      要处理的用户ID
   * @param mute        是否屏蔽
   */
  abstract muteRemoteAudio(userId: string, mute: boolean): Promise<void>;

  /**
   * 开启音量大小回调，回调直接通过事件抛出
   * @param interval    回调间隔(最小100ms，0为关闭)
   */
  abstract enableVolumeEvaluation(interval: number): Promise<void>;

  // *******************************************设备检测*******************************************
  /**
   * 开始摄像头设备测试
   * @param dom         用于渲染摄像头画面的DOM节点，不传入表示只是打开摄像头，但是不渲染
   */
  abstract startCameraTest(dom?: HTMLElement): Promise<void>;

  /**
   * 停止摄像头设备测试
   */
  abstract stopCameraTest(): Promise<void>;

  /**
   * 开始麦克风设备测试
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  abstract startMicTest(dom?: HTMLElement): Promise<void>;

  /**
   * 停止麦克风设备测试
   */
  abstract stopMicTest(): Promise<void>;

  /**
   * 开启扬声器设备测试
   * @param path        要播放的声音文件路径
   */
  abstract startSpeakerTest(path: string): Promise<void>;

  /**
   * 停止扬声器设备测试
   */
  abstract stopSpeakerTest(): Promise<void>;

  // *******************************************设备管理*******************************************
  /**
   * 获取摄像头设备列表
   */
  abstract getCameras(): Promise<TTrtcDeviceInfo[]>;

  /**
   * 切换使用的摄像头设备
   * @param deviceId      要切换的设备ID
   */
  abstract switchCamera(deviceId: string): Promise<void>;

  /**
   * 获取正在使用的摄像头设备ID
   */
  abstract getCameraDeviceId(): Promise<string>;

  /**
   * 获取麦克风设备列表
   */
  abstract getMics(): Promise<TTrtcDeviceInfo[]>;

  /**
   * 切换使用的麦克风设备
   * @param deviceId      要切换的设备ID
   */
  abstract switchMic(deviceId: string): Promise<void>;

  /**
   * 获取正在使用的麦克风设备ID
   */
  abstract getMicDeviceId(): Promise<string>;

  /**
   * 设置正在使用的麦克风设备音量
   * @param volume        要设置的音量大小
   */
  abstract setMicVolume(volume: number): Promise<void>;

  /**
   * 获取正在使用的系统采集音量
   */
  abstract getMicVolume(): Promise<number>;

  /**
   * 获取正在使用的麦克风设备音量
   */
  abstract getRealMicVolume(): Promise<number>;

  /**
   * 获取扬声器设备列表
   */
  abstract getSpeakers(): Promise<TTrtcDeviceInfo[]>;

  /**
   * 切换使用的扬声器设备
   * @param deviceId      要切换的设备ID
   */
  abstract switchSpeaker(deviceId: string): Promise<void>;

  /**
   * 获取正在使用的扬声器设备ID
   */
  abstract getSpeakerDeviceId(): Promise<string>;

  /**
   * 设置正在使用的扬声器设备音量
   * @param volume        要设置的音量大小
   */
  abstract setSpeakerVolume(volume: number): Promise<void>;

  /**
   * 获取正在使用的扬声器设备音量
   */
  abstract getSpeakerVolume(): Promise<number>;

  // *******************************************屏幕分享*******************************************

  /**
   * 检查当前平台是否支持屏幕分享
   */
  abstract isScreenShareSupported(): Promise<boolean>;

  /**
   * 检查屏幕分享权限
   */
  abstract hasScreenCapturePermission(): Promise<boolean>;

  /**
   * 获取屏幕分享屏幕采集源列表
   */
  abstract getScreenCaptureSources(): Promise<TScreenCaptureSourceInfo[]>;

  /**
   * 选择要进行屏幕分享的目标采集源
   * @param source        要分享的采集源
   * @param captureMouse        是否捕获鼠标
   * @param highlightWindow        是否高亮选择区域
   */
  abstract selectScreenCaptureTarget(
    source: TScreenCaptureSourceInfo,
    captureMouse: boolean,
    highlightWindow: boolean): Promise<void>;

  /**
   * 进行屏幕分享的目标采集源
   */
  abstract getScreenCaptureTarget(): TScreenCaptureSourceInfo;

  /**
   * 将指定窗口加入屏幕分享的排除列表中
   */
  abstract addExcludedShareWindows(sourceIds: string[]): Promise<void>;

  /**
   * 将指定窗口加入屏幕分享的列表中
   */
  abstract addIncludedShareWindows(sourceIds: string[]): Promise<void>;

  /**
   * 开始屏幕分享t
   * @param dom           用于渲染分享画面的DOM节点，不需要渲染可以忽略
   */
  abstract startScreenShare(dom?: HTMLElement, opts?: {videoSource: any}): Promise<void>;

  /**
   * 暂停屏幕分享
   */
  abstract pauseScreenShare(): Promise<void>;

  /**
   * 设置屏幕分享编码参数
   */
  abstract setSubStreamEncoderParam(params: Partial<{
    videoResolution: TTrtcVideoResolution,
    resMode: 0 | 1,
    videoFps: number,
    videoBitrate: number,
    enableAdjustRes: boolean,
    screenCaptureMode: number,
  }>): Promise<void>;

  /**
   * 恢复屏幕分享
   */
  abstract resumeScreenShare(): Promise<void>;

  /**
   * 停止屏幕分享
   */
  abstract stopScreenShare(): Promise<void>;

  /**
   * 获取屏幕分享流
   */

  abstract getScreenShareStream(): any;
  /**
   * 控制是否分享系统声音
   * @param enable        是否分享
   */
  abstract enableSystemAudioLoopback(enable: boolean): Promise<void>;

  /**
   * 播放背景音乐
   */
  abstract startMusic(documentId: string, url: string): Promise<boolean>;

  /**
   * 停止背景音乐
   */
  abstract stopMusic(): void;

  /**
   * 暂停背景音乐
   */
  abstract pauseMusic(): void;

  /**
   * 恢复背景音乐
   */
  abstract resumeMusic(): void;

  /**
   * 获取音乐时长，单位毫秒
   */
  abstract getMusicDuration(): number;

  /**
   * 设置背景音乐进度
   */
  abstract seekMusic(pts: number): void;

  /**
   * 设置背景音乐的音量大小
   */
  abstract setMusicVolume(volume: number): void;

  /**
   * 开始自定义音视频采集
   */
  abstract startCaptureStream(elementId: string): Promise<void>;

  /**
   * 停止自定义音视频采集
   */
  abstract stopCaptureStream(): Promise<void>;

  /**
   * 加载视频
   */
  abstract loadVideo(dom: HTMLElement, url: string): Promise<void>;

  /**
   * 开始播放视频
   */
  abstract playVideo(): Promise<void>;

  /**
   * 暂停播放视频
   */
  abstract pauseVideo(): Promise<void>;

  /**
   * 视频进度跳转
   */
  abstract seekVideo(time: number): Promise<void>;

  /**
   * 结束播放视频
   */
  abstract stopVideo(): Promise<void>;

  /**
   * 设置音量大小
   */
  abstract setVideoVolume(volume: number): Promise<void>;

  /**
   * 打开辅助摄像头
   */
  abstract startSubCamera(dom: HTMLElement, deviceIndex: number, resolution: TTrtcVideoResolution): Promise<void>;

  /**
   * 关闭辅助摄像头
   */
  abstract stopSubCamera(): Promise<void>;
  // *******************************************美颜相关*******************************************
  /**
   * 设置虚拟背景
   * @param enable  开关
   * @param url   虚拟背景(为空时为背景虚化)
   */
  abstract setVirtualImg(enable: boolean, url: string, sceneKey: string): Promise<void>;
  /**
   * 设置美颜
   * @param beauty  美颜度( 0 - 10 ，推荐为 5 )
   * @param brightness 	明亮度( 0 - 1 ，，推荐为 5 )
   * @param ruddy 红润度( 0 - 1 ，，推荐为 5 )
   */
  abstract setBeautyParam(beauty: number, brightness: number, ruddy: number): Promise<void>;
  /**
   * 设置虚拟形象
   * @param effectId 特效id
   * @param url   虚拟背景url,可选,不填的话内置一个
   */
  abstract setAvatar(effectId: string, url?: string): Promise<void>;
  // *******************************************降噪相关*******************************************
  /**
   * 检查当前平台是否支持AI降噪
   */
  abstract isAIDenoiseSupported(): boolean;

  /**
   * 开启AI降噪
   * @param enable    是否开启
   */
  abstract enableAIDenoise(enable: boolean): Promise<void>;
  /**
   * 检查当前平台是否支持webrtc
   */
  abstract isWebRTCSupport(): boolean;

  abstract removeLogCallback(): void;
}


/**
* 填充音量信息
*/
export function paddingVolumeInfo(infos: Map<string, number[]>, opts: {uid: string, val: number, spectrumData?: []}) {
  if (!opts.uid || !infos) {
    return;
  }
  let info = infos.get(opts.uid);
  // 最多保留100个音量采样点
  const MAX  = 100;
  if (!info) {
    infos.set(opts.uid, [opts.val]);
    info = [];
  }
  if (info.length >= MAX) {
    info.shift();
  }
  info.push(opts.val);
}
