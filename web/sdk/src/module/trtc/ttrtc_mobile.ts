import i18next from 'i18next';
import { TEvent } from '../../base/tevent';
import { TMainEvent, TMainState, TDeviceStatus } from '../../constants';
import { TSession } from '../tsession';
import { TState } from '../tstate';
import {
  TScreenCaptureSourceInfo,
  TTrtcAction,
  TTrtcBase,
  TTrtcDeviceInfo,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcLocalVideoParams,
  TTrtcMode,
  TTrtcRtcInfo,
  TTrtcSnapshot,
  TTrtcVideoResolution,
  TTrtcVideoStreamType,
  TTrtcStatistics,
  TTrtcRemoteStatistics,
  TTrtcDeviceState,
  getNativeErrorInfo,
  isDeviceAbnormal,
  getVideoSizeFromResolution,
  paddingVolumeInfo,
  TRTCVideoQosPreference,
  TTrtcVideoMirrorType,
} from './ttrtc_base';
import { TWebView } from '../twebview_inner';
import { TRTCMobileError as TCICError } from '../../base/tmodule';
import { TMain } from '../tmain';
import { TStreamType } from '../business/tbusiness_member';

enum TTrtcViewType {
  ViewType_Audio = -1,
  ViewType_Camera = 0,
  ViewType_Small = 1,
  ViewType_Screen = 2,
  ViewType_FastLive = 3,
}

enum TTrtcCameraID {
  CameraID_Front = 'front',
  CameraID_Back = 'back',
}

class TTrtcRenderInfo {
  mUserId: string;
  mViewType: TTrtcViewType;
  mDom: any;
  mFitMode: boolean;
  mIsRendering: boolean;

  constructor(userId: string, viewType: TTrtcViewType, dom: any, fitMode: boolean) {
    this.mUserId = userId;
    this.mViewType = viewType;
    this.mDom = dom;
    this.mFitMode = fitMode;
    this.mIsRendering = false;
  }
}

class TTrtcVideoInfo {
  userId: string;
  streamType: TTrtcVideoStreamType;
  width: number;
  height: number;

  constructor(userId: string, type: TTrtcVideoStreamType, width: number, height: number) {
    this.userId = userId;
    this.streamType = type;
    this.width = width;
    this.height = height;
  }
}

export class TTrtcMobile extends TTrtcBase {
  // *******************************************静态方法*******************************************
  private static mInstance: TTrtcMobile;

  /**
   * 获取 SDK JS，移动端不需要
   */
  public static getSDKJS(): string {
    return '';
  }
  /**
   * 获取插件列表
   */
  public static getPluginList(): { name: string, url: string }[] {
    return [];
  }

  // 创建同屏组件样式
  private static createNativeStyle() {
    const style = document.createElement('style');
    style.id = 'native-css';
    document.head.appendChild(style);

    if (TSession.instance.isIOSNative()) {
      style.textContent += '.native_render_view {'
        /* 忽略所有手势操作，避免拖动时子节点会移动*/
        + 'pointer-events: none;'
        /* 以下两个属性保证nativeRenderContent节点大于nativeRenderView节点时会出现滚动条，进而native侧会生成对应的同层渲染View可供渲染视频*/
        + 'overflow: scroll;'
        + '-webkit-overflow-scrolling: touch;'
        /* 保证宽度填满native_render_wrapper节点，由.native_render_wrapper节点设置了align-items: stretch，不指定height高度也会默认填满*/
        + 'width: 100%;'
        + 'height: 100%;'
        + 'pointer-events: none !important;'
        + ' }\n';

      style.textContent += '.native_render_content {'
        /* 保证高度大于nativeRenderView，进而native侧会生成对应的同层渲染View可供渲染视频*/
        + 'display: block;'
        + 'width: 100%;'
        + 'height: 100%;'
        + 'padding-top: 1px;'
        + 'box-sizing: content-box;'
        + ' }';
    } else if (TSession.instance.isAndroidNative()) {
      style.textContent += '.native_render_view {'
        /* 忽略所有手势操作，避免拖动时子节点会移动*/
        + 'pointer-events: none;'
        + 'width: 100%;'
        + 'height: 100%;'
        + ' }\n';
    }
  }

  // 获取网络质量等级
  private static getNetworkLevel(loss: number) {
    if (loss === 0) {
      return 1;
    }
    if (loss < 3) {
      return 2;
    }
    if (loss < 5) {
      return 3;
    }
    if (loss < 10) {
      return 4;
    }
    return 5;
  }

  // *******************************************内部变量*******************************************
  private mSdkAppId = 0;
  private mRoomId = 0;
  private mUserId = '';
  private mUserSig = '';
  private mAppMode = '';
  private mIsFrontCamera = true;
  private mNeedSwitchCameraAfterJoin = false;
  private mTrtcTimer: any = null;
  private mEncodeParams: any = null;
  private mRtcInfo: any = [];
  private _volumeInfo: Map<string, number[]> = new Map();
  private mRenderMap = new Map<string, TTrtcRenderInfo>();
  private mOpenCamera = false;
  private mHasEnableMic = false;   // 本地是否开启过Mic(Android进房，不开Mic也会上抛onMicDidReady事件，需要忽略)
  private mIsSwitchRole = false;   // 正在切换角色
  private mHeartbeatTimer = 0;   // 心跳定时器
  private mRenderTask: any = [];     // 同展渲染队列
  private mCurRenderTask: string = null;    // 当前渲染任务
  private mLastWarnCode = 0;         // 上次警告码
  private mLastWarnTimeStamp = 0;    // 上次上报警告时间

  // *******************************************主流程控制*******************************************
  public constructor() {
    super();
    TTrtcMobile.mInstance = this;  // 保存实例
    // 监听事件
    const events = ['onEnterRoom', 'onExitRoom', 'onSwitchRole', 'onUserAudioAvailable', 'onUserVideoAvailable', 'onUserSubStreamAvailable', 'onScreenCaptureStarted',
      'onScreenCaptureStopped', 'onScreenCapturePaused', 'onScreenCaptureResumed', 'onMicDidReady', 'onCameraDidReady', 'onError', 'onBackPressed', 'onScreenKitEvent',
      'onConnectionLost', 'onConnectionRecovery'];
    const sEvents = ['onWarning', 'onUserVoiceVolume', 'onNetworkQuality', 'onStatistics'];   // 不上报日志的事件
    const callbacks: any[] = [];
    const self: any = this;
    // 普通事件
    events.forEach((name) => {
      if (self[name] instanceof Function) { // 有定义方法
        callbacks.push({
          sdkcbName: name,
          jsRecvcbName: TWebView.instance.generateStaticCb(name, self[name]),
        });
      } else {
        this._warn('init', `listener not found: ${name}`);
      }
    });
    // 不上报事件
    sEvents.forEach((name) => {
      if (self[name] instanceof Function) { // 有定义方法
        callbacks.push({
          sdkcbName: name,
          jsRecvcbName: TWebView.instance.generateStaticCb(name, self[name], false),
        });
      } else {
        this._warn('init', `listener not found: ${name}`);
      }
    });
    this.innerNativeCall('trtc', 'registerCallBacks', { callbacks });
    // iOS端关闭重力感应, 已移动至setLocalVideoParams内部
    if (TSession.instance.isIOSNative()) {
      const params = new TTrtcLocalVideoParams();
      this.setLocalVideoParams(params);
    }
    // 这里不清楚为啥安卓不用设置本地视频渲染参数，但是为了解决阿卡索的12.4的bug，也是设置一下。
    if (TSession.instance.isAndroidNative() && TSession.instance.getSchoolId().toString() == '3594898') {
      const params = new TTrtcLocalVideoParams();
      this.setLocalVideoParams(params);
    }
    // 和其他端保持一致，有 _devices
    const micPromise: Promise<TTrtcDeviceInfo[]> = this.getMics().catch(_err => []);
    const cameraPromise: Promise<TTrtcDeviceInfo[]> = this.getCameras().catch(_err => []);
    const speakerPromise: Promise<TTrtcDeviceInfo[]> = this.getSpeakers().catch(_err => []);
    Promise.all([micPromise, cameraPromise, speakerPromise]).then(([cameras, mics, speakers]) => {
      this._devices = [
        ...cameras,
        ...mics,
        ...speakers,
      ];
      this._info('initDevices', JSON.stringify(this._devices));
    });
  }

  public removeLogCallback(): void {
    console.log('Method not support');
  }


  /**
   * 检测当前环境是否支持
   */
  public checkSystemRequirements(): Promise<any> {
    return Promise.resolve({
      result: true,
    });
  }

  public isWebRTCSupport(): boolean {
    return true;
  }

  /**
   * 模块初始化
   * @param sdkAppId  sdkAppId
   * @param userId    用户帐号
   * @param userSig   用户签名
   * @param mode      TRTC模式
   */
  public init(sdkAppId: number, userId: string, userSig: string, mode = TTrtcMode.RTC): Promise<void> {
    const executor = this.newExecutor(TTrtcAction.Init);
    setTimeout(() => {
      this._info('init', `${sdkAppId}, ${userId}`);
      this.mSdkAppId = sdkAppId;
      this.mUserId = userId;
      this.mUserSig = userSig;
      this.mAppMode = mode;

      // 初始化同层渲染相关
      TTrtcMobile.createNativeStyle();
      this.innerNativeCall('tcicui', 'registerRender', {
        addRender: TWebView.instance.generateStaticCb('addRender', this.onAddRender),
        removeRender: TWebView.instance.generateStaticCb('removeRender', this.onRemoveRender),
        renderCss: 'native_render_view',
        loginUserId: this.mUserId,
      });
      // 创建与Native的心跳定时器
      if (this.mHeartbeatTimer) {
        clearInterval(this.mHeartbeatTimer);
      }
      this.mHeartbeatTimer = window.setInterval(() => {
        this.innerNativeCall('trtc', 'classHeartBeat', {});
      }, 5000);
      executor.resolve();
    }, 0);
    return executor.getPromise();
  }

  /**
   * 模块反初始化
   */
  public unInit(): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 加入音视频房间
   * @param roomId    音视频房间 ID
   * @param isAnchor  是否主播角色
   */
  public join(roomId: number, isAnchor: boolean): Promise<void> {
    this._info('join', `${roomId}, ${isAnchor}`);
    this.mRoomId = roomId;
    this.innerNativeCall('trtc', 'enterRoom', {
      sdkAppId: this.mSdkAppId,
      userId: this.mUserId,
      userSig: this.mUserSig,
      roomId,
      role: isAnchor ? 20 : 21,
      appScene: this.mAppMode === TTrtcMode.RTC ? 0 : 1,
    }, (params: any) => {
      this._info('join', `rsp: ${JSON.stringify(params)}`);
      if (params && params.retval && params.retval.elapsed > 0) {   // elapse>0则为进房耗时,否则为失败
        this.onEnterRoom(params);
      } else {
        this.rejectPromise(TTrtcAction.Join, new TCICError(params.errinfo.errcode, params.errinfo.errmsg, i18next.t('进入音视频房间失败')));
      }
    });
    return this.newPromise(TTrtcAction.Join, '', true, 20000);
  }

  /**
   * 退出音视频房间
   */
  public quit(): Promise<void> {
    this._info('quit', `${this.mRoomId}`);
    const timeout = 5 * 1000;
    return new Promise((resolve, reject) => {
      // 回收音视频资源
      this.stopLocalVideo();
      this.stopLocalAudio();
      /**
       * 避免客户端不回调导致无响应
       */
      setTimeout(() => {
        reject('timeout');
      }, timeout);
      this.innerNativeCall('trtc', 'exitRoom', {}, () => {
        resolve();
      });
    });
  }

  /**
   * 切换用户角色
   * @param isAnchor    是否主播角色
   */
  public switchRole(isAnchor: boolean): Promise<void> {
    this._info('switchRole', `enter with status: ${this.mIsSwitchRole}`);
    if (!this.mIsSwitchRole) {
      this.mIsSwitchRole = true;
      this.innerNativeCall('trtc', 'switchRole', {
        role: isAnchor ? 20 : 21,
      }, (pararms: any) => {
        this.mIsSwitchRole = false;
        this._reportEvent('switch_role', pararms);
        this.resolvePromise(TTrtcAction.SwitchRole, {}, null, true);
      });
    }
    return this.newPromise(TTrtcAction.SwitchRole, null, false);
  }

  /**
   * 获取上报用的是RTCInfo
   */
  public getRtcInfo() {
    return { video: this.mRtcInfo, audio: this?._volumeInfo };
  }

  // eslint-disable-next-line @typescript-eslint/member-ordering
  private customVideoParams = 0;

  // *******************************************视频控制*******************************************
  /**
   * 设置本地视频参数
   * @param option      本地视频参数
   */
  public setLocalVideoParams(option: TTrtcLocalVideoParams & { customVideoParams?: 1 }): Promise<void> {
    let realOption = option;
    if (TWebView.instance.inTVList()) {
      realOption = Object.assign(option, { rotation: 1, sensorMode: 0, mirror: 2 });
    } else {
      realOption = Object.assign(option, {
        rotation: option.rotation || 0,
      });
    }
    // iOS端关闭重力感应
    if (TSession.instance.isIOSNative()) {
      const clientInfo = TMain.instance.getClientInfo();
      const trtcSdkVer = clientInfo.trtcSdkVersion;
      this._info('clientInfo', `${clientInfo}-${trtcSdkVer}`);
      // trtc ios sdk  11.5 之后不用再hack sensorMode 参数.
      if ((trtcSdkVer || '') < '11.5') {
        realOption = Object.assign({ sensorMode: 0 }, option);
      }
    }
    // 设置了customVideoParams参数之后, 如果再次调用没有设置customVideoParams将不生效,
    // 避免客户自定义js中的传参被课堂本身的调用覆盖
    if (this.customVideoParams && !option.customVideoParams) {
      return Promise.resolve();
    }
    if (option.customVideoParams !== undefined) {
      this.customVideoParams = option.customVideoParams;
    }
    if (TSession.instance.getParams('sensorMode') === '-') {
      delete realOption.sensorMode;
    }
    // 关闭镜像编码，临时解决native trtc sdk 12.4画面旋转角度异常的bug,针对阿卡索（3594898）生效
    try {
      const disableMirror = TSession.instance.getSchoolId().toString() == '3594898';
      if (TSession.instance.isAndroidNative()) {
        // @ts-ignore
        realOption.encoderMirror = disableMirror ? false : realOption.mirror === 1;
      }
      if (TSession.instance.isIOSNative()) {
        realOption.encoderMirror = disableMirror ?  0 : (realOption.mirror === 1 ? 1 : 0) ;
      }
    } catch (err) {
      this._error('setLocalPreviewParams', `${JSON.stringify(realOption)}_${err.toString()}`);
    }
    this._info('setLocalPreviewParams', `${JSON.stringify(realOption)}`);
    return new Promise((resolve) => {
      this.innerNativeCall('trtc', 'setLocalPreviewParams', realOption, (_info: any) => {
        resolve();
      });
    });
  }
  /**
   * 设置渲染渲染参数
   */
  public setRemoteVideoParams(
    option: TTrtcLocalVideoParams & { customVideoParams?: 1 },
    userId: string,
    streamType: TStreamType,
  ): Promise<void> {
    let realOption = option;
    if (TWebView.instance.inTVList()) {
      realOption = Object.assign(option, { rotation: 1, sensorMode: 0, mirror: 2 });
    } else {
      realOption = Object.assign(option, {
        rotation: option.rotation || 0,
      });
    }
    realOption = Object.assign(option, {
      userId,
      streamType,
    });
    // iOS端关闭重力感应
    if (TSession.instance.isIOSNative()) {
      const clientInfo = TMain.instance.getClientInfo();
      const trtcSdkVer = clientInfo.trtcSdkVersion;
      this._info('clientInfo', `${clientInfo}-${trtcSdkVer}`);
      // trtc ios sdk  11.5 之后不用再hack sensorMode 参数.
      if ((trtcSdkVer || '') < '11.5') {
        realOption = Object.assign({ sensorMode: 0 }, option);
      }
    }
    // 设置了customVideoParams参数之后, 如果再次调用没有设置customVideoParams将不生效,
    // 避免客户自定义js中的传参被课堂本身的调用覆盖
    if (this.customVideoParams && !option.customVideoParams) {
      return Promise.resolve();
    }
    if (option.customVideoParams !== undefined) {
      this.customVideoParams = option.customVideoParams;
    }
    if (TSession.instance.getParams('sensorMode') === '-') {
      delete realOption.sensorMode;
    }
    return new Promise((resolve) => {
      this.innerNativeCall('trtc', 'setRemotePreviewParams', realOption, (_info: any) => {
        resolve();
      });
    });
  }
  /**
   * 设置视频编码参数
   * @param resolution  编码分辨率
   * @param fps         编码FPS
   * @param bitrate     编码码率
   */
  public setVideoEncoderParam(
    resolution: TTrtcVideoResolution,
    fps: number,
    bitrate: number,
    resMode: 0 | 1,
  ): Promise<void> {
    this.mEncodeParams = {
      resMode,
      videoResolution: resolution,
      videoFps: fps,
      videoBitrate: bitrate,
      enableAdjustRes: false,
    };
    return this.mobileSetVideoEncoderParam();
  }

  async mobileSetVideoEncoderParam() {
    const exp = TMain.instance.getParams('exp');
    const useExp = exp === 'setVideoEncodeParamEx';
    if (useExp) {
      return new Promise<void>((resolve) => {
        // 非最新Android sdk上,没有回调,走超时返回逻辑.
        const timer = setTimeout(() => {
          this._warn('mobileSetVideoEncoderParam', 'timeout resolved');
          resolve();
        }, 1000);
        const params = {
          ...this.mEncodeParams,
          resolutionMode: 2, // 0:横屏 1:竖屏 2:定制设备画面兼容（10.3稳定版or10.8以上版本可使用）
        };
        const size = getVideoSizeFromResolution(params.videoResolution);
        params.videoHeight = size.height;
        params.videoWidth = size.width;

        delete params.resMode;
        this.innerNativeCall(
          'trtc', 'callExperimentalAPI',
          {
            APIParams: JSON.stringify({
              api: 'setVideoEncodeParamEx',
              params,
            }),
          },
          () => {
            this._info('mobileSetVideoEncoderParam', 'normal resolved');
            resolve();
            clearTimeout(timer);
          },
        );
      });
    }
    return new Promise<void>((resolve) => {
      this.innerNativeCall('trtc', 'setVideoEncoderParam', this.mEncodeParams, () => {
        resolve();
      });
    });
  }

  public setVideoResolutionMode(resMode: 0 | 1) {
    this.mEncodeParams = {
      ...this.mEncodeParams,
      resMode,
    };
    return this.mobileSetVideoEncoderParam();
  }

  /**
   * 开启本地视频采集及渲染
   * @param dom         用于渲染视频画面的DOM节点
   */
  public startLocalVideo(dom: HTMLElement): Promise<void> {
    this._info('startLocalVideo', 'enter');
    const streamId = this.innerGetStreamId(this.mUserId, TTrtcViewType.ViewType_Camera);
    if (!this.mRenderMap.has(streamId)) {
      this.mRenderMap.set(streamId, new TTrtcRenderInfo(this.mUserId, TTrtcViewType.ViewType_Camera, dom, false));
      this.innerStartLocalRender(streamId);
      this.mOpenCamera = true;
    }
    return this.newPromise(TTrtcAction.StartLocalVideo, null, false, 0);
  }

  /**
   * 关闭本地视频采集及渲染
   */
  public stopLocalVideo(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalVideo',
      `deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    this.innerUpdateCameraStatus(false, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this.innerStopLocalRender();
    this.mRenderMap.delete(this.innerGetStreamId(this.mUserId, TTrtcViewType.ViewType_Camera));
    this.mOpenCamera = false;
    return Promise.resolve();
  }

  /**
   * 控制是否屏蔽自己的视频画面，屏蔽后不推流
   * @param mute        是否屏蔽
   */
  public muteLocalVideo(mute: boolean): Promise<void> {
    this._info('muteLocalVideo', `enter=>mute: ${mute}`);
    this._publishStatus.video = !mute;
    this.innerNativeCall('trtc', 'muteLocalVideo', { mute, streamType: 0 });
    this._updatePublishState(TMainState.Video_Publish);
    return Promise.resolve(undefined);
  }

  /**
   * 开始接收并渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param isFitMode   是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  public startRemoteVideo(
    userId: string,
    type: TTrtcVideoStreamType,
    dom: HTMLElement,
    isFitMode?: boolean,
  ): Promise<void> {
    const viewType = this.innerGetViewType(type);
    this._info('startRemoteVideo', `enter=>userId: ${userId}, viewType: ${viewType}, isFitMode: ${isFitMode}`);
    const streamId = this.innerGetStreamId(userId, viewType);
    if (!this.mRenderMap.has(streamId)) {
      this.mRenderMap.set(streamId, new TTrtcRenderInfo(userId, viewType, dom, isFitMode));
      this.innerStartRemoteRender(streamId);
    }
    return this.newPromise(TTrtcAction.StartRemoteVideo, streamId, false, 0);
  };

  /**
   * 更新远端视频画面渲染模式
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param isFitMode    是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  public updateRemoteVideoFitMode(userId: string, type: TTrtcVideoStreamType, isFitMode: boolean): Promise<void> {
    const viewType = this.innerGetViewType(type);
    this._info('updateRemoteVideoFitMode', `enter=>userId: ${userId}, viewType: ${viewType}, isFitMode: ${isFitMode}`);
    const streamId = this.innerGetStreamId(userId, viewType);
    const renderInfo = this.mRenderMap.get(streamId);
    if (renderInfo) {
      renderInfo.mFitMode = isFitMode;
      if (renderInfo.mIsRendering) {    // 已经开始渲染了
        if (viewType === TTrtcViewType.ViewType_Screen) {
          this.innerNativeCall('trtc', 'setRemoteSubStreamPreviewParams', {
            userId,
            mode: isFitMode ? 1 : 0,
            rotation: 0,
          });
        } else {
          this.innerNativeCall('trtc', 'setRemotePreviewParams', {
            userId,
            mode: isFitMode ? 1 : 0,
          });
        }
      }
    }
    return Promise.resolve();
  };

  // 重置视频渲染
  public resetVideoRender(userId: string, type: TTrtcVideoStreamType) {
    const viewType = this.innerGetViewType(type);
    const streamId = this.innerGetStreamId(userId, viewType);
    if (this.mRenderMap.has(streamId)) {
      if (userId === this.mUserId) {  // 自己
        this._info('resetLocalVideoRender', `enter=>userId: ${userId}, viewType: ${viewType}`);
        this.innerStopLocalRender();
        setTimeout(() => {    // 延时200毫秒加载
          this.innerStartLocalRender(streamId);
        }, 200);
      } else {
        this._info('resetRemoteVideoRender', `enter=>userId: ${userId}, viewType: ${viewType}`);
        this.innerStopRemoteRender(userId, viewType);
        window.setTimeout(() => {
          this.innerStartRemoteRender(streamId);
        }, 50);
      }
    }
  }

  /**
   * 停止接收和渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  public stopRemoteVideo(userId: string, type: TTrtcVideoStreamType): Promise<void> {
    const viewType = this.innerGetViewType(type);
    this._info('stopRemoteVideo', `enter=>userId: ${userId}, viewType: ${viewType}`);
    const streamId = this.innerGetStreamId(userId, viewType);
    this.innerStopRemoteRender(userId, viewType);
    this.mRenderMap.delete(streamId);   // 最后才移除
    return Promise.resolve(undefined);
  }

  /**
   * 视频截图
   * @param _userId      要处理的用户ID
   * @param _streamType  要处理的视频流类型
   */
  public snapshotVideo(_userId: string, _streamType: TTrtcVideoStreamType): Promise<TTrtcSnapshot> {
    return Promise.resolve(undefined);
  }

  /**
   * 暂停渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  public pauseVideoRender(userId: string, streamType: TTrtcVideoStreamType): Promise<void> {
    if (TSession.instance.isIOSNative()) return;
    if (userId === this.mUserId) {
      this._info('pauseLocalVideoRender', `userId: ${userId}, streamType: ${streamType}`);
      if (streamType === TTrtcVideoStreamType.Big) { // 是自己摄像头则关闭本地预览
        this.innerNativeCall('trtc', 'stopLocalPreview');
      }
    } else {
      const viewType = this.innerGetViewType(streamType);
      this._info('pauseRemoteVideoRender', `userId: ${userId}, viewType: ${viewType}`);
      const streamId = this.innerGetStreamId(userId, viewType);
      if (this.mRenderMap.has(streamId)) {
        if (viewType === TTrtcViewType.ViewType_Screen) {
          this.innerNativeCall('trtc', 'stopRemoteSubStreamView', { userId });
        } else {
          this.innerNativeCall('trtc', 'stopRemoteView', { userId });
        }
      }
    }
    return Promise.resolve();
  }

  /**
    * 恢复渲染
    * @param userId   要处理的用户id
    * @param streamType  要处理的视频流类型
    */
  public resumeVideoRender(userId: string, streamType: TTrtcVideoStreamType): Promise<void> {
    if (TSession.instance.isIOSNative()) return;
    if (userId === this.mUserId) {
      this._info('resumeLocalVideoRender', `userId: ${userId}, streamType: ${streamType}`);
      if (streamType === TTrtcVideoStreamType.Big) { // 是自己摄像头则关闭本地预览
        this.innerNativeCall('trtc', 'startLocalPreview', { frontCamera: this.mIsFrontCamera ? 1 : 0 });
      }
    } else {
      const viewType = this.innerGetViewType(streamType);
      this._info('resumeRemoteVideoRender', `userId: ${userId}, viewType: ${viewType}`);
      const streamId = this.innerGetStreamId(userId, viewType);
      if (this.mRenderMap.has(streamId)) {
        if (viewType === TTrtcViewType.ViewType_Screen) {
          this.innerNativeCall('trtc', 'setRemoteSubStreamPreviewParams', {
            userId,
            mode: 1,
            rotation: 0,
          });
          this.innerNativeCall('trtc', 'startRemoteSubStreamView', { userId });
        } else {
          this.innerNativeCall('trtc', 'startRemoteView', { userId });
        }
      }
    }
    return Promise.resolve();
  }

  // *******************************************音频控制*******************************************
  /**
   * 开始本地音频采集
   * @param highAudioQuality  高清音质
   * @param _dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public startLocalAudio(highAudioQuality: boolean, _dom?: HTMLElement): Promise<void> {
    this._info('startLocalAudio', 'enter');
    if (!this.mHasEnableMic) {    // 记录本地是否开启过麦克风
      this.mHasEnableMic = true;
    }
    const quality = highAudioQuality ? 3 : 2;
    // ipad临时改为高音质模式.
    const isipadNative = TMain.instance.isiPadNative();
    if (isipadNative) {
      this._info('ipad_startLocalAudio_begin', '1');
      // quality = 3;
    }
    this.innerNativeCall('trtc', 'startLocalAudio', { quality }, (params: any) => {
      this._info(isipadNative ? 'ipad_startLocalAudio_result' : 'startLocalAudio', `rsp: ${JSON.stringify(params)}`);
      // 成功在 onMicDidReady，为什么不写在一起？
      if (params && params.errinfo && params.errinfo.errcode !== 0) {   // 不为0则失败
        const { deviceStatus } = getNativeErrorInfo(params.errinfo.errcode);
        this.innerUpdateMicStatus(false, deviceStatus);
        this.rejectPromise(TTrtcAction.StartLocalAudio, new TCICError(params.errinfo.errcode, params.errinfo.errmsg, i18next.t('没有权限打开麦克风')), null, true);
      }
    });
    return this.newPromise(TTrtcAction.StartLocalAudio, null, false);
  }

  /**
   * 停止本地音频采集
   */
  public stopLocalAudio(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalAudio',
      `deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    this.innerUpdateMicStatus(false, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this.innerNativeCall('trtc', 'stopLocalAudio');
    return Promise.resolve(undefined);
  }

  /**
   * 控制是否屏蔽自己的声音
   * @param mute        是否屏蔽
   */
  public async muteLocalAudio(mute: boolean): Promise<void> {
    this._info('muteLocalAudio', `enter=>mute: ${mute}`);
    this._publishStatus.audio = !mute;
    this.innerNativeCall('trtc', 'muteLocalAudio', { mute });
    this._updatePublishState(TMainState.Audio_Publish);
    return Promise.resolve(undefined);
  }

  /**
   * 控制是否屏蔽远端的声音
   * @param userId      要处理的用户ID
   * @param mute        是否屏蔽
   */
  public muteRemoteAudio(userId: string, mute: boolean): Promise<void> {
    this._info('muteRemoteAudio', `
myUserId: ${TSession.instance.getUserId()}
targetUserId: ${userId}
mute: ${mute}
`);

    this.innerNativeCall('trtc', 'muteRemoteAudio', {
      userId,
      mute,
    });
    return Promise.resolve(undefined);
  }

  /**
   * 开启音量大小回调，回调直接通过事件抛出
   * @param interval    回调间隔(最小100ms，0为关闭)
   */
  public enableVolumeEvaluation(interval: number): Promise<void> {
    this._info('enableVolumeEvaluation', `interval: ${interval}`);
    this.innerNativeCall('trtc', 'enableAudioVolumeEvaluation', { interval, enableSpectrumCalculation: true, enablePitchCalculation: true, enable: true });
    return Promise.resolve(undefined);
  }

  // *******************************************设备检测*******************************************
  /**
   * 开始摄像头设备测试
   * @param _dom         用于渲染摄像头画面的DOM节点，不传入表示只是打开摄像头，但是不渲染
   */
  public startCameraTest(_dom?: HTMLElement): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('打开摄像头测试失败，移动端不支持')));
  }

  /**
   * 停止摄像头设备测试
   */
  public stopCameraTest(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('关闭摄像头测试失败，移动端不支持')));
  }

  /**
   * 开始麦克风设备测试
   * @param _dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public startMicTest(_dom?: HTMLElement): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('打开麦克风测试失败，移动端不支持')));
  }

  /**
   * 停止麦克风设备测试
   */
  public stopMicTest(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('停止麦克风测试失败，移动端不支持')));
  }

  /**
   * 开启扬声器设备测试
   * @param _path        要播放的声音文件路径
   */
  public startSpeakerTest(_path: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('打开扬声器测试失败，移动端不支持')));
  }

  /**
   * 停止扬声器设备测试
   */
  public stopSpeakerTest(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('关闭扬声器失败，移动端不支持')));
  }
  public startCaptureStream(_elementId: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 停止自定义音视频采集
   */
  public stopCaptureStream(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }


  // *******************************************设备管理*******************************************
  /**
   * 获取摄像头设备列表
   */
  public getCameras(): Promise<TTrtcDeviceInfo[]> {
    return Promise.resolve([{
      type: TTrtcDeviceType.Camera,
      deviceId: 'front',
      deviceName: i18next.t('前置摄像头'),
    }, {
      type: TTrtcDeviceType.Camera,
      deviceId: 'back',
      deviceName: i18next.t('后置摄像头'),
    }]);
  }

  /**
   * 切换使用的摄像头设备
   * @param deviceId      要切换的设备ID
   */
  public switchCamera(deviceId: string): Promise<void> {
    this._info('switchCamera', `deviceId ${deviceId}, oldIsFrontCamera ${this.mIsFrontCamera}, _publishStatus.join ${this._publishStatus.join}`);
    this.mIsFrontCamera = deviceId === 'front';
    // 进房后再调用，否则安卓会crash
    if (this._publishStatus.join) {
      this.innerNativeCall('trtc', 'switchCamera', { frontCamera: this.mIsFrontCamera });
    } else {
      this.mNeedSwitchCameraAfterJoin = true;
    }
    TEvent.instance.notify(TTrtcEvent.Device_Changed, {
      deviceId,
      type: TTrtcDeviceType.Camera,
      state: TTrtcDeviceState.Update,
    });
    return Promise.resolve(undefined);
  }

  /**
   * 获取正在使用的摄像头设备ID
   */
  public getCameraDeviceId(): Promise<string> {
    return Promise.resolve(this.mIsFrontCamera ? TTrtcCameraID.CameraID_Front : TTrtcCameraID.CameraID_Back);
  }

  /**
   * 获取麦克风设备列表
   */
  public getMics(): Promise<TTrtcDeviceInfo[]> {
    return Promise.resolve([{
      type: TTrtcDeviceType.Mic,
      deviceId: 'default',
      deviceName: i18next.t('默认麦克风'),
    }]);
  }

  /**
   * 切换使用的麦克风设备
   * @param deviceId      要切换的设备ID
   */
  public switchMic(deviceId: string): Promise<void> {
    this._info('switchMic', `deviceId ${deviceId}`);
    return Promise.resolve();
  }

  /**
   * 获取正在使用的麦克风设备ID
   */
  public getMicDeviceId(): Promise<string> {
    return Promise.resolve('default');
  }

  /**
   * 设置正在使用的麦克风设备音量
   * @param volume        要设置的音量大小
   */
  public setMicVolume(volume: number): Promise<void> {
    this._info('setMicVolume', `volume ${volume}`);
    const vol = Math.floor(volume);
    if (!isNaN(vol)) {
      this._micVolume = vol;
      this.innerNativeCall('trtc', 'setAudioVolume', {
        captureVolume: this._micVolume,
      });
      this._updatePublishState(TMainState.Audio_Publish);
      return Promise.resolve();
    }
    return Promise.reject(new TCICError(-1, i18next.t('传入非法参数')));
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  public getMicVolume(): Promise<number> {
    return new Promise((resolve) => {
      this.innerNativeCall('trtc', 'getAudioVolume', {
        capture: true,
      }, (params) => {
        if (params && params.retval && typeof params.retval.captureVolume !== 'undefined') {
          resolve(parseInt(params.retval.captureVolume, 10));
        } else {
          resolve(this._micVolume);
        }
      });
    });
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  public getRealMicVolume(): Promise<number> {
    return this.getMicVolume();
  }

  /**
   * 获取扬声器设备列表
   */
  public getSpeakers(): Promise<TTrtcDeviceInfo[]> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('获取扬声器失败，移动端不支持')));
  }

  /**
   * 切换使用的扬声器设备
   * @param _deviceId      要切换的设备ID
   */
  public switchSpeaker(_deviceId: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('切换扬声器失败，移动端不支持')));
  }

  /**
   * 获取正在使用的扬声器设备ID
   */
  public getSpeakerDeviceId(): Promise<string> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('获取当前扬声器失败，移动端不支持')));
  }

  /**
   * 设置正在使用的扬声器设备音量
   * @param volume        要设置的音量大小
   */
  public setSpeakerVolume(volume: number): Promise<void> {
    this._info('setSpeakerVolume', `volume ${volume}`);
    this.innerNativeCall('trtc', 'setAudioVolume', {
      playoutVolume: Math.floor(volume),
    });
    return Promise.resolve(undefined);
  }

  public setNetworkQosParam(preference: TRTCVideoQosPreference): Promise<void> {
    this._info('setNetworkQosParam', `preference ${preference}`);
    this.innerNativeCall('trtc', 'setNetworkQosParam', {
      preference,
    });
    return Promise.resolve(undefined);
  }

  /**
   * 获取正在使用的扬声器设备音量
   */
  public getSpeakerVolume(): Promise<number> {
    return new Promise((resolve) => {
      this.innerNativeCall('trtc', 'getAudioVolume', {
        playout: true,
      }, (params) => {
        resolve(params.retval.playoutVolume || 0);
      });
    });
  }

  // *******************************************屏幕分享*******************************************

  /**
   * 检查当前平台是否支持屏幕分享
   */
  public isScreenShareSupported(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.innerNativeCall('trtc', 'isSupportScreenCapture', {}, (params) => {
        if (params && params.retval && params.retval.mode > 1) {
          resolve(true);
        } else {
          reject(new TCICError(-1, JSON.stringify(params), i18next.t('系统不支持')));
        }
      });
    });
  }

  /**
   * 检查屏幕分享权限
   */
  public hasScreenCapturePermission(): Promise<boolean> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 获取屏幕分享屏幕采集源列表
   */
  public getScreenCaptureSources(): Promise<TScreenCaptureSourceInfo[]> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 选择要进行屏幕分享的目标采集源
   * @param _source        要分享的采集源
   * @param _captureMouse        是否捕获鼠标
   * @param _highlightWindow        是否高亮选择区域
   */
  public selectScreenCaptureTarget(
    _source: TScreenCaptureSourceInfo,
    _captureMouse: boolean,
    _highlightWindow: boolean,
  ): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 进行屏幕分享的目标采集源
   * @return source        要分享的采集源
   */
  public getScreenCaptureTarget(): TScreenCaptureSourceInfo {
    return null;
  }

  /**
   * 将指定窗口加入屏幕分享的排除列表中
   */
  public addExcludedShareWindows(_sourceIds: string[]): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 将指定窗口加入屏幕分享的列表中
   */
  public addIncludedShareWindows(_sourceIds: string[]): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 开始屏幕分享
   * @param _dom           用于渲染分享画面的DOM节点，不需要渲染可以忽略
   */
  public startScreenShare(_dom?: HTMLElement): Promise<void> {
    if (TSession.instance.isAndroidNative()) {
      this._info('startScreenShare', 'isAndroidNative');
      // if (this.mOpenCamera) { // 开启屏幕分享前先关闭摄像头
      //   this.innerNativeCall('trtc', 'stopLocalPreview');
      //   this.innerRemoveNativeDom(this.mUserId, TTrtcViewType.ViewType_Camera);
      // }
      this.innerStartScreenShare();
    } else if (TSession.instance.isIOSNative()) {
      this._info('startScreenShare', 'isIOSNative, innerNativeCall trtc.launchScreenKit');
      this.innerNativeCall('trtc', 'launchScreenKit', {}, () => {
        this._info('startScreenShare', 'innerNativeCall trtc.launchScreenKit callback');
        // if (this.mOpenCamera) { // 开启屏幕分享前先关闭摄像头
        //   this.innerNativeCall('trtc', 'stopLocalPreview');
        // }
        this.innerStartScreenShare();
      });
    } else {
      this._info('startScreenShare', 'not support platform');
      return Promise.reject(new TCICError(-1, 'not support platform', i18next.t('未知系统')));
    }
    return this.newPromise(TTrtcAction.StartScreenShare, null, false, 0);   // 需要用户确认权限不能使用超时
  }

  /**
   * 暂停屏幕分享
   */
  public pauseScreenShare(): Promise<void> {
    this._info('pauseScreenShare', 'innerNativeCall trtc.pauseScreenShare');
    this.innerNativeCall('trtc', 'pauseScreenCapture', {}, (params) => {
      this._info('pauseScreenShare', `innerNativeCall trtc.pauseScreenCapture callback ${JSON.stringify(params)}`);
      if (params && params.errinfo) {
        if (params.errinfo.errcode !== 0) {     // 返回错误码不为0则为失败(成功需等onScreenCapturePaused回调)
          this.rejectPromise(
            TTrtcAction.PauseScreenShare,
            new TCICError(params.errinfo.errcode, params.errinfo.errmsg),
            null,
            true,
          );
        }
      } else {
        this._warn('pauseScreenShare', `parse callback fail: ${JSON.stringify(params)}`);
      }
    });
    return this.newPromise(TTrtcAction.PauseScreenShare, null, false);
  }

  /**
   * 恢复屏幕分享
   */
  public resumeScreenShare(): Promise<void> {
    this._info('resumeScreenShare', 'innerNativeCall trtc.resumeScreenCapture');
    this.innerNativeCall('trtc', 'resumeScreenCapture', {}, (params) => {
      this._info('resumeScreenShare', `innerNativeCall trtc.resumeScreenCapture callback ${JSON.stringify(params)}`);
      if (params && params.errinfo) {
        if (params.errinfo.errcode !== 0) {     // 返回错误码不为0则为失败(成功需等onScreenCaptureResumed回调)
          this.rejectPromise(
            TTrtcAction.ResumeScreenShare,
            new TCICError(params.errinfo.errcode, params.errinfo.errmsg),
          );
        }
      } else {
        this._warn('resumeScreenShare', `parse callback fail: ${JSON.stringify(params)}`);
      }
    });
    return this.newPromise(TTrtcAction.ResumeScreenShare, null, false);
  }

  /**
   * 停止屏幕分享
   */
  public stopScreenShare(): Promise<void> {
    return new Promise((resolve) => {
      this._info('stopScreenShare', 'innerNativeCall trtc.stopScreenCapture');
      this.innerNativeCall('trtc', 'stopScreenCapture', {}, (params) => {
        this._info('stopScreenShare', `innerNativeCall trtc.stopScreenCapture callback ${JSON.stringify(params)}`);
        // if (this.mOpenCamera) { // 恢复摄像头
        //   const streamId = this.innerGetStreamId(this.mUserId, TTrtcViewType.ViewType_Camera);
        //   const streamInfo: TTrtcRenderInfo = this.mRenderMap.get(streamId);
        //   if (TSession.instance.isAndroidNative()) {
        //     // this.innerCreateAndroidDom(this.mUserId, TTrtcViewType.ViewType_Camera, streamInfo.mDom).then(() => {
        //     //   this.innerStartLocalPreview();
        //     // });
        //   } else if (TSession.instance.isIOSNative()) {
        //     this.innerStartLocalPreview();
        //   }
        // }
        resolve();
      });
    });
  }
  /**
   *
   * 获取屏幕共享流
   */
  public getScreenShareStream() { }

  public setSubStreamEncoderParam(parmas: Partial<{
    videoResolution: TTrtcVideoResolution,
    resMode: 0 | 1,
    videoFps: number,
    videoBitrate: number,
    enableAdjustRes: boolean,
    screenCaptureMode: number,
  }>): Promise<void> {
    if (TSession.instance.isAndroidNative()) {
      return new Promise((resolve) => {
        this.innerNativeCall('trtc', 'setSubStreamEncoderParam', parmas, () => {
          this._info('setSubStreamEncoderParam', 'innerNativeCall trtc.setSubStreamEncoderParam callback');
          resolve();
        });
      });
    }
    this._warn('setSubStreamEncoderParam', 'ios call trtc.setSubStreamEncoderParam');
    return Promise.resolve();
  }
  /**
   * 控制是否分享系统声音
   * @param _enable        是否分享
   */
  public enableSystemAudioLoopback(_enable: boolean): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 播放背景音乐
   */
  public startMusic(_documentId: string, _url: string): Promise<boolean> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 停止背景音乐
   */
  public stopMusic(): void {
  }

  /**
   * 暂停背景音乐
   */
  public pauseMusic(): void {
  }

  /**
   * 恢复背景音乐
   */
  public resumeMusic(): void {
  }

  /**
   * 获取音乐时长，单位毫秒
   */
  public getMusicDuration(): number {
    return 0;
  }

  /**
   * 设置背景音乐进度
   */
  public seekMusic(_pts: number): void {
  }

  /**
   * 设置背景音乐的音量大小
   */
  public setMusicVolume(_volume: number): void {
  }

  /**
   * 加载视频
   */
  public loadVideo(_dom: HTMLElement, _url: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 开始播放视频
   */
  public playVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 暂停播放视频
   */
  public pauseVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 视频进度跳转
   */
  public seekVideo(_time: number): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 结束播放视频
   */
  public stopVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 设置音量大小
   */
  public setVideoVolume(_volume: number): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 打开辅助摄像头
   */
  public startSubCamera(_dom: HTMLElement, _deviceIndex: number, _resolution: TTrtcVideoResolution): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  /**
   * 关闭辅助摄像头
   */
  public stopSubCamera(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }


  // *******************************************美颜相关*******************************************
  /**
   * 设置虚拟背景
   */
  public setVirtualImg(_enable: boolean, _url: string, _sceneKey: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }
  /**
   * 设置美颜
   * @param beauty  美颜度( 0 - 10，推荐为 5 )
   * @param brightness 	明亮度( 0 - 10，推荐为 5 )
   * @param ruddy 红润度( 0 - 10，推荐为 5 )
   */
  public setBeautyParam(beauty: number, brightness: number, ruddy: number): Promise<void> {
    return new Promise((resolve) => {
      this.innerNativeCall('trtc', 'setBeauty', {
        beautyStyle: 1,
        beautyLevel: beauty > 9 ? 9 : beauty,
        whitenessLevel: brightness > 9 ? 9 : brightness,
        ruddyLevel: ruddy > 9 ? 9 : ruddy,
      }, () => {
        resolve();
      });
    });
  }
  public setAvatar(_effectId: string, _url: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('移动端不支持')));
  }

  // *******************************************降噪相关*******************************************
  /**
   * 检查当前平台是否支持AI降噪
   */
  public isAIDenoiseSupported(): boolean {
    return false;
  }

  /**
   * 开启AI降噪
   * @param _enable    是否开启
   */
  public enableAIDenoise(_enable: boolean): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('开启AI降噪失败，移动端不支持')));
  }

  /**
   * 获取类型
   * @protected
   */
  protected _getClassName() {
    return 'TTrtcMobile';
  }

  // *******************************************内部方法*******************************************
  // *******************************************TRTC事件*******************************************
  private onEnterRoom(_ret: any) {
    TTrtcMobile.mInstance._publishStatus.join = true;
    if (TTrtcMobile.mInstance.mNeedSwitchCameraAfterJoin) {
      TTrtcMobile.mInstance.mNeedSwitchCameraAfterJoin = false;
      const isFrontCamera = TTrtcMobile.mInstance.mIsFrontCamera;
      TTrtcMobile.mInstance._info('switchCamera', `do switch after join, isFrontCamera ${isFrontCamera}`);
      TTrtcMobile.mInstance.innerNativeCall('trtc', 'switchCamera', { frontCamera: isFrontCamera });
    }
    TTrtcMobile.mInstance._updatePublishState(TMainState.Video_Publish);
    TTrtcMobile.mInstance._updatePublishState(TMainState.Audio_Publish);
    TTrtcMobile.mInstance.innerStartTrtcTimer();
    TTrtcMobile.mInstance.resolvePromise(TTrtcAction.Join);
  }
  private onExitRoom(ret: any) {
    TTrtcMobile.mInstance._publishStatus.join = false;
    TTrtcMobile.mInstance._updatePublishState(TMainState.Video_Publish);
    TTrtcMobile.mInstance._updatePublishState(TMainState.Audio_Publish);
    TTrtcMobile.mInstance.rejectPromise(TTrtcAction.Join, new TCICError(ret.retval.reason, i18next.t('加入音视频房间失败')));
  }
  private onUserAudioAvailable(ret: any) {
    if (ret && ret.retval && ret.retval.availableUserId) {
      TEvent.instance.notify(TTrtcEvent.Audio_Changed, {
        userId: ret.retval.availableUserId,
        available: ret.retval.available,
      });
    }
  }
  private onUserVideoAvailable(ret: any) {
    if (ret && ret.retval && ret.retval.availableUserId) {
      TEvent.instance.notify(TTrtcEvent.Video_Changed, {
        userId: ret.retval.availableUserId,
        available: ret.retval.available,
      });
    }
  }
  private onUserSubStreamAvailable(ret: any) {
    if (ret && ret.retval && ret.retval.availableUserId) {
      TEvent.instance.notify(TTrtcEvent.SubStream_Changed, {
        userId: ret.retval.availableUserId,
        available: ret.retval.available,
      });
    }
  }
  private onScreenCaptureStarted(_ret: any) {
    // 通过 TWebView 的 callback 调用，没有 this
    TTrtcMobile.mInstance._info('onScreenCaptureStarted');
    TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 0);
  }
  private onScreenCaptureStopped(_ret: any) {
    // 通过 TWebView 的 callback 调用，没有 this
    TTrtcMobile.mInstance._info('onScreenCaptureStopped');
    TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 2);
    TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
  }
  private onScreenCapturePaused(_ret: any) {
    // 通过 TWebView 的 callback 调用，没有 this
    TTrtcMobile.mInstance._info('onScreenCapturePaused');
    TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 1);
    TTrtcMobile.mInstance.resolvePromise(TTrtcAction.PauseScreenShare, {}, null, true);
  }
  private onScreenCaptureResumed(_ret: any) {
    // 通过 TWebView 的 callback 调用，没有 this
    TTrtcMobile.mInstance._info('onScreenCaptureResumed');
    TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 0);
    TTrtcMobile.mInstance.resolvePromise(TTrtcAction.ResumeScreenShare, {}, null, true);
  }
  private onScreenKitEvent(ret: any) {
    if (ret && ret.retval && ret.retval.event) {
      const event = ret.retval.event;
      TTrtcMobile.mInstance._info('onScreenKitEvent', `event ${event}`);
      if (event === 'started') {
        TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 0);
      } else if (event === 'paused') {
        TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 1);
        TTrtcMobile.mInstance.resolvePromise(TTrtcAction.PauseScreenShare, {}, null, true);
      } else if (event === 'resumed') {
        TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 0);
        TTrtcMobile.mInstance.resolvePromise(TTrtcAction.ResumeScreenShare, {}, null, true);
      } else if (event === 'finished') {
        TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 2);
        TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
      } else {
        TTrtcMobile.mInstance._warn('onScreenKitEvent', `unknown event: ${event}`);
      }
    } else {
      TTrtcMobile.mInstance._warn('onScreenKitEvent', `unknown ret ${JSON.stringify(ret)}`);
    }
  }
  /**
   *
   * @param ret {
      "head": {
          "action": "onUserVoiceVolume",
          "module": "trtccb",
          "seqid": 55885307087936
      },
      "retval": {
          "totalVolume": 6,
          "userVolumes": [
              {
                  "userId": "2ZhL49DDsM1DuZxNNaBQ3GPTyGt",
                  "volume": 1
              },
              {
                  "userId": "2XZAx7Rsencu9G7ggV8RVgkr5my",
                  "volume": 5
              },
              {
                  "userId": "tic_push_user_323777580_163004",
                  "volume": 0
              },
              {
                  "userId": "2a4Ym5YHbBaqbDLQJSVa0l4ZRZD",
                  "volume": 0
              }
          ]
      }
  }
   */
  private onUserVoiceVolume(ret: any) {
    /**
     * 移动端里找不到this
     */
    const volumeInfo = TTrtcMobile.mInstance._volumeInfo || new Map();
    if (ret && ret.retval && ret.retval.userVolumes) {
      for (const data of ret.retval.userVolumes) {
        paddingVolumeInfo(volumeInfo, {
          uid: data.userId,
          val: data.volume,
          spectrumData: data.spectrumData,
        });
        TEvent.instance.notify(TTrtcEvent.Volume_Update, {
          userId: data.userId,
          volume: data.volume,
          spectrumData: data.spectrumData,
        }, false);
      }
    }
  }
  private onMicDidReady(_ret: any) {
    if (TTrtcMobile.mInstance.mHasEnableMic) {
      TTrtcMobile.mInstance._info('onMicDidReady', 'enter');
      TTrtcMobile.mInstance.innerUpdateMicStatus(true, TDeviceStatus.Open);
      TTrtcMobile.mInstance.resolvePromise(TTrtcAction.StartLocalAudio, {}, null, true);
    } else {
      TTrtcMobile.mInstance._info('onMicDidReady', 'ignore without enable mic');
    }
  }
  private onCameraDidReady(_ret: any) {
    TTrtcMobile.mInstance._info('onCameraDidReady', 'enter');
    TTrtcMobile.mInstance.innerUpdateCameraStatus(true, TDeviceStatus.Open);
    TTrtcMobile.mInstance.resolvePromise(TTrtcAction.StartLocalVideo, {}, null, true);
  }
  private onNetworkQuality(ret: any) {
    if (ret && ret.retval && ret.retval.localQuality) {
      const uplinkNetworkQuality = ret.retval.localQuality.quality;
      let downTotal = 0;
      ret.retval.remoteQuality.forEach((info: any) => {
        downTotal += info.quality;
      });
      const downlinkNetworkQuality = Math.floor(downTotal / ret.retval.remoteQuality.length);

      if ([4, 5].includes(uplinkNetworkQuality) || [4, 5].includes(downlinkNetworkQuality)) {
        TEvent.instance.notify(TMainEvent.Weak_Network, {}, true);
      }
      if (uplinkNetworkQuality == 6 || downlinkNetworkQuality == 6) {
        TEvent.instance.notify(TMainEvent.Broken_Network, {}, true);
      }
      TEvent.instance.notify(TTrtcEvent.Network_Quality, {
        uplinkNetworkQuality,
        downlinkNetworkQuality,
      }, false);
    }
  }

  private onStatistics(ret: any) {
    if (ret && ret.retval && ret.retval.statistics) {
      const param = ret.retval.statistics;
      const trtcStatistics = new TTrtcStatistics();
      trtcStatistics.upLoss = param.upLoss;
      trtcStatistics.downLoss = param.downLoss;
      trtcStatistics.upVideoPixels = 0;
      trtcStatistics.downVideoPixels = 0;
      trtcStatistics.appCpu = param.appCpu;
      trtcStatistics.systemCpu = param.systemCpu;
      trtcStatistics.rtt = param.rtt;
      trtcStatistics.remoteStatisticsArray = [];
      param.localStatistics.forEach((item: any) => {
        trtcStatistics.upVideoPixels += item.width * item.height;
      });
      param.remoteStatistics.forEach((item: any) => {
        if (item.width && item.height && item.frameRate && !item.userId.startsWith('tic_push_user')) {
          trtcStatistics.downVideoPixels += item.width * item.height;
        }
        const remoteStatistics = new TTrtcRemoteStatistics();
        remoteStatistics.userId = item.userId;
        remoteStatistics.upLoss = Math.max(item.finalLoss - param.downLoss, 0);
        trtcStatistics.remoteStatisticsArray.push(remoteStatistics);
      });
      TEvent.instance.notify(TTrtcEvent.Network_Statistis, trtcStatistics);
    }
  }

  private onError(ret: any) {
    if (ret && ret.retval && ret.retval.errCode) {
      TTrtcMobile.mInstance._error('onError', `code: ${ret.retval.errCode}, msg: ${ret.retval.errMsg}`);
      const streamId = TTrtcMobile.mInstance.innerGetStreamId(
        TTrtcMobile.mInstance.mUserId,
        TTrtcViewType.ViewType_Camera,
      );
      const { errCode, errMsg } = ret.retval;
      if (errCode === -1301 || errCode === -1314 || errCode === -1316) {
        const { deviceStatus, errMsg: showMsg } = getNativeErrorInfo(errCode);
        TTrtcMobile.mInstance.mRenderMap.delete(streamId);
        TTrtcMobile.mInstance.innerUpdateCameraStatus(false, deviceStatus);
        TTrtcMobile.mInstance.rejectPromise(
          TTrtcAction.StartLocalVideo,
          new TCICError(errCode, showMsg || i18next.t('打开摄像头失败'), errMsg),
          null,
          true,
        );
        // andriod下额外弹窗处理
        // if (TSession.instance.isAndroidNative() && (errCode === -1314 || errCode === -1316)) {
        //   TMain.instance.showErrorMsgBox({
        //     title: i18next.t('打开摄像头失败'),
        //     message: errMsg,
        //     buttons: [i18next.t('确定')],
        //   });
        // }
      } else if (errCode === -1302 || errCode === -1317 || errCode === -1319) {
        const { deviceStatus, errMsg: showMsg } = getNativeErrorInfo(errCode);
        TTrtcMobile.mInstance.innerUpdateMicStatus(false, deviceStatus);
        TTrtcMobile.mInstance.rejectPromise(
          TTrtcAction.StartLocalAudio,
          new TCICError(errCode, showMsg || i18next.t('打开麦克风失败'), errMsg),
          null,
          true,
        );
        // andriod下额外弹窗处理
        // if (TSession.instance.isAndroidNative() && (errCode === -1317 || errCode === -1319)) {
        //   TMain.instance.showErrorMsgBox({
        //     title: i18next.t('打开麦克风失败'),
        //     message: errMsg,
        //     buttons: [i18next.t('确定')],
        //   });
        // }
      } else if (errCode === -1308 || errCode === -1309 || errCode === -7001) {
        const showMsg = getNativeErrorInfo(errCode)?.errMsg || i18next.t('屏幕共享失败');
        TTrtcMobile.mInstance._setState(TMainState.Screen_Share, 2);    // 更新屏幕共享状态为关闭
        TTrtcMobile.mInstance.rejectPromise(
          TTrtcAction.StartScreenShare,
          new TCICError(errCode, showMsg, errMsg),
          null,
          true,
        );
        TEvent.instance.notify(
          TMainEvent.Warn,
          new TCICError(errCode, showMsg, `${JSON.stringify(ret.retval)}`),
        );
      }
    }
  }

  private onWarning(ret: any) {
    if (ret && ret.retval && ret.retval.warningCode) {
      const curTime = new Date().getTime();
      const { warningCode, warningMsg } = ret.retval;
      if (warningCode === TTrtcMobile.mInstance.mLastWarnCode
        && curTime - TTrtcMobile.mInstance.mLastWarnTimeStamp < 2000) {
        // 警告码相同，且间隔小于2秒则不上报
      } else {
        TTrtcMobile.mInstance._warn('onWarning', `code: ${warningCode}, msg: ${warningMsg}`);
      }
      TTrtcMobile.mInstance.mLastWarnCode = warningCode;
      TTrtcMobile.mInstance.mLastWarnTimeStamp = curTime;

      // 麦克风警告
      if (warningCode === 1201 || warningCode === 1203 || warningCode === 1204) { // 麦克风未授权仅有warn事件，没有error事件
        const { deviceStatus, errMsg: showMsg } = getNativeErrorInfo(warningCode);
        // TODO 下面注释没写清楚为什么处理状态就会导致无法开麦，待确认原因，先不改采集和推流的状态，只改设备状态
        // 注意：不能改变麦克风的状态，会导致无法开麦；底层sdk会自动恢复，无需处理这里的状态
        // TTrtcMobile.mInstance.innerUpdateMicStatus(false, deviceStatus);
        TTrtcMobile.mInstance._setState(TMainState.Audio_Device_Status, deviceStatus);
        TTrtcMobile.mInstance.rejectPromise(
          TTrtcAction.StartLocalAudio,
          new TCICError(warningCode, showMsg || i18next.t('打开麦克风失败'), warningMsg),
          null,
          true,
        );
      }

      // 扬声器警告
      if (warningCode === 1202 || warningCode === 1205) {
        const { errMsg: showMsg } = getNativeErrorInfo(warningCode);
        TTrtcMobile.mInstance.rejectPromise(
          TTrtcAction.StartSpeakerTest,
          new TCICError(warningCode, showMsg || i18next.t('打开扬声器失败'), warningMsg),
          null,
          true,
        );
      }
    }
  }

  private onBackPressed(_ret: any) {
    TEvent.instance.notify(TMainEvent.Back_Pressed, {});
  }

  private onConnectionLost(_ret: any) {
    TTrtcMobile.mInstance._setState(TMainState.Network_Broken, true);
  }

  private onConnectionRecovery(_ret: any) {
    TTrtcMobile.mInstance._setState(TMainState.Network_Broken, false);
  }
  // *******************************************渲染事件*******************************************
  private onAddRender(params: any) {
    TTrtcMobile.mInstance._info('onAddRender', JSON.stringify(params));
    const streamId = TTrtcMobile.mInstance.innerGetStreamId(params.userId, params.viewType);
    if (TSession.instance.isAndroidNative()) {
      if (!TTrtcMobile.mInstance.resolvePromise(TTrtcAction.CreateNativeDom, {}, streamId)) {
        TTrtcMobile.mInstance._warn('onAddRender', `create native dom task not found: ${params.userId}, type: ${params.viewType}`);
        // 重试无法避免画面无法显示的问题，先放弃
        // TTrtcMobile.mInstance.resetVideoRender(params.userId, params.viewType);
      }
    } else if (TSession.instance.isIOSNative()) {
      const renderInfo: TTrtcRenderInfo = TTrtcMobile.mInstance.mRenderMap.get(streamId);
      if (renderInfo) {
        TTrtcMobile.mInstance.innerCreateIOSDom(renderInfo.mUserId, renderInfo.mViewType, renderInfo.mDom);
      }
    }
  }
  private onRemoveRender(params: any) {
    TTrtcMobile.mInstance._info('onRemoveRender', JSON.stringify(params));
  }
  // *******************************************内部方法*******************************************
  // 开启音视频状态监控
  private innerStartTrtcTimer() {
    if (this.mTrtcTimer) {
      clearInterval(this.mTrtcTimer);
    }
    this.mTrtcTimer = setInterval(async () => {
      this.innerNativeCall('trtc', 'getTRTCStats', {}, (infos) => {
        console.log('mTrtcTimer', infos);
        if (infos && infos.retval && infos.retval.RtcInfos) {
          console.log(`getTRTCStats->info: ${JSON.stringify(infos.retval.RtcInfos)}`);
          if (infos.retval.RtcInfos instanceof Array) {   // 添加保护避免数据格式问题
            this.mRtcInfo = infos.retval.RtcInfos;
          }
        }
      });
    }, 15000);
  }
  // 通过协议调用Native接口
  private innerNativeCall(module: string, name: string, params = {}, callback = (_ret: any) => { }) {
    TWebView.instance.call(module, name, params, callback);
  }

  // 创建Android同层渲染组件
  private innerCreateAndroidDom(userId: string, viewType: TTrtcViewType, dom: any, enableTimeOut = true) {
    this._info('innerCreateAndroidDom', `enter=>userId: ${userId}, viewType: ${viewType}`);
    dom.innerHTML = '';     // 避免存在多个渲染组件
    const createDom = () => {
      try {
        const viewDom = document.createElement('tcic-video');
        viewDom.id = `content_${userId}_${viewType}`;
        viewDom.setAttribute('userId', userId);
        viewDom.setAttribute('streamType', `${viewType}`);
        viewDom.className = 'native_render_view';
        dom.appendChild(viewDom);
      } catch (error) {
        // 同层渲染组件创建失败时上报.
        console.error(' [ error ]-1508', error);
        this._error('innerCreateAndroidDom', JSON.stringify(error));
      }
    };
    if (viewType === TTrtcViewType.ViewType_Screen) {   // 辅路延时，避免native收不到dom创建事件
      window.setTimeout(createDom, 100);
    } else {
      createDom();
    }
    return this.newPromise(TTrtcAction.CreateNativeDom, `${userId}_${viewType}`, enableTimeOut, enableTimeOut ? 30000 : 0);
  }
  // 创建iOS同层渲染组件
  private innerCreateIOSDom(userId: string, viewType: TTrtcViewType, dom: any) {
    try {
      this._info('innerCreateIOSDom', `enter=>userId: ${userId}, viewType: ${viewType}`);
      dom.innerHTML = '';     // 避免存在多个渲染组件
      const viewDom = document.createElement('div');
      viewDom.id = `${userId}_${viewType}`;
      viewDom.className = 'native_render_view';
      const contentDom = document.createElement('div');
      contentDom.id = `content_${userId}_${viewType}`;
      contentDom.className = 'native_render_content';
      viewDom.appendChild(contentDom);
      // 添加dom节点
      dom.appendChild(viewDom);
    } catch (error) {
      console.error(' [ error ]-1535', error);
    }
  }

  // 移除同层渲染组件
  private innerRemoveNativeDom(userId: string, viewType: TTrtcViewType) {
    const streamId = this.innerGetStreamId(userId, viewType);
    if (this.mRenderMap.has(streamId)) {
      this._info('innerRemoveNativeDom', `${userId}, ${viewType}`);
      const streamInfo: TTrtcRenderInfo = this.mRenderMap.get(streamId);
      streamInfo.mDom.innerHTML = '';
    } else {
      this._info('innerRemoveNativeDom', `${userId}, ${viewType} not found`);
    }
  }

  // 获取视频流标识
  private innerGetStreamId(userId: string, viewType: TTrtcViewType) {
    return `${userId}_${viewType}`;
  }

  // 打开摄像头
  private innerStartLocalPreview() {
    this._info('innerStartLocalPreview', 'enter');
    this.innerNativeCall('trtc', 'startLocalPreview', { frontCamera: this.mIsFrontCamera ? 1 : 0 }, (params: any) => {
      this._info('startLocalPreview', `rsp: ${JSON.stringify(params)}`);
      // 成功在 onCameraDidReady，为什么不写在一起？
      if (params && params.errinfo && params.errinfo.errcode !== 0) {   // 不为0则失败
        this.mRenderMap.delete(this.innerGetStreamId(this.mUserId, TTrtcViewType.ViewType_Camera));
        const { deviceStatus } = getNativeErrorInfo(params.errinfo.errcode);
        this.innerUpdateCameraStatus(false, deviceStatus);
        this.rejectPromise(TTrtcAction.StartLocalVideo, new TCICError(params.errinfo.errcode, params.errinfo.errmsg, i18next.t('没有权限打开摄像头')), null, true);
      }
    });
  }

  // 渲染远端视频流
  private innerRenderUserView(userId: string, viewType: TTrtcViewType, fitMode: boolean) {
    const mode = fitMode ? 1 : 0;
    this._info('innerRenderUserView', `userId: ${userId}, viewType: ${viewType}, fitMode: ${fitMode}, mode: ${mode}`);
    const streamId = this.innerGetStreamId(userId, viewType);
    const renderInfo = this.mRenderMap.get(streamId);
    if (renderInfo) {
      renderInfo.mIsRendering = true;
    }
    if (viewType === TTrtcViewType.ViewType_Screen) {
      this.innerNativeCall('trtc', 'setRemoteSubStreamPreviewParams', {
        userId,
        mode,
        rotation: 0,
      });
      this.innerNativeCall('trtc', 'startRemoteSubStreamView', { userId });
    } else {
      this.innerNativeCall('trtc', 'setRemotePreviewParams', {
        userId,
        mode,
      });
      this.innerNativeCall('trtc', 'startRemoteView', { userId });
    }
    this.resolvePromise(TTrtcAction.StartRemoteVideo, {}, streamId, true);
  }

  // 类型转换
  private innerGetViewType(type: TTrtcVideoStreamType) {
    let viewType = TTrtcViewType.ViewType_Camera;
    if (type === TTrtcVideoStreamType.Sub) {
      viewType = TTrtcViewType.ViewType_Screen;
    }
    return viewType;
  }

  // 开始屏幕分享
  private innerStartScreenShare() {
    // this.innerNativeCall('trtc', 'stopScreenCapture', {});  // 先关闭屏幕分享作保护
    // 线上问题反馈：因之前resMode一直为0，导致iOS设备在某种情况下，会只出现竖屏帧，导致不会更新角度，所有以出现录制时画面旋转90度；
    // 改成 iOS : 1 (iOS在课中都是横屏) , android继续保持0, 改1有其他问题；
    const isIOS = TSession.instance.isIOS();
    this._info('innerStartScreenShare', 'innerNativeCall trtc.startScreenCapture');
    this.innerNativeCall('trtc', 'startScreenCapture', {
      videoResolution: TTrtcVideoResolution.Resolution_1920_1080,
      resMode: 0,
      videoFps: 15,
      videoBitrate: 4000,
      enableAdjustRes: false,
      screenCaptureMode: 2,
    }, (params) => {
      this._info('innerStartScreenShare', `innerNativeCall trtc.startScreenCapture callback ${JSON.stringify(params)}`);
      if (params && params.retval && params.retval.reason === 0) {
        this.resolvePromise(TTrtcAction.StartScreenShare, {}, null, true);
      } else {
        this.stopScreenShare();   // 重置TRTC状态
        this.rejectPromise(TTrtcAction.StartScreenShare, new TCICError(-1, JSON.stringify(params), i18next.t('屏幕共享失败')), null, true);
      }
    });
  }

  // 更新摄像头状态
  private innerUpdateCameraStatus(enable: boolean, deviceStatus: TDeviceStatus) {
    TTrtcMobile.mInstance._setState(TMainState.Video_Device_Status, deviceStatus);
    TTrtcMobile.mInstance._setState(TMainState.Video_Capture, enable);
    TTrtcMobile.mInstance._updatePublishState(TMainState.Video_Publish);
  }
  // 更新麦克风状态
  private innerUpdateMicStatus(enable: boolean, deviceStatus: TDeviceStatus) {
    TTrtcMobile.mInstance._setState(TMainState.Audio_Device_Status, deviceStatus);
    TTrtcMobile.mInstance._setState(TMainState.Audio_Capture, enable);
    TTrtcMobile.mInstance._updatePublishState(TMainState.Audio_Publish);
  }

  // 停止本地视频采集及渲染
  private innerStopLocalRender() {
    this.innerNativeCall('trtc', 'stopLocalPreview');
    this.innerRemoveNativeDom(this.mUserId, TTrtcViewType.ViewType_Camera);
    if (TSession.instance.isIOSNative()) {
      if (TSession.instance.isIOS8910()) {
        const streamId = this.innerGetStreamId(this.mUserId, TTrtcViewType.ViewType_Camera);
        this.innerRemoveRenderTask(streamId);
      } else {
        this.innerNativeCall('tcicui', 'removeUserRenderItem', {
          userId: this.mUserId,
          viewType: TTrtcViewType.ViewType_Camera,
        });
      }
    }
  }
  // 开启本地视频采集及渲染
  private innerStartLocalRender(streamId: string) {
    const renderInfo = this.mRenderMap.get(streamId);
    if (renderInfo) {
      const dom = renderInfo.mDom;
      if (TSession.instance.isAndroidNative()) {
        this._info('innerStartLocalRender', `isAndroidNative, innerCreateAndroidDom, streamId ${streamId}`);
        this.innerCreateAndroidDom(this.mUserId, TTrtcViewType.ViewType_Camera, dom)
          .then(() => {
            const hasStream = this.mRenderMap.has(streamId);
            this._info('innerStartLocalPreview', `innerCreateAndroidDom success, hasStream ${hasStream}`);
            if (hasStream) {
              this.innerStartLocalPreview();
            }
          })
          .catch((err: any) => {
            this._warn('innerStartLocalRender', `innerCreateAndroidDom fail: ${JSON.stringify(err)}`);
            this.mRenderMap.delete(streamId);
          });
      } else if (TSession.instance.isIOSNative()) {
        this._info('innerStartLocalRender', `isIOSNative, addUserRenderItem, streamId ${streamId}`);
        if (TSession.instance.isIOS8910()) {
          this._info('innerStartLocalRender', `isIOSNative and isIOS8910, innerAddRenderTask, streamId ${streamId}`);
          this.innerAddRenderTask(streamId);
        } else {
          this._info('innerStartLocalRender', `isIOSNative not isIOS8910, addUserRenderItem, streamId ${streamId}`);
          this.innerNativeCall('tcicui', 'addUserRenderItem', {
            userId: this.mUserId,
            viewType: TTrtcViewType.ViewType_Camera,
          }, () => {
            const hasStream = this.mRenderMap.has(streamId);
            this._info('innerStartRemoteRender', `addUserRenderItem success, hasStream ${hasStream}`);
            if (hasStream) {
              this.innerStartLocalPreview();
            }
          });
        }
      } else {
        this._warn('innerStartLocalRender', 'unknown platform!');
      }
    }
  }

  // 停止视频渲染
  private innerStopRemoteRender(userId: string, viewType: TTrtcViewType) {
    if (viewType === TTrtcViewType.ViewType_Screen) {
      this.innerNativeCall('trtc', 'stopRemoteSubStreamView', { userId });
    } else {
      this.innerNativeCall('trtc', 'stopRemoteView', { userId });
    }
    if (TSession.instance.isIOSNative()) {
      if (TSession.instance.isIOS8910()) {
        const streamId = this.innerGetStreamId(userId, viewType);
        this.innerRemoveRenderTask(streamId);
      } else {
        this.innerNativeCall('tcicui', 'removeUserRenderItem', { userId, viewType });
      }
    }
    const streamId = this.innerGetStreamId(userId, viewType);
    const renderInfo = this.mRenderMap.get(streamId);
    if (renderInfo) {
      renderInfo.mIsRendering = false;
    }
    this.innerRemoveNativeDom(userId, viewType);
  }
  // 开始视频渲染
  private innerStartRemoteRender(streamId: string) {
    const renderInfo = this.mRenderMap.get(streamId);
    if (renderInfo) {
      const userId = renderInfo.mUserId;
      const viewType = renderInfo.mViewType;
      const dom = renderInfo.mDom;
      const fitMode = renderInfo.mFitMode;
      if (TSession.instance.isAndroidNative()) {
        this.innerCreateAndroidDom(userId, viewType, dom, false)
          .then(() => {
            if (this.mRenderMap.has(streamId)) {
              this.innerRenderUserView(userId, viewType, fitMode);
            }
          })
          .catch((err: any) => {
            this._warn('innerStartRemoteRender', `fail: ${JSON.stringify(err)}`);
            this.mRenderMap.delete(streamId);
          });
      } else if (TSession.instance.isIOSNative()) {
        if (TSession.instance.isIOS8910()) {
          this.innerAddRenderTask(streamId);
        } else {
          this.innerNativeCall('tcicui', 'addUserRenderItem', { userId, viewType }, () => {
            if (this.mRenderMap.has(streamId)) {
              this.innerRenderUserView(userId, viewType, fitMode);
            }
          });
        }
      }
    }
  }

  // 添加到视频渲染队列(ios910)
  private innerAddRenderTask(streamId: string) {
    if (!this.mRenderTask.includes(streamId)) {
      this._info('innerAddRenderTask', `add render task ${streamId}`);
      this.mRenderTask.push(streamId);
      this.innerProcessRenderTask();
    } else {
      this._warn('innerAddRenderTask', `add render task ${streamId} existed!`);
    }
  }

  // 从视频渲染队列中移除任务(ios910)
  private innerRemoveRenderTask(streamId: string) {
    const idx = this.mRenderTask.indexOf(streamId);
    if (idx > -1) {
      this.mRenderTask.splice(idx, 1);
      this._info('innerRemoveRenderTask', `remove render task ${streamId}`);
    } else {    // 不在任务列表中直接移除
      const renderInfo = this.mRenderMap.get(streamId);
      if (renderInfo) {
        this.innerNativeCall('tcicui', 'removeUserRenderItem', {
          userId: renderInfo.mUserId,
          viewType: renderInfo.mViewType,
        });
      } else {
        this._warn('innerRemoveRenderTask', `remove render task ${streamId} not found`);
      }
      if (this.mCurRenderTask === streamId) { // 是当前正在进行的任务
        this.innerMoveOnTask();
      }
    }
  }

  // 执行渲染队列任务(ios910)
  private innerProcessRenderTask() {
    if (null === this.mCurRenderTask) {   // 当前没有任务
      if (this.mRenderTask.length > 0) {    // 有任务
        const streamId = this.mRenderTask.shift();
        this.mCurRenderTask = streamId;

        this._info('innerProcessRenderTask', `process task: ${streamId}`);
        const renderInfo = this.mRenderMap.get(streamId);
        if (renderInfo) {
          const userId = renderInfo.mUserId;
          const viewType = renderInfo.mViewType;

          if (userId === this.mUserId) {    // 本地
            this.innerNativeCall('tcicui', 'addUserRenderItem', {
              userId: this.mUserId,
              viewType: TTrtcViewType.ViewType_Camera,
            }, () => {
              if (this.mRenderMap.has(streamId)) {
                this.innerStartLocalPreview();
              }
              this.innerMoveOnTask();
            });
          } else {  // 远端
            this.innerNativeCall('tcicui', 'addUserRenderItem', { userId, viewType }, () => {
              if (this.mRenderMap.has(streamId)) {
                this.innerRenderUserView(userId, viewType, renderInfo.mFitMode);
              }
              this.innerMoveOnTask();
            });
          }
        } else {
          this._warn('innerProcessRenderTask', `task not found: ${streamId}`);
          this.innerMoveOnTask();
        }
      }
    }
  }

  // 结束当前任务并尝试执行下一任务
  private innerMoveOnTask() {
    this._info('innerMoveOnTask', `task completed: ${this.mCurRenderTask}`);
    this.mCurRenderTask = null;
    this.innerProcessRenderTask();
  }
}
