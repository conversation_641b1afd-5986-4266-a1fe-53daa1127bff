import { TRTCWebError as T<PERSON><PERSON><PERSON><PERSON>, TLoggerModule, TPromiseExecutor } from '../../base/tmodule';
import { TDeviceStatus, TMainEvent, TMainState } from '../../constants';
import {
  TRTCVideoQosPreference,
  TScreenCaptureSourceInfo,
  TStreamStatus,
  TTrtcAction,
  TTrtcBase,
  TTrtcDeviceInfo,
  TTrtcDeviceState,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcLocalVideoParams,
  TTrtcMode,
  TTrtcRtcInfo,
  TTrtcSnapshot,
  TTrtcStatistics,
  TTrtcVideoResolution,
  TTrtcVideoStreamType,
  convertMediaDeviceInfo,
  getVideoSizeFromResolution,
  isDeviceAbnormal,
  paddingVolumeInfo,
} from './ttrtc_base';

import { TAV } from '../tav';
import { TBusinessMember } from '../business/tbusiness_member';
import { TEvent } from '../../base/tevent';
import { TMain } from '../tmain';
import { TPermissionInfo } from '../business/tbusiness_user';
import TRTC from 'trtc-sdk-v5';
import { TSession } from '../tsession';
import { TState } from '../tstate';
import { TUtil } from '../tutil_inner';
import { TWebAR } from '../webar';
import { WebarUtil } from '../webar-util';
/* eslint-disable no-plusplus */
import i18next from 'i18next';

// TRTC库路径
const TRTCLibPath = './static/libs/trtc';

// AI降噪插件用相对路径，避免因跨域问题导致加载不到wasm
const TRTCAIDenoiserPath = './static/libs/trtc/rtc-ai-denoiser-1.1.3';

interface TTrtcDenoiserProcessor {
  enabled: boolean;
  process: (stream: any) => Promise<any>;
  enable: () => Promise<void>;
  disable: () => Promise<void>;
  startDump: () => void;
  stopDump: () => void;
  on: (evtName: string, evtHandler: (params: any) => any) => void;
  off: (evtName: string, evtHandler: (params: any) => any) => void;
  destroy: () => void;
}
interface DeviceChangedInfo {
  device: TTrtcDeviceInfo,
  changed: boolean
}
interface TTrtcDenoiserProcessorEx extends TTrtcDenoiserProcessor {
  __streamTag?: string;
  __dumpTimer?: any;
  __onDumpEnd?: () => any;
}

class TRemoteStreamHolder extends TLoggerModule {
  private static _permissionUserIds = new Set();

  public static permissionAV(userID: string, permissonSucCallback: Function) {
    return TRemoteStreamHolder._permissionAV(userID, permissonSucCallback);
  }

  // 判断视频流类型
  public static isScreenStream(type: string) {
    return type === 'sub'; // 主路：main 辅路：sub
  }
  // 授权音视频
  private static _permissionAV(userID: string, permissonSucCallback: Function) {
    const isEmpty = TRemoteStreamHolder._permissionUserIds.size === 0;
    TRemoteStreamHolder._permissionUserIds.add(userID);
    if (isEmpty) {
      // 只有第一条流触发弹窗提示，其它流等待第一条流的弹窗授权即可
      TMain.instance.reportLog('trtcAuthorization', '[TTrtcWeb] showMessageBox');
      TMain.instance.showMessageBox(
        '',
        i18next.t('需要您授权以播放音视频'),
        [i18next.t('授权')],
        (index) => {
          const isConfirm = index === 0;
          TMain.instance.reportLog('trtcAuthorization', `[TTrtcWeb] isConfirm ${isConfirm}`);
          TRemoteStreamHolder._permissionUserIds.forEach((userID: string) => {
            permissonSucCallback();
          });
          TRemoteStreamHolder._permissionUserIds.clear();
        },
      );
    }
  }
  protected _getClassName() {
    return 'TTrtcWeb';
  }
}

function logMethod(target: any, key: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;

  descriptor.value = function (...args: any[]) {
    // console.log(`【Calling ${key} with arguments: ${args.map(i => JSON.stringify(i)).join(' ,')}`);
    const result = originalMethod.apply(this, args);
    // console.log(`Finished calling ${key}`);
    return result;
  };

  return descriptor;
}

export class TTrtcWeb extends TTrtcBase {
  private static readonly PublishState = 'trtc-web-publish';
  private static readonly ScreenPublishState = 'trtc-screen-publish';

  /**
   * 获取 SDK JS
   */
  public static getSDKJS(): string {
    return '';
  }

  /**
   * 获取插件列表
   */
  public static getPluginList(): { name: string, url: string }[] {
    return [];
  }

  private _trtc: any = null;
  private _client: TRTC = TRTC.create();
  private _shareStream: any = null;
  private _publishStream: any = null;
  private _localVideoStream: any = null;
  private _localAudioStream: any = null;
  private _testCameraStream: any = null;
  private _testMicStream: any = null;
  private _oldAudioTrack: any = null;
  private _micAudioStream: any = null;
  private _micAudioGain: GainNode = null;
  private _sysAudioGain: GainNode = null;
  private _testVideoDom: HTMLElement = null;
  private _localVideoDom: HTMLElement = null;
  private _localScreenDom: HTMLElement = null;
  private _testSpeakerDom: HTMLAudioElement = null;
  private _audioContext: AudioContext = null;
  // 帐号与用户
  private _sdkAppId: number;  // 当前应用id
  private _userId: string; // 用户帐号
  private _userSig: string; // 用户签名
  private _roomId: number; // 房间号
  private _isAnchor: boolean; // 是否为主播(老师)
  private _mode: TTrtcMode; // 模式
  private _resolutionMode: 0 | 1;
  private _speakerVolume = 100;
  private _volumeInterval = 0;

  // _devices 移到 TTrtcBase
  // private _devices: TTrtcDeviceInfo[] = [];
  private _devicesHandler: any = null;
  private _cameraId: string = undefined;
  private _cameraLabel: string | null = null;
  private _micLabal: String | null;
  private _speakerLabel: String | null;
  private _micId: string = undefined;
  private _speakerId: string = undefined;
  private _mirror = false; // 是否为镜像显示
  private _isCollegeWeb = false; // 是否为大教学web版本，合自 1.7.15 分支

  private _videoEncoderParams: any = null;
  private _curVideoResolution = 0;
  private _localStatus: TStreamStatus = {
    // 本地允许推流状态，
    join: false,
    audio: false,
    video: false,
    // 大教学模式特有，合自 1.7.15 分支
    // screen: false,
    // screenAudio: false,
    // screenPaused: false,
  };
  private _cachedLocalStatus: TStreamStatus = {
    audio: false,
    video: false,
  };
  private _screenStatus: TStreamStatus = {
    screen: false,
    screenAudio: false,
    screenPaused: false,
  };
  private _isCreatingPublishStream = false;

  private _remoteStreamsHolder: Map<string, TRemoteStreamHolder> = new Map();

  private _remoteUserList: string[] = [];

  private _rtcInfoInterval = 0;
  private _rtcInfo: any[] = [];
  private _volumeInfo: Map<string, number[]> = new Map();
  private _rtcLocalBeautyPlugin: any = null; // 本流美颜插件
  private _enableWebARPlugin = true; // 使用 webar 美颜插件
  private _loopCheckId: any = 0; // 循环检查id
  private _webARInstance: any = null; // webar 实例
  private _beautyCfg: any = null; // 美颜配置(默认关闭)
  private _virtualUrl: string = undefined; //  虚拟背景
  private _avatarEffectId: string = undefined; //  虚拟形象
  private _enableVirtualBackground = false; // 是否开启虚拟背景(默认关闭)
  private _loadVirtualResource = false; // 是否加载虚拟背景资源
  private _localBeautyStream: any = null; // 本地美颜流
  private _highAudioQuality = false; // 是否开启高清音质
  private _localAudioDom: HTMLElement = null;
  private _enableAIDenoiser = false; // 是否开启AI降噪
  private _rtcAIDenoiser: any = null; // AI降噪插件
  private _logCache: any = {}; // log cache
  private _fillMode = 1;
  private _qosPreference: any = TRTC.TYPE.QOS_PREFERENCE_CLEAR;

  // *******************************************主流程控制*******************************************
  public constructor() {
    super();
    TState.instance.registerState(TTrtcWeb.PublishState, 'Web端推流', false, [
      this._getClassName(),
    ]);
    TState.instance.registerState(TTrtcWeb.ScreenPublishState, 'Web端屏幕分享推流', false);
    TRTC.setLogLevel(2); // 只允许警告级别日志
    this.checkMediaDevices();

    // 设备变更检测
    this._devicesInfoConvert(this._getDevices(false) as Promise<MediaDeviceInfo[]>).then((devices) => {
      this._devices = devices;
      this._info('initDevices', JSON.stringify(this._devices));

      this._devicesHandler = this._deviceChangeHandler.bind(this);
      navigator.mediaDevices.addEventListener('devicechange', this._devicesHandler);

      // 更新是否notfound
      this._updateDeviceStatusByDeviceList('initDevices');
    });
  }


  public removeLogCallback(): void {
    console.log('Method not support');
  }

  /**
   * 检测当前环境是否支持
   */
  public checkSystemRequirements(): Promise<any> {
    return TRTC.isSupported();
  }

  public isWebRTCSupport(): boolean {
    // @ts-ignore
    return ['RTCPeerConnection', 'webkitRTCPeerConnection', 'RTCIceGatherer'].filter((e => e in window && window[e])).length > 0;
  }

  // mediaDevices polyfill
  public checkMediaDevices() {
    if (typeof navigator === 'undefined') {
      this._error('checkMediaDevices', 'navigator is null');
      return;
    }
    if (navigator.mediaDevices === undefined) {
      this._error('checkMediaDevices', 'navigator.mediaDevices is null');
      // @ts-ignore
      navigator.mediaDevices = {};
    }
    if (navigator.mediaDevices.getUserMedia === undefined) {
      navigator.mediaDevices.getUserMedia = (constraints) => {
        // @ts-ignore
        const getUserMedia = navigator.webkitGetUserMedia || navigator.mozGetUserMedia;
        if (!getUserMedia) {
          this._error('checkMediaDevices', 'navigator.mediaDevices.getUserMedia is null');
          return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
        }
        return new Promise((resolve, reject) => {
          getUserMedia.call(navigator, constraints, resolve, reject);
        });
      };
    }
    if (navigator.mediaDevices.enumerateDevices === undefined) {
      this._error('checkMediaDevices', 'navigator.mediaDevices.enumerateDevices is null');
      navigator.mediaDevices.enumerateDevices = () => Promise.reject(new Error('enumerateDevices is not implemented in this browser'));
    }
    if (navigator.mediaDevices.addEventListener === undefined) {
      this._error('checkMediaDevices', 'navigator.mediaDevices.addEventListener is null');
      navigator.mediaDevices.addEventListener = function () { };
    }
    if (navigator.mediaDevices.removeEventListener === undefined) {
      this._error('checkMediaDevices', 'navigator.mediaDevices.removeEventListener is null');
      navigator.mediaDevices.removeEventListener = function () { };
    }
  }

  /**
   * 模块初始化
   * @param sdkAppId  sdkAppId
   * @param userId    用户帐号
   * @param userSig   用户签名
   * @param mode      TRTC模式
   */
  @logMethod
  public init(
    sdkAppId: number,
    userId: string,
    userSig: string,
    mode = TTrtcMode.RTC, // 不再支持live流
  ): Promise<void> {
    const executor = this.newExecutor(TTrtcAction.Init);
    setTimeout(() => {
      this._sdkAppId = sdkAppId;
      this._userId = userId;
      this._userSig = userSig;
      this._isCollegeWeb = TMain.instance.isCollegeClass() && TSession.instance.isWeb();

      const proxyServer = {
        loggerProxy: 'api.my-imcloud.com',
        // scheduleProxy: 'schedule.cloud-rtc.com'
      };
      this._info('init', JSON.stringify({
        sdkAppId,
        userId,
        mode,
        // loggerProxy: proxyServer?.loggerProxy,
        // scheduleProxy: proxyServer?.scheduleProxy,
      }));
      this._mode = mode;
      this._audioContext = new AudioContext();
      // 监听事件
      // 用户进房
      this._client.on(TRTC.EVENT.REMOTE_USER_ENTER, (event) => {
        const { userId } = event;
        if (!userId.startsWith('tic_push_user')) {
          this._remoteUserList.push(userId);
          TEvent.instance.notify(TTrtcEvent.Stream_Added, {
            userId,
          });
        }
      });

      // 用户退房
      this._client.on(TRTC.EVENT.REMOTE_USER_EXIT, (event) => {
        const { userId } = event;
        this._remoteUserList.splice(this._remoteUserList.indexOf(userId), 1);
      });

      // 远端用户发布了视频
      this._client.on(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, (event) => {
        // [TODO] 渲染远端视频
        const { userId, streamType } = event;
        const isScreen = streamType === 'sub';
        if (isScreen) {
          this._client.startRemoteVideo({
            view: 'screenplayer__view',
            userId,
            streamType,
            option: {
              fillMode: 'contain',
            },
          }).catch((error) => {
            this._error('startRemoteVideo', `RemoteStream play failed: [${userId}], error: ${error.message_}`);
          });
        }

        TEvent.instance.notify(isScreen ? TTrtcEvent.SubStream_Changed : TTrtcEvent.Video_Changed, {
          userId,
          available: true,
          state: `updateStream of reason: remote video available, type: ${streamType}`,
        });
      });
      // 远端用户停止发布视频
      this._client.on(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, (event) => {
        const { userId, streamType } = event;
        const isScreen = streamType === 'sub';

        TEvent.instance.notify(isScreen ? TTrtcEvent.SubStream_Changed : TTrtcEvent.Video_Changed, {
          userId,
          available: false,
          state: `updateStream of reason: remote video unavailable, type: ${streamType}`,
        });
      });
      this._client.on(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, (event) => {
        const { userId } = event;
        if (userId.startsWith('tic_push_user')) {
          return;
        }
        TEvent.instance.notify(TTrtcEvent.Audio_Changed, {
          userId,
          available: true,
        });
      });
      this._client.on(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, (event) => {
        const { userId } = event;
        if (userId.startsWith('tic_push_user')) {
          return;
        }
        TEvent.instance.notify(TTrtcEvent.Audio_Changed, {
          userId,
          available: false,
        });
      });
      // this._client.on(TRTC.EVENT.CONNECTION_STATE_CHANGED, (event) => {
      //   const prevState = event.prevState;
      //   const curState = event.state;
      //   if (prevState == 'DISCONNECTED' && curState == 'CONNECTING') {
      //     // 正在尝试建立连接，调用进房接口或者 SDK 自动重连时触发。
      //   } else if (prevState == 'CONNECTING' && curState == 'DISCONNECTED') {
      //     // 连接建立失败，当正在连接时调用退房接口中断连接或者经过 SDK 重试后任然连接失败时触发。
      //   } else if (prevState == 'CONNECTING' && curState == 'CONNECTED') {
      //     // 连接建立成功，连接成功时触发。
      //   } else if (prevState == 'CONNECTED' && curState == 'DISCONNECTED') {
      //     // 连接中断，调用退房接口或者当网络异常导致连接断开时触发。
      //     TEvent.instance.notify(TMainEvent.Broken_Network, {}, true);
      //   }
      // });
      // 网络质量
      this._client.on(TRTC.EVENT.NETWORK_QUALITY, (event) => {
        const { uplinkLoss, downlinkLoss, uplinkRTT, downlinkRTT, uplinkNetworkQuality,
          downlinkNetworkQuality } = event;
        const size = getVideoSizeFromResolution(TAV.instance.lastVideoResolution);
        const trtcStatistics = new TTrtcStatistics();
        trtcStatistics.upLoss = uplinkLoss;
        trtcStatistics.downLoss = downlinkLoss;
        trtcStatistics.upVideoPixels = size.width * size.height;
        trtcStatistics.downVideoPixels = 0;
        trtcStatistics.rtt = Math.max(
          uplinkRTT || 0,
          downlinkRTT || 0,
        );
        trtcStatistics.remoteStatisticsArray = [];
        this._remoteUserList.forEach((uid) => {
          const videoTrack = this._client.getVideoTrack({
            userId: uid,
          });
          const settings = videoTrack?.getSettings();
          if (
            settings
            && settings.width
            && settings.height
            && settings.frameRate
            && !uid.startsWith('tic_push_user')
          ) {
            trtcStatistics.downVideoPixels
              += settings.width * settings.height;
          }
        });
        TEvent.instance.notify(TTrtcEvent.Network_Quality, event, false);
        TEvent.instance.notify(TTrtcEvent.Network_Statistis, trtcStatistics);
      });
      // 异常
      this._client.on(TRTC.EVENT.ERROR, (error) => {
        const { code, extraCode, message } = error;
        this._error('error', `${code}, ${message}, ${extraCode}`);
        this.streamErrorHandler(code);
      });
      // 被踢出房间
      this._client.on(TRTC.EVENT.KICKED_OUT, (event) => {
        const { reason } = event;
        this._error('client-banned', `reason: ${reason}`);
      });
      // 音频
      this._client.on(TRTC.EVENT.AUDIO_VOLUME, (event) => {
        event.result.forEach(({ userId, volume }) => {
          const isMe = userId === '';
          const volumeUserID = isMe ? this._userId : userId;
          const volumeVal = volume * 100;
          paddingVolumeInfo(this?._volumeInfo, {
            uid: volumeUserID,
            val: volumeVal,
          });
          TEvent.instance.notify(
            TTrtcEvent.Volume_Update,
            {
              userId: volumeUserID,
              volume: volumeVal,
            },
            false,
          );
        });
      });

      this._client.on(TRTC.EVENT.AUTOPLAY_FAILED, (event) => {
        const { userId, resume } = event;
        TEvent.instance.notify(
          TTrtcEvent.AUTOPLAY_FAILED,
          {
            userId,
            resume,
          },
          false,
        );
      });

      this._client.on(TRTC.EVENT.STATISTICS, async (statistics) => {
        const { rtt, remoteStatistics, localStatistics, bytesSent, bytesReceived } = statistics;
        const { video, audio } = localStatistics;
        const infos: any[] = [];
        const localVideoStats: any = {};
        const remoteVideoStats: any = {};
        const localAudioStats: any = {};
        const remoteAudioStats: any = {};
        localAudioStats[this._userId] = audio;
        video.forEach(({ width, height, videoType, frameRate }) => {
          localVideoStats[this._userId] = {
            frameWidth: width,
            frameHeight: height,
            frameRate,
            bytesSent,
            bytesReceived,
          };
          const isSub = videoType === 'sub';
          if (width && height) {
            infos.push({
              UserId: this._userId,
              StreamType: isSub ? 'sub' : 'main',
              Resolution: `${width}x${height}`,
            });
          }
        });
        remoteStatistics.forEach((i) => {
          const { userId, video, audio } = i;
          remoteAudioStats[userId] = audio;
          video.forEach(({ width, height, videoType, frameRate }) => {
            remoteVideoStats[userId] = {
              frameWidth: width,
              frameHeight: height,
              frameRate,
            };
            const isSub = videoType === 'sub';
            if (width && height) {
              infos.push({
                UserId: userId,
                StreamType: isSub ? 'sub' : 'main',
                Resolution: `${width}x${height}`,
              });
            }
          });
        });
        this._rtcInfo = infos;

        await this.checkAudioVideoStatus({
          localVideoStats,
          localAudioStats,
          remoteVideoStats,
          remoteAudioStats,
        });
      });
      executor.resolve();
    }, 0);
    return executor.getPromise();
  }

  /**
   * 模块反初始化
   */
  @logMethod
  public unInit(): Promise<void> {
    this._info('unInit');
    return this.stopCameraTest()
      .then(() => this.stopMicTest())
      .then(() => this.stopSpeakerTest())
      .then(() => this.stopLocalVideo())
      .then(() => this.stopLocalAudio())
      .then(() => this.stopScreenShare())
      .then(() => {
        this._client.off(TRTC.EVENT.REMOTE_USER_ENTER, () => { });
        this._client.off(TRTC.EVENT.REMOTE_USER_EXIT, () => { });
        this._client.off(TRTC.EVENT.REMOTE_AUDIO_AVAILABLE, () => { });
        this._client.off(TRTC.EVENT.REMOTE_AUDIO_UNAVAILABLE, () => { });
        this._client.off(TRTC.EVENT.REMOTE_VIDEO_AVAILABLE, () => { });
        this._client.off(TRTC.EVENT.REMOTE_VIDEO_UNAVAILABLE, () => { });
        this._client.off(TRTC.EVENT.ERROR, () => { });
        this._client.off(TRTC.EVENT.AUDIO_VOLUME, () => { });
        this._client.off(TRTC.EVENT.NETWORK_QUALITY, () => { });
        if (this._devicesHandler) {
          navigator.mediaDevices.removeEventListener('devicechange', this._devicesHandler);
        }
        this._client = null;
      });
  }
  // 找到异常的userId
  @logMethod
  public _logErrorDiffUserId(plist: TPermissionInfo[], opt: any) {
    let hasError = false;
    const { localVideoStats, localAudioStats, remoteVideoStats, remoteAudioStats } = opt;
    // const permissionObj = {};
    // 找到permission里有， 但实际没有流的user
    plist.forEach((p, index) => {
      const userId = p.userId;
      const jsonPermission = JSON.stringify(p);
      const lvs = localVideoStats[userId];
      const las = localAudioStats[userId];
      const rvs = remoteVideoStats[userId];
      const ras = remoteAudioStats[userId];
      // 如果四种流都找不到 且已经开启视频和音频 则异常
      if (!(lvs || las || ras || rvs)) {
        hasError = true;
        if (p.micState !== TDeviceStatus.Open || p.cameraState !== TDeviceStatus.Open) {
          this._logWithCacheCheck('TRTC-no-stream-in-classroom', `${userId} no stream, device is close; permission is ${jsonPermission}`, 'warn');
        } else {
          this._logWithCacheCheck('TRTC-no-stream-in-classroom', `${userId} no stream, permission is ${jsonPermission}`);
        }
      } else { // 否则记录一下
        if (lvs) {
          lvs._hasPermission = 1;
        }
        if (las) {
          las._hasPermission = 1;
        }
        if (ras) {
          ras._hasPermission = 1;
        }
        if (rvs) {
          rvs._hasPermission = 1;
        }
      }
    });
    const jsonLVS = JSON.stringify(localVideoStats);
    const jsonLAS = JSON.stringify(localAudioStats);
    const jsonRVS = JSON.stringify(remoteVideoStats);
    const jsonRAS = JSON.stringify(remoteAudioStats);
    // 如果存在没有标记_hasPermission的流， 则说明异常
    // Object.keys(localVideoStats).forEach((userId) => {
    //   if (!localVideoStats[userId]._hasPermission) {
    //     this._logWithCacheCheck(
    //       'TRTC-local-video-with-out-classroom',
    //       `${userId} has stream, but no permission; videoStats is ${jsonLVS}`,
    //     );
    //     hasError = true;
    //   }
    // });
    // Object.keys(localAudioStats).forEach((userId) => {
    //   if (!localAudioStats[userId]._hasPermission) {
    //     hasError = true;
    //     this._logWithCacheCheck(
    //       'TRTC-local-audio-with-out-classroom',
    //       `${userId} has stream, but no permission; audioStats is ${jsonLAS}`,
    //     );
    //   }
    // });
    Object.keys(remoteVideoStats).forEach((userId) => {
      if (userId.startsWith('tic_push_user')) {
        return;
      }
      if (!remoteVideoStats[userId]._hasPermission) {
        hasError = true;
        this._logWithCacheCheck('TRTC-remote-video-with-out-classroom', `${userId} has stream, but no permission; videoStats is ${jsonRVS}`);
      }
    });
    Object.keys(remoteAudioStats).forEach((userId) => {
      if (userId.startsWith('tic_push_user')) {
        return;
      }
      if (!remoteAudioStats[userId]._hasPermission) {
        hasError = true;
        this._logWithCacheCheck('TRTC-remote-audio-with-out-classroom', `${userId} has stream, but no permission; videoStats is ${jsonRAS}`);
      }
    });
    // 一切正常
    if (!hasError) {
      this._info('TRTC-stream-in-classroom-all-ok', `stream and permission are right; videoStats is ${jsonLVS}`);
    }
  }
  // 上报去重日志， 上报之前先检查是否已有, 如果已有&&在gap时间范围内， 则不再上报
  @logMethod
  public _logWithCacheCheck(key: string, value: string, level?: string, gap?: number) {
    gap = gap || 120000; // 120秒
    level = level || 'info';
    const timestamp = + new Date();
    const cache = this._logCache;
    const data = cache[key] || {};
    const oldValue = data.value;
    const oldTimestamp = data.timestamp;
    // cache里已存在 且时间范围内
    if (oldValue && oldTimestamp && (timestamp - oldTimestamp < gap)) {
      console.warn('log exists, no report', key);
    } else {
      if (level === 'info') {
        this._info(key, value);
      } else if (level === 'warn') {
        this._warn(key, value);
      } else if (level === 'error') {
        this._error(key, value);
      } else {
        this._debug(key, value);
      }
      this._logCache[key] = {
        value,
        timestamp,
      };
    }
  }
  @logMethod
  public async checkAudioVideoStatus(opt: any) {
    opt = opt || {};
    try {
      const classInfo = TSession.instance.getClassInfo();
      const teacherId = classInfo.teacherId;
      const { localVideoStats, localAudioStats, remoteVideoStats, remoteAudioStats } = opt;
      const plist = TMain.instance.getPermissionList();
      const speakerVolume = await TMain.instance.getSpeakerVolume();
      const micVolume = await TMain.instance.getMicVolume();
      // 找出异常userId
      this._logErrorDiffUserId(plist, opt);
      // 找出异常流状态
      plist.forEach((p, index) => {
        const userId = p.userId;
        const jsonPermission = JSON.stringify(p);
        const selfIsTeacher = teacherId === userId; // 判断当前用户是不是老师
        const roleStr = selfIsTeacher ? '-teacher' : '-other';
        if (userId === this._userId) { // 检查自己的音视频,这里都是本地流
          let myVideoStat = localVideoStats[userId];
          let myAudioStat = localAudioStats[userId];
          // 取不到音频或者视频信息
          if (!myAudioStat || !myVideoStat) {
            if (!myAudioStat) {
              this._logWithCacheCheck(`TRTC-local-no-audio-stat${roleStr}`, `no audio stat, permission is ${jsonPermission}`);
            }
            if (!myVideoStat) {
              this._logWithCacheCheck(`TRTC-local-no-video-stat${roleStr}`, `no video stat, permission is ${jsonPermission}`);
            }
            return;
          }
          myAudioStat = myAudioStat || {};
          myVideoStat = myVideoStat || {};
          const myJsonVideoStat = JSON.stringify(myVideoStat);
          const myJsonAudioStat = JSON.stringify(myAudioStat);
          const mainEvtCallbackData = { // 传递给用户的callback data
            userPermission: p,
            videoStat: myVideoStat,
            audioStat: myAudioStat,
            isTeacher: selfIsTeacher,
          };
          // 有权限， 有开启， 但视频流没宽高，算异常
          if (p.cameraState === TDeviceStatus.Open && (myVideoStat.frameWidth < 1 || myVideoStat.frameHeight < 1)) {
            TEvent.instance.notify(TMainEvent.Error_No_Frame_Content, mainEvtCallbackData);
            this._logWithCacheCheck(`TRTC-local-no-frame-content${roleStr}`, `frame size is zero; permission is ${jsonPermission}; videoStat:${myJsonVideoStat}; audioStat:${myJsonAudioStat}`, 'warn');
          }
          if (speakerVolume < 1) { // 0-100 如果没有扬声器音量，算异常
            TEvent.instance.notify(TMainEvent.Error_No_Speaker_Volume, mainEvtCallbackData);
            this._logWithCacheCheck(`TRTC-local-no-speaker-volume${roleStr}`, `speaker volume is 0; permission is ${jsonPermission}; videoStat:${myJsonVideoStat}; audioStat:${myJsonAudioStat}`);
          }
          if (micVolume < 1) { // 如果没有麦克风音量，算异常
            TEvent.instance.notify(TMainEvent.Error_No_Mic_Volume, mainEvtCallbackData);
            this._logWithCacheCheck(`TRTC-local-no-mic-volume${roleStr}`, `mic volume is 0; permission is ${jsonPermission}; videoStat:${myJsonVideoStat}; audioStat:${myJsonAudioStat}`);
          }
        } else { // 远程流的分析
          const videoStat = remoteVideoStats[userId] || {};
          const audioStat = remoteAudioStats[userId] || {};
          const mainEvtCallbackData = { // 传递给用户的callback data
            userPermission: p,
            videoStat,
            audioStat,
            isTeacher: selfIsTeacher,
          };
          const jsonVideoStat = JSON.stringify(videoStat);
          const jsonAudioStat = JSON.stringify(audioStat);
          if (videoStat.frameWidth < 1 || videoStat.frameHeight < 1) {
            TEvent.instance.notify(TMainEvent.Error_No_Frame_Content, mainEvtCallbackData);
            this._logWithCacheCheck(`TRTC-remote-no-frame-content${roleStr}`, `frame size is zero; permission is ${jsonPermission}; videoStat:${jsonVideoStat}; audioStat:${jsonAudioStat}`);
          }
        }
      });
    } catch (e) {
      console.error('checkAudioVideoStatus error', e);
    }
  }
  /**
   * 加入音视频房间
   * @param roomId    音视频房间 ID
   * @param isAnchor  是否主播角色
   */
  @logMethod
  public async join(roomId: number, isAnchor: boolean): Promise<void> {
    this._info('join', `enter=>${roomId}, isAnchor ${isAnchor}, localStatus.join ${this._localStatus.join}`);
    const executor = this.newExecutor(TTrtcAction.Join);
    if (!this._localStatus.join) {
      this._roomId = roomId;
      this._isAnchor = isAnchor; // 是否是主播
      const trtcRole = isAnchor ? TRTC.TYPE.ROLE_ANCHOR : TRTC.TYPE.ROLE_AUDIENCE;
      this._info('join', `trtcClient.join, ${roomId} ${trtcRole}`);
      TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Start_Join_TRTC);
      await this._client
        .enterRoom({
          roomId,
          sdkAppId: this._sdkAppId,
          userId: this._userId,
          userSig: this._userSig,
          role: trtcRole,
          scene: this._mode === TTrtcMode.RTC ? TRTC.TYPE.SCENE_RTC : TRTC.TYPE.SCENE_LIVE,
          enableAutoPlayDialog: false,
          proxy: {
            loggerProxy: 'https://api.my-imcloud.com',
          },
        })
        .then(() => {
          this.updatePublishStatus({ join: true });
          TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Join_TRTC_Success);
        })
        .then(() => {
          executor.resolve();
        })
        .catch((err: any) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('加入音视频房间失败'),
            err.toString(),
          ));
          TState.instance.setState(TMainState.Project_Core_Process, TMainEvent.Core_process_Join_TRTC_Error);
        });
    }
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 退出音视频房间
   */
  @logMethod
  public quit(): Promise<void> {
    this._info('quit', `localStatus.join ${this._localStatus.join}`);
    const executor = this.newExecutor(TTrtcAction.Quit);
    if (this._localStatus.join) {
      if (this._rtcInfoInterval) {
        clearInterval(this._rtcInfoInterval);
        this._rtcInfoInterval = 0;
      }
      this._info('quit', 'trtcClient.leave');
      this._client.exitRoom().then(() => {
        this.updatePublishStatus({
          audio: false,
          video: false,
        });
        executor.resolve();
        // @ts-ignore
        this._client.off('*'); // // 解除所有事件绑定
        this.updatePublishStatus({ join: false });
      })
        .catch((err: any) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('退出音视频房间失败'),
            err.toString(),
          ));
        });
    } else {
      executor.resolve();
    }

    return executor.getPromise();
  }

  /**
   * 切换用户角色
   * @param isAnchor    是否主播角色
   */
  @logMethod
  public switchRole(isAnchor: boolean): Promise<void> {
    this._info('switchRole', `isAnchor ${isAnchor}`);
    const executor = this.newExecutor(TTrtcAction.SwitchRole);
    this._isAnchor = isAnchor;
    this._client
      .switchRole(isAnchor ? TRTC.TYPE.ROLE_ANCHOR : TRTC.TYPE.ROLE_AUDIENCE)
      .then(() => {
        executor.resolve();
      })
      .catch((err: any) => {
        executor.reject(new TCICError(
          err.code,
          i18next.t('切换用户角色遇到一些问题'),
          err.toString(),
        ));
      });
    return executor.getPromise();
  }

  /**
   * 获取上报用的是RTCInfo
   */
  public getRtcInfo() {
    return { video: this._rtcInfo, audio: this?._volumeInfo };
  }

  // *******************************************视频控制*******************************************
  /**
   * 设置本地视频参数
   * @param option      本地视频参数
   */
  @logMethod
  public setLocalVideoParams(option: TTrtcLocalVideoParams): Promise<void> {
    this._info('setLocalVideoParams', JSON.stringify(option));
    this._mirror = option.mirror === 1;
    this._fillMode = option.mode !== undefined ? option.mode : this._fillMode;
    const haveVideoTrack = this._client.getVideoTrack();
    if (haveVideoTrack) {
      // https://web.sdk.qcloud.com/trtc/webrtc/v5/doc/zh-cn/TRTC.html#updateLocalVideo
      // 根据v5 sdk文档，mirror有'view' | 'publish' | 'both' | boolean 这几个值
      // 产品需求本地镜像和远端要同步， 所以此处选择both
      this._client.updateLocalVideo({
        option: {
          fillMode: this._testVideoDom ? 'cover' : (option.mode === 1 ? 'contain' : 'cover'),
          mirror: this._mirror ? 'both' : false,
        },
      });
    }
    return Promise.resolve();
  }
  /**
   * 设置渲染渲染参数
   */
  setRemoteVideoParams(option: TTrtcLocalVideoParams): Promise<void> {
    this._info('setRemoteRenderParams', 'web do nothing');
    throw new Error('Method not implemented.');
  }
  /**
   * 设置视频编码参数
   * @param resolution  编码分辨率
   * @param fps         编码FPS
   * @param bitrate     编码码率
   */
  @logMethod
  public async setVideoEncoderParam(
    resolution: TTrtcVideoResolution,
    fps: number,
    bitrate: number,
    resolutionMode: 0 | 1,
  ): Promise<void> {
    this._resolutionMode = resolutionMode;
    this._info('setVideoEncoderParam', JSON.stringify({
      resolution,
      fps,
      bitrate,
      resolutionMode,
    }));
    if (
      this._curVideoResolution === resolution
      && this._videoEncoderParams
      && this._videoEncoderParams.frameRate
    ) {
      if (Math.abs(this._videoEncoderParams.frameRate - fps) < 5) {
        // 分辨率未变时，忽略小于5的帧率变更避免Web端重新创建流引起观看闪烁
        return Promise.resolve();
      }
    }
    this._curVideoResolution = resolution;
    this._videoEncoderParams = Object.assign(
      {
        frameRate: fps,
        bitrate,
      },
      getVideoSizeFromResolution(resolution, this._resolutionMode),
    );
    console.log(`----render setVideoEncoderParam ${new Date().getTime()}`, this._publishStatus);
    if (this._client) {
      const videoTrack = this._client.getVideoTrack();
      if (videoTrack) {
        this._info('setVideoEncoderParam', `${JSON.stringify(this._videoEncoderParams)}`);
        await this._client.updateLocalVideo({
          option: {
            profile: this._videoEncoderParams,
          },
        });
      }
    }

    return Promise.resolve();
  }

  @logMethod
  public setVideoResolutionMode(resolutionMode: 0 | 1) {
      this._resolutionMode = resolutionMode;
      const videoSize = getVideoSizeFromResolution(this._curVideoResolution, this._resolutionMode);
      if (this._videoEncoderParams?.width === videoSize.width && this._videoEncoderParams?.height === videoSize.
        height) {
        return;
      }
      this._videoEncoderParams = {
        ...this._videoEncoderParams,
        ...videoSize,
      };
      this._info('setVideoResolutionMode', `${JSON.stringify(this._videoEncoderParams)}`);
      const currentVideoTrack = this._client.getVideoTrack();
      if (!currentVideoTrack) {
        this._info('setVideoResolutionMode', `not start`);
        return;
      }
      return this._client.updateLocalVideo({
        option: {
          profile: this._videoEncoderParams,
        },
      });
  }

  /**
   * 开启本地视频采集及渲染
   * @param dom         用于渲染视频画面的DOM节点
   */
  @logMethod
  public startLocalVideo(dom: HTMLElement): Promise<void> {
    this._info('startLocalVideo', `start, has dom ${!!dom}`);
    const startTime = Date.now();
    const executor = this.newExecutor(TTrtcAction.StartLocalVideo, true, 20000);
    if (this._cameraTestStarted) {
      this._info('startLocalVideo', 'isTestingCamera, reject');
      executor.reject(new TCICError(-1, '', i18next.t('正在设备检测，打开摄像头失败')));
      return executor.getPromise();
    }
    if (executor.isConflicted()) {
      // 冲突时直接返回
      this._info('startLocalVideo', `${executor.getIdentifier()} isConflicted`);
      return executor.getPromise();
    }
    if (!dom && !this._localVideoDom) {
      this._error('startLocalVideo', 'no dom, reject');
      executor.reject(new TCICError(-5, i18next.t('Web端本地视频采集必须传入DOM节点')));
      return executor.getPromise();
    }

    let localSucc = false;
    this.stopLocalVideo()
      .then(async () => {
        this._info('startLocalVideo', 'getCameraDeviceId');
        await this.getCameraDeviceId();
        const videoSize = getVideoSizeFromResolution(this._curVideoResolution, this._resolutionMode);
        this._info('startLocalVideo', `getCameraDeviceId succ, ${this._cameraId} ${this._cameraLabel} ${this._fillMode}this._curVideoResolution ${this._curVideoResolution} this._resolutionMode ${this._resolutionMode}`);
        this._videoEncoderParams = {
          ...this._videoEncoderParams,
          ...videoSize,
        };
        this._info('startLocalVideo', `_videoEncoderParams ${JSON.stringify(this._videoEncoderParams)}`);
        this._client.startLocalVideo({
          view: dom || this._localVideoDom,
          option: {
            cameraId: this._cameraId,
            fillMode: this._fillMode === 1 ? 'contain' : 'cover',
            mirror: this._mirror,
            profile: this._videoEncoderParams,
            qosPreference: this._qosPreference,
          },
        }).then(async () => {
          localSucc = true;
          this._localVideoDom = dom || this._localVideoDom;
          this._processCameraResult(executor, true, null, { startTime });
          try {
            if (this._avatarEffectId) {
              await this.setAvatar(this._avatarEffectId);
            }
            if (this._virtualUrl) {
              await this.setVirtualImg(true, this._virtualUrl);
            }
            if (this._avatarEffectId) {
              await this.setAvatar(this._avatarEffectId);
            }
            if (this._beautyCfg) {
              await this.setBeautyParam(this._beautyCfg.whiten, this._beautyCfg.lift, this._beautyCfg.eye);
            }
          } catch (error) {
            this._error('setBeautyParams', JSON.stringify({
              error,
            }));
          }
        })
          .catch((err) => {
            this._processCameraResult(executor, false, err, { startTime });
          });
      })
      .catch(async (err: any) => {
        this._error('startLocalVideo', `error, localSucc ${localSucc}, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
        await this._processCameraResult(executor, false, err, { startTime });
      });
    return executor.getPromise();
  }
  /**
   * 关闭本地视频采集及渲染
   */
  @logMethod
  public stopLocalVideo(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalVideo',
      `has localVideoStream ${!!this._localVideoStream}, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    const executor = this.newExecutor(TTrtcAction.StopLocalVideo);
    console.debug('-------stopLocalVideo------', new Date().getTime());
    if (this._webARInstance) {
      this._webARInstance.destroy();
      this._webARInstance = null;
    }
    this._client.stopLocalVideo().then(() => {
      this._localVideoStream = null;
      this._localVideoDom = null;
      this._setState(TMainState.Video_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
      this._setState(TMainState.Video_Capture, false);
      this.updatePublishStatus({
        video: false,
      });
      executor.resolve();
    })
      .catch((err) => {
        executor.reject(new TCICError(
          err.code,
          i18next.t('关闭本地视频采集遇到一些问题'),
          err.toString(),
        ));
      });
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽自己的视频画面，屏蔽后不推流
   * @param mute        是否屏蔽
   */
  @logMethod
  public muteLocalVideo(mute: boolean): Promise<void> {
    this._info('muteLocalVideo', `mute ${mute}`);
    const executor = this.newExecutor(TTrtcAction.MuteLocalVideo);
    this._info(
      TTrtcAction.MuteLocalVideo,
      `${executor.getIdentifier()} : ${JSON.stringify({ mute })}`,
    );
    if (this._localStatus.video === !mute) {
      executor.resolve();
    } else {
      const isVideoStarted = this._client.getVideoTrack();
      if (isVideoStarted) {
        this._client.updateLocalVideo({
          mute,
        }).then(() => {
          executor.resolve();
        })
          .catch(() => {
            executor.reject(new TCICError(-5, i18next.t('打开本地视频推送失败，请先关掉屏幕共享')));
          });
      } else {
        executor.resolve();
      }
    }
    return executor.getPromise();
  }

  /**
   * 开始接收并渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param isFitMode   是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  @logMethod
  public startRemoteVideo(
    userId: string,
    type: TTrtcVideoStreamType,
    dom: HTMLElement,
    isFitMode?: boolean,
  ): Promise<void> {
    let streamType: any = TRTC.TYPE.STREAM_TYPE_MAIN;
    if (type === TTrtcVideoStreamType.Sub) {
      streamType = TRTC.TYPE.STREAM_TYPE_SUB;
    }

    const isJoined = this._localStatus?.join;
    this._info('startRemoteVideo', `userId ${userId}, type ${type}, isJoined ${isJoined}`);
    const executor = this.newExecutor(TTrtcAction.StartRemoteVideo, false, 0);
    // 如果未进房先进房
    TState.instance.promiseState(TMainState.Joined_TRTC, true).then(() => {
      setTimeout(() => {
        this._client.startRemoteVideo({
          userId,
          view: dom,
          streamType,
          option: {
            fillMode: isFitMode ? 'contain' : 'cover',
          },
        }).then(() => {
          executor.resolve();
          this._info('startRemoteVideo', `success userId ${userId}, type ${type}`);
        })
          .catch((err) => {
            executor.reject(new TCICError(-5, i18next.t('远端视频流不存在'), err?.message));
          });
      }, 50); // 稍微延迟50ms 刚进房不能立刻调用startRemote
    });
    return executor.getPromise();
  }

  /**
   * 更新远端视频画面渲染模式
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param isFitMode    是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  @logMethod
  public updateRemoteVideoFitMode(
    userId: string,
    type: TTrtcVideoStreamType,
    isFitMode: boolean,
  ): Promise<void> {
    const isScreen = type === TTrtcVideoStreamType.Sub;
    return this._client.updateRemoteVideo({
      userId,
      streamType: isScreen ? TRTC.TYPE.STREAM_TYPE_SUB : TRTC.TYPE.STREAM_TYPE_MAIN,
      option: {
        fillMode: isFitMode ? 'contain' : 'cover',
      },
    });
  }

  /**
   * 停止接收和渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  @logMethod
  public stopRemoteVideo(
    userId: string,
    type: TTrtcVideoStreamType,
  ): Promise<void> {
    this._info('stopRemoteVideo', `userId ${userId}, type ${type}`);
    const executor = this.newExecutor(TTrtcAction.StopRemoteVideo, false, 0);
    const isScreen = type === TTrtcVideoStreamType.Sub;
    const streamType = isScreen ? TRTC.TYPE.STREAM_TYPE_SUB : TRTC.TYPE.STREAM_TYPE_MAIN;
    if (this._client) {
      this._client.stopRemoteVideo({ userId, streamType })
        .then(() => executor.resolve())
        .catch((err) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('关闭远端视频遇到一些问题'),
            err.toString(),
          ));
        });
    } else {
      executor.resolve();
    }

    return executor.getPromise();
  }

  /**
   * 视频截图
   * @param userId      要处理的用户ID
   * @param streamType  要处理的视频流类型
   */
  @logMethod
  public snapshotVideo(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<TTrtcSnapshot> {
    this._info('snapshotVideo', `userId ${userId}, streamType ${streamType}`);
    return Promise.resolve(undefined);
  }

  // *******************************************音频控制*******************************************
  /**
   * 开始本地音频采集
   * @param highAudioQuality  高清音质
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  @logMethod
  public startLocalAudio(
    highAudioQuality: boolean,
    dom?: HTMLElement,
  ): Promise<void> {
    this._info('startLocalAudio', `start, highAudioQuality ${highAudioQuality}, has dom ${!!dom}`);
    const startTime = Date.now();
    const executor = this.newExecutor(TTrtcAction.StartLocalAudio, true, 20000);
    if (this._micTestStarted) {
      this._info('startLocalAudio', 'isTestingMic, reject');
      executor.reject(new TCICError(-1, '', i18next.t('正在设备检测，打开麦克风失败')));
      return executor.getPromise();
    }
    if (executor.isConflicted()) {
      this._info('startLocalAudio', `${executor.getIdentifier()} isConflicted`);
      return executor.getPromise();
    }
    if (!dom) {
      this._info('startLocalAudio', 'no dom, reject');
      executor.reject(new TCICError(-5, i18next.t('Web端本地音频采集必须传入DOM节点')));
      return executor.getPromise();
    }

    this._highAudioQuality = highAudioQuality;
    let localSucc = false;
    this.stopLocalAudio()
      .then(async () => {
        this._info('startLocalAudio', 'getMicDeviceId');
        await this.getMicDeviceId();
        this._info('startLocalAudio', `getMicDeviceId succ, ${this._micId}`);

        await this._client.startLocalAudio({
          option: {
            microphoneId: this._micId,
            earMonitorVolume: 0,
            profile: highAudioQuality ? TRTC.TYPE.AUDIO_PROFILE_HIGH : TRTC.TYPE.AUDIO_PROFILE_STANDARD,
          },
        }).then(async () => {
          localSucc = true;
          this._localAudioDom = dom;
          if (this._enableAIDenoiser) {
            this.startAiDenoiser();
          }
          await this._processMicResult(executor, true, null, { startTime });
        })
          .catch(async (err) => {
            await this._processMicResult(executor, false, err, { startTime });
          });
      })
      .catch(async (err: any) => {
        this._error('startLocalAudio', `error, localSucc ${localSucc}, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
        await this._processMicResult(executor, false, err, { startTime });
      });
    return executor.getPromise();
  }

  /**
   * 停止本地音频采集
   */
  @logMethod
  public stopLocalAudio(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalAudio',
      `has localAudioStream ${!!this._localAudioStream}, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    const executor = this.newExecutor(TTrtcAction.StopLocalAudio);
    this._client.stopLocalAudio().then(() => {
      this._setState(TMainState.Audio_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
      this._setState(TMainState.Audio_Capture, false);
      this.updatePublishStatus({
        audio: false,
      });
      this.stopAiDenoiser();
      executor.resolve();
    })
      .catch((err) => {
        executor.reject(new TCICError(
          err.code,
          i18next.t('关闭本地音频采集遇到一些问题'),
          err.toString(),
        ));
      });
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽自己的声音
   * @param mute        是否屏蔽
   */
  @logMethod
  public muteLocalAudio(mute: boolean): Promise<void> {
    this._info('muteLocalAudio', `mute ${mute}`);
    const executor = this.newExecutor(TTrtcAction.MuteLocalAudio);
    this._info(
      TTrtcAction.MuteLocalAudio,
      `${executor.getIdentifier()} : ${JSON.stringify({ mute })}`,
    );
    if (this._localStatus.audio === !mute) {
      // 没有变化
      executor.resolve();
    }
    if (!this._client) {
      executor.resolve();
      return executor.getPromise();
    }
    const isStartLocalAudio = this._client.getAudioTrack();
    if (isStartLocalAudio) {
      this._client.updateLocalAudio({
        mute,
      }).then(() => {
        executor.resolve();
        this.updatePublishStatus({
          audio: !mute,
        });
      })
        .catch((err) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('控制本地音频推送遇到一些问题'),
            err.toString(),
          ));
        });
    } else {
      executor.resolve();
    }
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽远端的声音
   * @param userId      要处理的用户ID
   * @param mute        是否屏蔽
   */
  @logMethod
  public muteRemoteAudio(userId: string, mute: boolean): Promise<void> {
    this._info('muteRemoteAudio', `userId ${userId}, mute ${mute}`);
    const executor = this.newExecutor(TTrtcAction.MuteRemoteAudio, false);
    if (this._client) {
      this._client.muteRemoteAudio(userId, mute).then(() => executor.resolve())
        .catch(err => executor.reject(new TCICError(
          err.code,
          'Mute Remote audio error',
          err.toString(),
        )));
    } else {
      executor.resolve();
    }
    return executor.getPromise();
  }

  /**
   * 开启音量大小回调，回调直接通过事件抛出
   * @param interval    回调间隔(最小100ms，0为关闭)
   */
  @logMethod
  public enableVolumeEvaluation(interval: number): Promise<void> {
    this._info('enableVolumeEvaluation', `interval: ${interval}`);
    if (this._client) {
      this._client.enableAudioVolumeEvaluation(interval);
    }
    return Promise.resolve();
  }

  /**
   * 暂停渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  @logMethod
  public pauseVideoRender(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 恢复渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  @logMethod
  public resumeVideoRender(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 重置视频渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  @logMethod
  public resetVideoRender(userId: string, type: TTrtcVideoStreamType) { }

  // *******************************************设备检测*******************************************
  /**
   * 开始摄像头设备测试
   * @param dom         用于渲染摄像头画面的DOM节点，不传入表示只是打开摄像头，但是不渲染
   */
  @logMethod
  public startCameraTest(dom?: HTMLElement): Promise<void> {
    this._cachedLocalStatus.video = this._localStatus.video;
    this._info('startCameraTest', `has testCameraStream ${!!this._testCameraStream}, _cameraTestStarted ${this._cameraTestStarted}`);
    const startTime = Date.now();
    const executor = this.newExecutor(TTrtcAction.StartCameraTest, true, 20000);
    if (executor.isConflicted()) {
      this._info('startCameraTest', `${executor.getIdentifier()} isConflicted`);
      return executor.getPromise();
    }
    this._stopCameraTestInner().then(async () => {
      this._info('startCameraTest', 'getCameras');
      const list = await this.getCameras();
      if (list?.length === 0) {
        this._error('startCameraTest', 'no camera device');
        this._processCameraResult(executor, false, { name: 'NotFoundError', message: 'no camera device' }, { startTime, isTest: true });
        executor.resolve();
        return;
      }
      this._info('startCameraTest', 'getCameraDeviceId');
      await this.getCameraDeviceId();
      this._info('startCameraTest', `getCameraDeviceId succ, ${this._cameraId} ${this._cameraLabel}`);
      this._testVideoDom = dom;
      this._client.startLocalVideo({
        view: dom,
        publish: false,
        option: {
          cameraId: this._cameraId,
          useFrontCamera: true,
          mirror: this._mirror,
          fillMode: 'cover',
        },
      }).then(() => {
        this._cameraTestStarted = true;
        executor.resolve();
      })
        .catch((err) => {
          this._error('startCameraTest', `error, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
          this._processCameraResult(executor, false, err, { startTime, isTest: true });
        });
    })
      .catch((err) => {
        this._error('startCameraTest', `error, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
        this._processCameraResult(executor, false, err, { startTime, isTest: true });
      });
    return executor.getPromise();
  }

  /**
   * 停止摄像头设备测试
   */
  @logMethod
  public stopCameraTest(): Promise<void> {
    const list = this.getExecutorList(TTrtcAction.StartCameraTest);
    this._info('stopCameraTest', `has testCameraExecutor ${list?.length > 0}, has testCameraStream ${!!this._testCameraStream}, _cameraTestStarted ${this._cameraTestStarted}`);
    return this._stopCameraTestInner().then(() => {
      if (this._cachedLocalStatus.video) {
        this.startLocalVideo(this._localVideoDom);
      } else {
        this.stopLocalVideo();
      }
      if (list?.length > 0) {
        this.rejectExecutor(TTrtcAction.StartCameraTest, new TCICError(-1, 'user stop'), true);
      }
    });
  }

  /**
   * 开始麦克风设备测试
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  @logMethod
  public startMicTest(dom?: HTMLElement): Promise<void> {
    this._cachedLocalStatus.audio = this._localStatus.audio;
    this._info('startMicTest', `has testMicStream ${!!this._testMicStream}, _micTestStarted ${this._micTestStarted}`);
    const startTime = Date.now();
    const executor = this.newExecutor(TTrtcAction.StartMicTest, true, 20000);
    if (executor.isConflicted()) {
      this._info('startMicTest', `${executor.getIdentifier()} isConflicted`);
      return executor.getPromise();
    }
    if (!dom) {
      this._info('startMicTest', 'no dom, reject');
      executor.reject(new TCICError(-5, i18next.t('Web端麦克风测试必须传入DOM节点')));
    } else {
      this._stopMicTestInner()
        .then(async () => {
          this._info('startMicTest', 'getMics');
          const list = await this.getMics();
          if (list?.length === 0) {
            this._error('startMicTest', 'no mic device');
            executor.resolve();
            this._processMicResult(executor, false, { name: 'NotFoundError', message: 'no mic device' }, { startTime, isTest: true });
            return;
          }

          // 确保有选中的麦克风
          this._info('startMicTest', 'getMicDeviceId');
          await this.getMicDeviceId();
          this._info('startMicTest', `getMicDeviceId succ, ${this._micId}`);
          this._client.startLocalAudio({
            publish: false,
            option: {
              microphoneId: this._micId,
            },
          }).then(async () => {
            this._micTestStarted = true;
            await this._processMicResult(executor, true, null, { startTime, isTest: true });
          })
            .catch(async (err) => {
              this._error('startMicTest', `error, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
              await this._processMicResult(executor, false, err, { startTime, isTest: true });
            });
        })
        .catch(async (err: any) => {
          this._error('startMicTest', `error, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);
          await this._processMicResult(executor, false, err, { startTime, isTest: true });
        });
    }
    return executor.getPromise();
  }

  /**
   * 停止麦克风设备测试
   */
  @logMethod
  public stopMicTest(): Promise<void> {
    const list = this.getExecutorList(TTrtcAction.StartMicTest);
    this._info('stopMicTest', `has testMicExecutor ${list?.length > 0}, has testMicStream ${!!this._testMicStream}, _micTestStarted ${this._micTestStarted}`);
    return this._stopMicTestInner().then(() => {
      if (this._cachedLocalStatus.audio) {
        this.startLocalAudio(this._highAudioQuality, this._localAudioDom);
      } else {
        this.stopLocalAudio();
      }
      if (list?.length > 0) {
        this.rejectExecutor(TTrtcAction.StartMicTest, new TCICError(-1, 'user stop'), true);
      }
    });
  }

  /**
   * 开启扬声器设备测试
   * @param path        要播放的声音文件路径
   */
  @logMethod
  public startSpeakerTest(path: string): Promise<void> {
    this._info('startSpeakerTest', `path ${path}`);
    const executor = this.newExecutor(TTrtcAction.StartSpeakerTest);
    this.stopSpeakerTest().then(() => {
      const testDom = document.createElement('audio');
      testDom.setAttribute('style', 'display: none;');
      testDom.src = path;
      testDom.loop = true;
      testDom.volume = this._speakerVolume / 100.0;
      if (this._speakerId) {
        // @ts-ignore
        if (typeof testDom.sinkId !== 'undefined') {
          // @ts-ignore
          testDom.setSinkId(this._speakerId);
        }
      }
      document.body.appendChild(testDom);
      testDom.play();
      this._testSpeakerDom = testDom;
      executor.resolve();
    });
    return executor.getPromise();
  }

  /**
   * 停止扬声器设备测试
   */
  @logMethod
  public stopSpeakerTest(): Promise<void> {
    this._info('stopSpeakerTest', '');
    if (this._testSpeakerDom) {
      this._testSpeakerDom.pause();
      document.body.removeChild(this._testSpeakerDom);
      this._testSpeakerDom = null;
    }
    return Promise.resolve();
  }

  // *******************************************设备管理*******************************************
  /**
   * 获取摄像头设备列表
   */
  @logMethod
  public getCameras(): Promise<TTrtcDeviceInfo[]> {
    return this._devicesInfoConvert(TRTC.getCameraList() as Promise<MediaDeviceInfo[]>);
  }

  /**
   * 切换使用的摄像头设备
   * @param deviceId      要切换的设备ID
   */
  @logMethod
  public async switchCamera(deviceId: string): Promise<void> {
    this._info('switchCamera', `deviceId ${deviceId}, _cameraId ${this._cameraId} ${this._cameraLabel}`);
    if (deviceId === this._cameraId) {
      return Promise.resolve();
    }

    const deviceLabel = await this._getDeviceLabelByDeviceId(deviceId);
    await this._applyCameraCompatibleVideoProfile(deviceLabel);
    const executor = this.newExecutor(TTrtcAction.SwitchCamera);
    const currentVideoTrack = this._client.getVideoTrack(); // get local video track
    if (!currentVideoTrack) {
      this._info('switchCamera', 'current video track is null start first');
      await this.stopLocalVideo();
      this._client.startLocalVideo({
        view: this._localVideoDom,
        option: {
          cameraId: deviceId,
          mirror: this._mirror,
        },
        publish: true,
      }).then(() => {
        this._info('switchCamera', `start local videp success. deviceId ${deviceId}`);
        setTimeout(() => {
          this._client.updateLocalVideo({
            option: {
              cameraId: deviceId,
            },
          }).then(() => {
            this._info('switchCamera', `update local videp success. deviceId ${deviceId}`);
            this._cameraId = deviceId;
            this._cameraLabel = deviceLabel;
            localStorage.setItem('cameraName', this._cameraLabel);
            TEvent.instance.notify(TTrtcEvent.Camera_Device_Changed, {
              deviceId,
              deviceType: TTrtcDeviceType.Camera,
              deviceLabel: this._cameraLabel,
            });
            executor.resolve();
          });
        }, 500);
      })
        .catch(async (err) => {
          this._info('switchCamera', `switch failed. deviceId ${deviceId}`);
          await this.stopLocalVideo();
          executor.reject(new TCICError(-4, i18next.t(`开启摄像头设备失败 ${err}`)));
        });
    } else {
      this._info('switchCamera', 'has trunck update direct');
      this._client.updateLocalVideo({
        option: {
          cameraId: deviceId,
        },
      }).then(() => {
        this._info('switchCamera', 'has trunck update direct. update success');
        this._cameraId = deviceId;
        this._cameraLabel = deviceLabel;
        localStorage.setItem('cameraName', this._cameraLabel);
        TEvent.instance.notify(TTrtcEvent.Camera_Device_Changed, {
          deviceId,
          deviceType: TTrtcDeviceType.Camera,
          deviceLabel: this._cameraLabel,
        });
        executor.resolve();
      })
        .catch(async (err) => {
          this._info('switchCamera', 'has trunck update failed');
          await this.stopLocalVideo();
          executor.reject(new TCICError(-4, i18next.t(`切换摄像头设备失败 ${err}`)));
        });
    }
    return executor.getPromise();
  }

  /**
   * 获取正在使用的摄像头设备ID
   */
  @logMethod
  public getCameraDeviceId(): Promise<string> {
    const executor = this.newExecutor(TTrtcAction.GetCameraDeviceId, false, 0);
    if (this.getExecutorList(TTrtcAction.GetCameraDeviceId).length > 1) {
      // 有多个调用
      return executor.getPromise(); // 直接返回，等待第一次调用结束触发resolve
    }
    // if (this._cameraId) {
    //   // 如果之前切换过设备
    //   this._info('getCameraDeviceId', `use current deviceId=${this._cameraId}`);
    //   executor.resolve(this._cameraId); // 返回前一次设置的ID
    // } else {

    // }
    // 始终以当前视频轨道中的设备ID为准
    // const videoTrack = this._client.getVideoTrack();
    // if (videoTrack) {
    //   // 如果本地设备已打开
    //   const settings = videoTrack.getSettings();
    //   const deviceId = settings.deviceId;
    //   this._info('getCameraDeviceId', `from stream deviceId=${deviceId}`);
    //   this._getDeviceLabelByDeviceId(deviceId)
    //     .then((deviceLabel) => {
    //       this._cameraId = deviceId;
    //       this._cameraLabel = deviceLabel;

    //       console.log(`分辨率：${settings.width} * ${settings.height}, 帧率：${settings.frameRate}`);
    //       this._info('getCameraDeviceId', `from stream deviceId=${deviceId},
    //         deviceLabel=${deviceLabel}, resolution=${settings.width}*${settings.height},
    //         fps=${settings.frameRate}`);
    //       executor.resolve(this._cameraId);
    //     });
    // } else {
    // }
    // try {
    //   const cid = localStorage.getItem('cameraId');
    //   if (cid) {
    //     this._info('getCameraDeviceId', `use localstroage cameraid`);
    //     this.resolveExecutor(TTrtcAction.GetCameraDeviceId, cid, true); // resolve所有Executor
    //     return;
    //   }
    // } catch (err) {
    //   this._error('getCameraDeviceId', `try to get cameraId from local stroage failed`);
    // }
    if (this._cameraId) {
      this._info('getCameraDeviceId', 'has cameraId. use cache.');
      this.resolveExecutor(TTrtcAction.GetCameraDeviceId, this._cameraId, true); // resolve所有Executor
      return executor.getPromise();
    }
    let enumerateSucc = false;
    navigator.mediaDevices
      .enumerateDevices()
      .then((devices) => {
        enumerateSucc = true;
        this._info('getCameraDeviceId', `enumerateDevices ${JSON.stringify(devices)}`);
        const device = devices.find(device => device.kind === 'videoinput');
        const cName = localStorage.getItem('cameraName');
        if (!device) {
          return Promise.reject({ name: 'NotFoundError', message: 'no camera device' });
        }
        this._info('getCameraDeviceId', `cache name: ${cName}`);
        const cacheCamera = devices.find(dev => dev.kind === 'videoinput' && dev.label == cName);
        if (cacheCamera) {
          this._info('getCameraDeviceId', `use localstroage ${cacheCamera.deviceId}`);
          this._cameraId = cacheCamera.deviceId;
          this._cameraLabel = cacheCamera.label;
          localStorage.setItem('cameraName', this._cameraLabel);
          this.resolveExecutor(TTrtcAction.GetCameraDeviceId, this._cameraId, true); // resolve所有Executor
          return;
        }
        const defaultDevice = devices.find(dev => (dev.deviceId == 'default' || dev.deviceId == '') && dev.kind === 'videoinput'); // 微信浏览器上deviceId为空也是默认设备
        if (!defaultDevice) {
          this._info('getCameraDeviceId', `use frist enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
          this._cameraId = device.deviceId;
          this._cameraLabel = device.label;
          localStorage.setItem('cameraName', this._cameraLabel);
        } else {
          const groupId = defaultDevice.groupId;
          const sameDefaultDevice = devices.find(dev => (dev.deviceId != 'default' && dev.deviceId != '') && dev.groupId == groupId);
          if (sameDefaultDevice) {
            this._info('getCameraDeviceId', `use default enumerateDevices succ deviceId=${sameDefaultDevice.deviceId}, deviceLabel=${sameDefaultDevice.label}`);
            this._cameraId = sameDefaultDevice.deviceId;
            this._cameraLabel = sameDefaultDevice.label;
            localStorage.setItem('cameraName', this._cameraLabel);
          } else {
            this._info('getCameraDeviceId', `use frist enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
            this._cameraId = device.deviceId;
            this._cameraLabel = device.label;
            localStorage.setItem('cameraName', this._cameraLabel);
          }
        }
        this.resolveExecutor(TTrtcAction.GetCameraDeviceId, this._cameraId, true); // resolve所有Executor
      })
      .catch((err: any) => {
        console.error('getUserMedia video error', err.name, err);
        if (enumerateSucc) {
          this._error('getCameraDeviceId', `enumerateDevices succ but still error, ${err.name}, ${err.message}`);
        } else {
          this._error('getCameraDeviceId', `enumerateDevices error, ${err.name}, ${err.message}`);
        }
        const { errMsg } = this.streamErrorNameHandler(err.name, 'video', err);
        this.rejectExecutor(
          TTrtcAction.GetCameraDeviceId,
          new TCICError(
            -4,
            errMsg || i18next.t('获取摄像头设备失败，无法开启设备'),
            `${err.name}|${err.code}|${err.message}`,
          ),
          true,
        ); // reject所有Executor
      });
    return executor.getPromise();
  }

  /**
   * 获取麦克风设备列表
   */
  @logMethod
  public getMics(): Promise<TTrtcDeviceInfo[]> {
    return this._devicesInfoConvert(TRTC.getMicrophoneList() as Promise<MediaDeviceInfo[]>);
  }

  /**
   * 切换使用的麦克风设备
   * @param deviceId      要切换的设备ID
   */
  @logMethod
  public switchMic(deviceId: string): Promise<void> {
    this._info('switchMic', `deviceId ${deviceId}, _micId ${this._micId}`);
    if (deviceId === this._micId) {
      return Promise.resolve();
    }
    const promiseArr = [];
    // if (this._localAudioStream) {
    //   this._info('switchMic', 'localAudioStream.switchDevice');
    //   const localPromise = this._localAudioStream.switchDevice('audio', deviceId)
    //     .then(() => {
    //       this._destroyAIDenoiserProcessor(this._localAudioStream);
    //       return this._aiDenoiserRender(this._localAudioStream, 'localAudioStream');
    //     });
    //   promiseArr.push(localPromise);
    // }
    // if (this._testMicStream) {
    //   this._info('switchMic', 'testMicStream.switchDevice');
    //   const testPromise = this._testMicStream.switchDevice('audio', deviceId);
    //   promiseArr.push(testPromise);
    // }

    // if (this._micAudioStream) {
    //   this._info('switchMic', 'micAudioStream.switchDevice');
    //   const micPromise = this._micAudioStream.switchDevice('audio', deviceId)
    //     .then(() => {
    //       this._destroyAIDenoiserProcessor(this._micAudioStream);
    //       this._info
    // ('switchMic', `_aiDenoiserRender, micAudioStream, publishStatus.audio ${this._publishStatus.audio}`);
    //       setTimeout(() => {
    //         /**
    //          * 修复切换麦克风后，本地视频流无法播放的问题
    //          */
    //         this._localVideoStream?.play(`video-${this._userId}`);
    //       }, 20);
    //       return this._aiDenoiserRender(this._micAudioStream, 'micAudioStream', {
    //         forceCreate: true, // 混流后再开启，process时会出错，这里不管开没开启都提前create，没开启就disable
    //       });
    //     });
    //   promiseArr.push(micPromise);
    // }
    const isStartLocalAudio = !!this._client.getAudioTrack();
    if (isStartLocalAudio) {
      const promise = this._client.updateLocalAudio({
        option: {
          microphoneId: deviceId,
        },
      });
      promiseArr.push(promise);
    }
    const executor = this.newExecutor(TTrtcAction.SwitchMic);
    Promise.all(promiseArr)
      .then(() => {
        this._info('switchMic', `all streams switch success, deviceId ${deviceId}`);
        this._micId = deviceId;
        this._micLabal = this._devices.find(dvc => dvc.deviceId === deviceId
          && dvc.type == TTrtcDeviceType.Mic).deviceName;
        // 【TODO】
        // 切换麦克风后，需要
        // 1. 重新创建混合流，原混合流被trtc内部替换
        // 2. 重新设置音量增益，包括系统和麦克风音量
        // 创建混合音频流
        // const mixAudioStream = this._audioContext.createMediaStreamDestination();
        // // 如果开启了系统混音
        // if (this._publishStatus.screenAudio) {
        //   if (this._client.getAudioTrack()) {
        //     const sysSource = this._audioContext.createMediaStreamSource(this._publishStream.getMediaStream());
        //     this._sysAudioGain = this._audioContext.createGain();
        //     this._sysAudioGain.gain.value = 1.0;
        //     sysSource.connect(this._sysAudioGain).connect(mixAudioStream);
        //   }
        // }
        // // 判断是否有音频流
        // if (this._client.getAudioTrack()) {
        //   const micSource = this._audioContext.createMediaStreamSource(this._micAudioStream.getMediaStream());
        //   this._micAudioGain = this._audioContext.createGain();
        //   this._micAudioGain.gain.value = this._micVolume / 100.0;
        //   micSource.connect(this._micAudioGain).connect(mixAudioStream);
        //   // 添加混合音频流到发布流
        //   if (!this._publishStream.getAudioTrack()) {
        //     // 按当前逻辑理论上不会走到这个分支
        //     this._publishStream.addTrack(mixAudioStream.stream.getAudioTracks()[0]);
        //   } else {
        //     this._oldAudioTrack = this._publishStream.getAudioTrack(); // 保存发布流原有音轨，替换成混音音轨
        //     this._publishStream.replaceTrack(mixAudioStream.stream.getAudioTracks()[0]);
        //   }
        // }
        executor.resolve();
        TEvent.instance.notify(TTrtcEvent.Mic_Device_Changed, {
          deviceId,
          deviceType: TTrtcDeviceType.Mic,
          deviceLabel: this._micLabal,
        });
      })
      .catch(async (err: any) => {
        await this.stopLocalAudio();
        executor.reject(new TCICError(-4, i18next.t('切换麦克风设备失败')));
      });
    return executor.getPromise();
  }

  /**
   * 获取正在使用的麦克风设备ID
   */
  @logMethod
  public getMicDeviceId(): Promise<string> {
    const executor = this.newExecutor(TTrtcAction.GetMicDeviceId, false, 0);
    if (this.getExecutorList(TTrtcAction.GetMicDeviceId).length > 1) {
      // 有多个调用
      return executor.getPromise(); // 直接返回，等待第一次调用结束触发resolve
    }
    // if (this._micId) {
    //   // 如果之前切换过设备
    //   this._info('getMicDeviceId', `use current deviceId=${this._micId}`);
    //   executor.resolve(this._micId); // 返回前一次设置的ID
    // } else {

    // }
    // const track = this._client.getAudioTrack();
    // const deviceID = track?.getSettings().deviceId;
    // if (deviceID) {
    //   this._micId = deviceID;
    //   this._info('getMicDeviceId', `from stream deviceId=${this._micId}`);
    //   executor.resolve(this._micId);
    // } else {
    let enumerateSucc = false;
    navigator.mediaDevices
      .enumerateDevices()
      .then((devices) => {
        enumerateSucc = true;
        this._info('getMicDeviceId', `enumerateDevices ${JSON.stringify(devices)}`);
        // 排除掉 id 为 communications 的设备，see https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-02-info-webrtc-issues.html#h2-2
        devices = devices.filter(dev => dev.kind == 'audioinput');
        const device = devices.find(device => device.kind === 'audioinput' && device.deviceId !== 'communications');
        const cacheMicName = this._micLabal;
        // 找到id为default的设备
        if (!device) {
          return Promise.reject({ name: 'NotFoundError', message: 'no mic device' });
        }
        this._info('getMicDeviceId', `cache name: ${cacheMicName}`);
        const cacheMic = devices.find(dev => dev.kind === 'audioinput' && dev.label === cacheMicName);
        if (cacheMic) {
          this._info('getMicDeviceId', `use localstroage ${cacheMic.deviceId}`);
          this._micId = cacheMic.deviceId;
          this._micLabal = cacheMic.label;
          this.resolveExecutor(TTrtcAction.GetMicDeviceId, this._micId, true); // resolve所有Executor
          return;
        }
        const defaultDevice = devices.find(dev => (dev.deviceId == 'default' || dev.deviceId == '')); // 微信浏览器上deviceId为空也是默认设备
        if (!defaultDevice) {
          this._info('getMicDeviceId', `use frist enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
          this._micId = device.deviceId;
          this._micLabal = device.label;
        } else {
          const groupId = defaultDevice.groupId;
          const sameDefaultDevice = devices.find(dev => (dev.deviceId != 'default' && dev.deviceId != '') && dev.groupId == groupId);
          if (sameDefaultDevice) {
            this._info('getMicDeviceId', `use default enumerateDevices succ deviceId=${sameDefaultDevice.deviceId}, deviceLabel=${sameDefaultDevice.label}`);
            this._micId = sameDefaultDevice.deviceId;
            this._micLabal = sameDefaultDevice.label;
          } else {
            this._info('getMicDeviceId', `use frist enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
            this._micId = device.deviceId;
            this._micLabal = device.label;
          }
        }
        this.resolveExecutor(TTrtcAction.GetMicDeviceId, this._micId, true); // resolve所有Executor
      })
      .catch((err: any) => {
        console.error('getUserMedia audio error', err.name, err);
        if (enumerateSucc) {
          this._error('getMicDeviceId', `enumerateDevices succ but still error, ${err.name}, ${err.message}`);
        } else {
          this._error('getMicDeviceId', `enumerateDevices error, ${err.name}, ${err.message}`);
        }
        const { errMsg } = this.streamErrorNameHandler(err.name, 'audio', err);
        this.rejectExecutor(
          TTrtcAction.GetMicDeviceId,
          new TCICError(
            -4,
            errMsg || i18next.t('获取麦克风设备失败，无法开启设备'),
            `${err.name}|${err.code}|${err.message}`,
          ),
          true,
        ); // reject所有Executor
      });
    // }
    return executor.getPromise();
  }
  /**
   * 设置正在使用的麦克风设备音量
   * @param volume        要设置的音量大小
   */
  @logMethod
  public async setMicVolume(volume: number): Promise<void> {
    this._info('setMicVolume', `volume ${volume}`);
    const executor = this.newExecutor(TTrtcAction.SetMicVolume);
    const vol = Math.floor(volume);
    if (!isNaN(vol)) {
      this._micVolume = vol;
      if (this._micAudioGain) {
        this._micAudioGain.gain.value = this._micVolume / 100.0;
      }
      this._updatePublishState(TMainState.Audio_Publish);
      executor.resolve();
    } else {
      executor.reject(new TCICError(-1, i18next.t('传入非法参数')));
    }
    return executor.getPromise();
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  @logMethod
  public getMicVolume(): Promise<number> {
    // 直接返回本地记录的值
    return Promise.resolve(this._micVolume);
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  @logMethod
  public getRealMicVolume(): Promise<number> {
    // 直接返回本地记录的值
    return Promise.resolve(this._micVolume);
  }


  /**
   * 获取扬声器设备列表
   */
  @logMethod
  public async getSpeakers(times = 0): Promise<TTrtcDeviceInfo[]> {
    const value = await this._devicesInfoConvert(TRTC.getSpeakerList() as Promise<MediaDeviceInfo[]>);
    if (times >= 3) {
      return value;
    }
    if (value.length === 0) {
      times += 1;
      await new Promise(res => setTimeout(res, 500));
      return await this.getSpeakers(times);
    }
    return value;
  }

  /**
   * 切换使用的扬声器设备
   * @param deviceId      要切换的设备ID
   */
  @logMethod
  public async switchSpeaker(deviceId: string): Promise<void> {
    this._info('switchSpeaker', `deviceId ${deviceId}, _speakerId ${this._speakerId}`);
    if (deviceId === this._speakerId) {
      return Promise.resolve();
    }
    const executor = this.newExecutor(TTrtcAction.SwitchSpeaker);
    try {
      if (this._client) {
        // @ts-ignore
        this._client.setCurrentSpeaker(deviceId);
        if (this._testSpeakerDom) {
          // @ts-ignore
          if (typeof this._testSpeakerDom.sinkId === 'undefined') {
            throw new Error(i18next.t('当前浏览器不支持切换扬声器设备'));
          }
          // @ts-ignore
          await this._testSpeakerDom.setSinkId(deviceId);
        }
        this._speakerId = deviceId;
        this._speakerLabel = this._devices.find(dvc => dvc.deviceId == deviceId
          && dvc.type == TTrtcDeviceType.Speaker).deviceName;
        executor.resolve();
        TEvent.instance.notify(TTrtcEvent.Speaker_Device_Changed, {
          deviceId,
          deviceType: TTrtcDeviceType.Speaker,
          deviceLabel: this._speakerLabel,
        });
      } else {
        executor.resolve();
      }
    } catch (err) {
      executor.reject(new TCICError(-4, i18next.t('切换扬声器设备失败')));
    }
    return executor.getPromise();
  }

  /**
   * 获取正在使用的扬声器设备ID
   */
  @logMethod
  public getSpeakerDeviceId(): Promise<string> {
    // return Promise.resolve(this._speakerId);
    const executor = this.newExecutor(TTrtcAction.GetSpeakerDeviceId, false, 0);
    if (this.getExecutorList(TTrtcAction.GetSpeakerDeviceId).length > 1) {
      // 有多个调用
      return executor.getPromise(); // 直接返回，等待第一次调用结束触发resolve
    }
    // if (this._micId) {
    //   // 如果之前切换过设备
    //   this._info('getMicDeviceId', `use current deviceId=${this._micId}`);
    //   executor.resolve(this._micId); // 返回前一次设置的ID
    // } else {

    // }
    // 始终以当前音轨的设备ID为准
    // const track = this._client.getAudioTrack();
    // const deviceID = track?.getSettings().deviceId;
    // if (deviceID) {
    //   this._speakerId = deviceID;
    //   this._info('getSpeakerDeviceId', `from stream deviceId=${this._speakerId}`);
    //   executor.resolve(this._speakerId);
    // } else {
    let enumerateSucc = false;
    navigator.mediaDevices
      .enumerateDevices()
      .then((devices) => {
        enumerateSucc = true;
        this._info('getSpeakerDeviceId', `enumerateDevices ${JSON.stringify(devices)}`);
        // 排除掉 id 为 communications 的设备，see https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-02-info-webrtc-issues.html#h2-2
        devices = devices.filter(dev => dev.kind == 'audiooutput');
        const device = devices.find(device => device.kind === 'audiooutput' && device.deviceId !== 'communications');
        const cacheSpeakerName = this._speakerLabel;
        // 找到id为default的设备
        if (!device) {
          return Promise.reject({ name: 'NotFoundError', message: 'no mic device' });
        }
        this._info('getSpeakerDeviceId', `cache name: ${cacheSpeakerName}`);
        const cacheSpeaker = devices.find(dev => dev.kind === 'audiooutput' && dev.label === cacheSpeakerName);
        if (cacheSpeaker) {
          this._info('getSpeakerDeviceId', `use localstroage ${cacheSpeaker.deviceId}`);
          this._speakerId = cacheSpeaker.deviceId;
          this._speakerLabel = cacheSpeaker.label;
          this.resolveExecutor(TTrtcAction.GetSpeakerDeviceId, this._speakerId, true); // resolve所有Executor
          return;
        }
        const defaultDevice = devices.find(dev => (dev.deviceId == 'default' || dev.deviceId == ''));
        if (!defaultDevice) {
          this._info('getSpeakerDeviceId', `use first enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
          this._speakerId = device.deviceId;
          this._speakerLabel = device.label;
        } else {
          const groupId = defaultDevice.groupId;
          const sameDefaultDevice = devices.find(dev => (dev.deviceId != 'default' && dev.deviceId != '') && dev.groupId == groupId);
          const track = this._client.getAudioTrack();
          const deviceID = track?.getSettings().deviceId;
          console.log(deviceID);
          if (sameDefaultDevice) {
            this._info('getSpeakerDeviceId', `use default enumerateDevices succ deviceId=${sameDefaultDevice.deviceId}, deviceLabel=${sameDefaultDevice.label}`);
            this._speakerId = sameDefaultDevice.deviceId;
            this._speakerLabel = sameDefaultDevice.label;
          } else {
            this._info('getSpeakerDeviceId', `use first enumerateDevices succ deviceId=${device.deviceId}, deviceLabel=${device.label}`);
            this._speakerId = device.deviceId;
            this._speakerLabel = device.label;
          }
        }
        this.resolveExecutor(TTrtcAction.GetSpeakerDeviceId, this._speakerId, true); // resolve所有Executor
      })
      .catch((err: any) => {
        console.error('getUserMedia audio error', err.name, err);
        if (enumerateSucc) {
          this._error('GetSpeakerDeviceId', `enumerateDevices succ but still error, ${err.name}, ${err.message}`);
        } else {
          this._error('GetSpeakerDeviceId', `enumerateDevices error, ${err.name}, ${err.message}`);
        }
        const { errMsg } = this.streamErrorNameHandler(err.name, 'audio', err);
        this.rejectExecutor(
          TTrtcAction.GetMicDeviceId,
          new TCICError(
            -4,
            errMsg || i18next.t('获取扬声器设备失败，无法开启设备'),
            `${err.name}|${err.code}|${err.message}`,
          ),
          true,
        ); // reject所有Executor
      });
    // }
    return executor.getPromise();
  }

  /**
   * 设置正在使用的扬声器设备音量
   * @param volume        要设置的音量大小
   */
  @logMethod
  public setSpeakerVolume(volume: number): Promise<void> {
    this._info('setSpeakerVolume', `volume ${volume}`);
    this._speakerVolume = volume;
    const isAudioStarted = this._client.getAudioTrack();
    if (isAudioStarted) {
      this._client.updateLocalAudio({
        option: {
          earMonitorVolume: 0,
        },
      });
    }
    // 设置远端音频播放音量
    this._remoteUserList.forEach((userID) => {
      this._client.setRemoteAudioVolume(userID, volume);
    });
    // 设置本地扬声器测试音量
    if (this._testSpeakerDom) {
      this._testSpeakerDom.volume = volume / 100.0;
    }
    return Promise.resolve();
  }

  /**
   * 获取正在使用的扬声器设备音量
   */
  @logMethod
  public getSpeakerVolume(): Promise<number> {
    // 直接返回本地记录的值
    return Promise.resolve(this._speakerVolume);
  }

  // *******************************************屏幕分享*******************************************

  /**
   * 检查当前平台是否支持屏幕分享
   */
  @logMethod
  public async isScreenShareSupported(): Promise<boolean> {
    const checkResult = await TRTC.isSupported();
    return checkResult.detail.isScreenShareSupported;
  }

  /**
   * 检查屏幕分享权限
   */
  @logMethod
  public hasScreenCapturePermission(): Promise<boolean> {
    return Promise.resolve(true);
  }

  /**
   * 获取屏幕分享屏幕采集源列表
   */
  @logMethod
  public getScreenCaptureSources(): Promise<TScreenCaptureSourceInfo[]> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('网页端不支持')));
  }

  /**
   * 选择要进行屏幕分享的目标采集源
   * @param source        要分享的采集源
   * @param captureMouse        是否捕获鼠标
   * @param highlightWindow        是否高亮选择区域
   */
  @logMethod
  public selectScreenCaptureTarget(
    source: TScreenCaptureSourceInfo,
    captureMouse: boolean,
    highlightWindow: boolean,
  ): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('网页端不支持')));
  }

  /**
   * 进行屏幕分享的目标采集源
   */
  @logMethod
  public getScreenCaptureTarget(): TScreenCaptureSourceInfo {
    return null;
  }

  /**
   * 将指定窗口加入屏幕分享的排除列表中
   */
  @logMethod
  public addExcludedShareWindows(sourceIds: string[]): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('网页端不支持')));
  }

  /**
   * 将指定窗口加入屏幕分享的列表中
   */
  @logMethod
  public addIncludedShareWindows(sourceIds: string[]): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('网页端不支持')));
  }

  /**
   * 开始屏幕分享
   * @param dom  用于渲染分享画面的DOM节点，不需要渲染可以忽略
   */
  @logMethod
  public async startScreenShare(dom?: HTMLElement, opts?: { videoSource?: any }): Promise<void> {
    this._info('startScreenShare', `start, has dom ${!!dom}`);
    console.log('[ScreenComponent][SDK] into startScreenShare', dom, opts);
    const executor = this.newExecutor(TTrtcAction.StartScreenShare);
    // 普通模式不允许多人同时共享
    if (executor.isConflicted()) {
      // 冲突时直接返回
      this._info('startScreenShare', `${executor.getIdentifier()} isConflicted`);
      return executor.getPromise();
    }
    let publishSucc = false;
    this.stopScreenShare()
      .then(async () => {
        this._info('startScreenShare', '_createLocalStream');
        this._localScreenDom = dom;
        if (!opts) {
          opts = {};
        }
        console.log('[ScreenComponent][SDK]: will create', { screen: true, screenPaused: false, ...opts });
        await this._client.startScreenShare({
          view: dom,
          option: {
            systemAudio: true,
            profile: {
              width: 1920,
              height: 1080,
              frameRate: 15,
              bitrate: 4000,
            },
            // profile: {
            //   width: 1920,
            //   height: 1080,
            //   frameRate: 60,
            //   bitrate: 4000,
            // },
          },
        });
        publishSucc = true;

        // 添加事件通知
        TEvent.instance.notify(TTrtcEvent.SubStream_Changed, {
          userId: this._userId,
          available: true,
        });
        // 屏幕分享时，处理屏幕分享相关事件
        this._client.on(TRTC.EVENT.SCREEN_SHARE_STOPPED, () => {
          this._info('screen-sharing-stopped', '');
          this.stopScreenShare();
        });

        // 发布屏幕共享辅流并更新
        this._info('startScreenShare', '_updateScreenPublishStatus, { screen: true }');
        this._updateScreenPublishStatus({ screen: true });

        this._info('startScreenShare', '_updateScreenStream, { screen: true }');
        await this._updateScreenStream({ screen: true });

        this._info('startScreenShare', `success, _publishStatus ${JSON.stringify(this._publishStatus)}`);
        executor.resolve();
      })
      .catch(async (err: any) => {
        this._error('startScreenShare', `error, ${err.name}, ${err.getCode ? err.getCode() : (err.errorCode || err.code)}, ${err.toString()}`);

        if (publishSucc) {
          try {
            await this._updateScreenStream({ screen: false, screenPaused: false });
          } catch (stopErr) {
            console.warn('_updateScreenStream { screen: false, screenPaused: false } err', stopErr);
          }
        }

        let errCode = err.getCode ? err.getCode() : (err.errorCode || err.code || -1);
        let errMsg = i18next.t('打开屏幕共享遇到一些问题');
        if (err.name === 'NotAllowedError' || err.message?.indexOf('Permission denied') !== -1) {
          // 权限问题
          if (err.message.indexOf('Permission denied by system') !== -1) {
            // 系统权限问题
            errMsg = i18next.t('打开屏幕共享遇到一些问题，请检查系统权限设置');
          } else {
            // 用户取消
            errCode = 0;
          }
        }
        executor.reject(new TCICError(
          errCode,
          errMsg,
          err.toString(),
        ));
      });
    return executor.getPromise();
  }

  public setSubStreamEncoderParam(params: Partial<{
    videoResolution: TTrtcVideoResolution,
    resMode: 0 | 1,
    videoFps: number,
    videoBitrate: number,
    enableAdjustRes: boolean,
    screenCaptureMode: number,
  }>): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('网页端不支持')));
  }

  /**
   * 暂停屏幕分享
   */
  @logMethod
  public pauseScreenShare(): Promise<void> {
    this._info('pauseScreenShare');
    const executor = this.newExecutor(TTrtcAction.PauseScreenShare);
    if (this._publishStatus.screen) {
      if (!this._screenStatus.screenPaused) {
        this._updateScreenStream({ screenPaused: true })
          .then(() => {
            executor.resolve();
          })
          .catch((err: any) => {
            executor.reject(new TCICError(
              err.code,
              i18next.t('暂停屏幕共享遇到一些问题'),
              err.toString(),
            ));
          });
      } else {
        executor.resolve();
      }
    } else {
      executor.reject(new TCICError(-6, i18next.t('暂停屏幕共享失败，屏幕共享未启动')));
    }
    return executor.getPromise();
  }

  /**
   * 恢复屏幕分享
   */
  @logMethod
  public resumeScreenShare(): Promise<void> {
    this._info('resumeScreenShare');
    const executor = this.newExecutor(TTrtcAction.ResumeScreenShare);
    if (this._publishStatus.screen) {
      if (this._screenStatus.screenPaused) {
        this._updateScreenStream({ screenPaused: false })
          .then(() => {
            executor.resolve();
          })
          .catch((err: any) => {
            executor.reject(new TCICError(
              err.code,
              i18next.t('恢复屏幕共享遇到一些问题'),
              err.toString(),
            ));
          });
      } else {
        executor.resolve();
      }
    } else {
      executor.reject(new TCICError(-6, i18next.t('恢复屏幕共享失败，屏幕共享未启动')));
    }
    return executor.getPromise();
  }

  /**
   * 停止屏幕分享
   */
  @logMethod
  public stopScreenShare(): Promise<void> {
    this._info('stopScreenShare', `_publishStatus.screen ${this._publishStatus.screen}`);
    const executor = this.newExecutor(TTrtcAction.StopScreenShare);
    if (this._publishStatus.screen) {
      this._updateScreenStream({ screen: false, screenPaused: false })
        .then(() => {
          executor.resolve();
        })
        .catch((err: any) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('结束屏幕共享遇到一些问题'),
            err.toString(),
          ));
        });
    } else {
      executor.resolve(); // 屏幕共享未启动，忽略
    }
    return executor.getPromise();
  }

  /**
   *
   * 获取屏幕共享流
   */
  public getScreenShareStream() {
    return this._client.getVideoTrack({
      streamType: TRTC.TYPE.STREAM_TYPE_SUB,
    });
  }

  /**
   * 控制是否分享系统声音
   * @param enable        是否分享
   */
  @logMethod
  public enableSystemAudioLoopback(enable: boolean): Promise<void> {
    this._info('enableSystemAudioLoopback', `enable ${enable}`);
    const executor = this.newExecutor(TTrtcAction.EnableSystemAudioLoopback);
    if (this._screenStatus.screenAudio === enable) {
      // 没有变化
      executor.resolve();
    } else {
      console.log(`----render enableSystemAudioLoopback ${new Date().getTime()}`, this._publishStatus);
      this._client.stopScreenShare().then(() => {
        this._client.startScreenShare({
          view: this._localScreenDom,
          option: {
            systemAudio: enable,
            profile: '1080p',
          },
        });
      })
        .then(() => {
          this._screenStatus.screenAudio = enable;
          executor.resolve();
        })
        .catch((err: any) => {
          executor.reject(new TCICError(
            err.code,
            i18next.t('控制是否分享系统声音遇到一些问题'),
            err.toString(),
          ));
        });
    }
    return executor.getPromise();
  }

  /**
   * 播放背景音乐
   */
  @logMethod
  public startMusic(documentId: string, url: string): Promise<boolean> {
    /**
     * web端会用白板适配音频播放，保证调用不报错
     */
    return new Promise((resolve) => {
      resolve(true);
    });
    //  Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 停止背景音乐
   */
  @logMethod
  public stopMusic(): void {
  }

  /**
   * 暂停背景音乐
   */
  @logMethod
  public pauseMusic(): void { }

  /**
   * 恢复背景音乐
   */
  @logMethod
  public resumeMusic(): void { }

  /**
   * 获取音乐时长，单位毫秒
   */
  @logMethod
  public getMusicDuration(): number {
    return 0;
  }

  /**
   * 设置背景音乐进度
   */
  @logMethod
  public seekMusic(pts: number): void { }

  @logMethod
  public startCaptureStream(elementId: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 停止自定义音视频采集
   */
  @logMethod
  public stopCaptureStream(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }
  @logMethod
  public setMusicVolume(volume: number): void { }

  /**
   * 加载视频
   */
  @logMethod
  public loadVideo(dom: HTMLElement, url: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 开始播放视频
   */
  @logMethod
  public playVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 暂停播放视频
   */
  @logMethod
  public pauseVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 视频进度跳转
   */
  @logMethod
  public seekVideo(time: number): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 结束播放视频
   */
  @logMethod
  public stopVideo(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 设置音量大小
   */
  @logMethod
  public setVideoVolume(volume: number): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 打开辅助摄像头
   */
  @logMethod
  public startSubCamera(
    dom: HTMLElement,
    deviceIndex: number,
    resolution: TTrtcVideoResolution,
  ): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  /**
   * 关闭辅助摄像头
   */
  @logMethod
  public stopSubCamera(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('Web端不支持')));
  }

  public setNetworkQosParam(preference: TRTCVideoQosPreference): Promise<void> {
    this._qosPreference = preference === TRTCVideoQosPreference.TRTC_VIDEO_QOS_PREFERENCE_SMOOTH
      ? TRTC.TYPE.QOS_PREFERENCE_SMOOTH : TRTC.TYPE.QOS_PREFERENCE_CLEAR;
    return Promise.resolve();
  }

  // *******************************************美颜相关*******************************************

  /**
   * 设置虚拟形象
   * @param effectId  特效id
   * @param url string 背景图链接地址
   */
  @logMethod
  public async setAvatar(effectId: string, url?: string): Promise<void> {
    this._info('setAvatar', ` effectId: ${effectId}, ${new Date()}`);
    this._avatarEffectId = effectId;
    if (!this._publishStatus.video) {
      return Promise.resolve();
    }

    const plugin = this._getBeautyPlugin();
    const newStream = await plugin.setAvatar(effectId, url);
    if (newStream) {
      requestAnimationFrame(() => {
        this._client.updateLocalVideo({
          option: {
            videoTrack: newStream.getVideoTracks()[0],
          },
        });
      });
    }
  }

  /**
   * 设置虚拟背景
   * @param enable  是否是图片
   * @param url string 背景图链接地址
   */
  @logMethod
  public async setVirtualImg(enable: boolean, url: string, sceneKey?: string): Promise<void> {
    this._info('setVirtualImg', `flag: ${enable}, url: ${url}, ${new Date()}, _enableWebARPlugin: ${this._enableWebARPlugin}`);
    // 使用webar sdk
    if (this._enableWebARPlugin) {
      this._virtualUrl = url;
      this._enableVirtualBackground = !!enable;
      let type = 'image';
      if (this._enableVirtualBackground && !url) { // 如果启用虚拟背景但url没有则默认虚化
        type = 'blur';
      }
      if (!this._publishStatus.video) {
        return Promise.resolve();
      }

      const plugin = this._getBeautyPlugin();
      const newStream = await plugin.setBackground(type, url);
      if (newStream) {
        requestAnimationFrame(() => {
          this._client.updateLocalVideo({
            option: {
              videoTrack: newStream.getVideoTracks()[0],
            },
          });
        });
      }
    }
    return Promise.resolve();
  }
  /**
   * 设置美颜旧--废弃！！
   * @param beauty  美颜度( 0 - 10，推荐为 5 )
   * @param brightness 	明亮度( 0 - 10，推荐为 5 )
   * @param ruddy 红润度( 0 - 10，推荐为 5 )
   */

  /**
   * 设置美颜新-使用webar sdk
   * @param whiten  美白( 0 - 100，推荐为 20 )
   * @param lift 	窄脸( 0 - 100，推荐为 20 )
   * @param eye 大眼( 0 - 100，推荐为 20 )
   */
  @logMethod
  public async setBeautyParam(
    whiten: number,
    lift: number,
    eye: number,
  ): Promise<void> {
    if (
      this._enableVirtualBackground
      && whiten === 0
      && lift === 0
      && eye === 0
    ) {
      whiten = 1; // 开启虚拟背景时，美颜不可关闭(避免虚拟背景失效)
    }
    whiten = whiten / 100 || 0;
    lift = lift / 100 || 0;
    eye = eye / 100 || 0;
    this._info(
      'setBeautyParam',
      `webar whiten: ${whiten}, lift: ${lift}, eye: ${eye}`,
    );

    if (!this._beautyCfg && whiten === 0
      && lift === 0 && eye === 0) {
      // 如果全是0则不用动了
      return Promise.resolve();
    }
    this._beautyCfg = {
      whiten,
      lift,
      eye,
    };
    const isVideoOpen = this._publishStatus.video;
    // 如果视频没有打开，就不走美颜插件，保留参数即可。
    if (!isVideoOpen) {
      return Promise.resolve();
    }

    const plugin = this._getBeautyPlugin();
    const newStream = await plugin.setBeautify(this._beautyCfg);
    if (newStream) {
      requestAnimationFrame(() => {
        this._client.updateLocalVideo({
          option: {
            videoTrack: newStream.getVideoTracks()[0],
          },
        });
      });
    }
    return Promise.resolve();
  }

  // *******************************************降噪相关*******************************************
  /**
   * 检查当前平台是否支持AI降噪
   */
  @logMethod
  public isAIDenoiseSupported(): boolean {
    return true;
    // return this._getAIDenoiser().isSupported();
  }

  /**
   * 开启AI降噪
   * @param enable    是否开启
   */
  @logMethod
  public async enableAIDenoise(enable: boolean): Promise<void> {
    this._info('enableAIDenoise', `enable: ${enable}`);
    this._enableAIDenoiser = enable;
    if (enable) {
      this.startAiDenoiser();
    } else {
      this.stopAiDenoiser();
    }
  }
  /**
   * 获取类型
   * @protected
   */
  protected _getClassName() {
    return 'TTrtcWeb';
  }

  @logMethod
  private _getRemoteStreamHolder(userId: string, screen: boolean) {
    const key = `${userId}_${screen ? 'screen' : 'video'}`;
    if (!this._remoteStreamsHolder.has(key)) {
      this._remoteStreamsHolder.set(key, new TRemoteStreamHolder());
    }
    return this._remoteStreamsHolder.get(key);
  }

  @logMethod
  private _getHolder(uesrId: string, screen: boolean) {
    return this._getRemoteStreamHolder(
      uesrId,
      screen,
    );
  }
  // 设备类型识别
  @logMethod
  private _devicesInfoConvert(promise: Promise<MediaDeviceInfo[]>): Promise<TTrtcDeviceInfo[]> {
    // return promise.then(devices => devices.map(device => convertMediaDeviceInfo(device)));
    return promise.then((devices) => {
      const devs = devices.filter(dev => dev.deviceId != 'default');
      return devs.map(device => convertMediaDeviceInfo(device));
    });
  }
  // 切换视频流采集设备
  @logMethod
  private _deviceChangeHandler(event: Event) {
    this._devicesInfoConvert(this._getDevices() as Promise<MediaDeviceInfo[]>).then(async (devices) => {
      if (JSON.stringify(devices) === JSON.stringify(this._devices)) {
        // 没变化
        console.log('deviceChange', 'no change');
        return;
      }
      this._info(
        'deviceChange',
        `old: ${JSON.stringify(this._devices)}\nnew: ${JSON.stringify(devices)}`,
      );
      const oldDevices = this._devices;
      this._devices = devices;
      this._info('deviceChange', `current device is mic:${this._micLabal}_${this._micId}. camera:${this._cameraId}_${this._cameraLabel}. speaker:${this._speakerId}_${this._speakerLabel}`);
      const notifyDevices: any[] = [];
      let cameraDeviceChanged = false;
      let micDeviceChanged = false;
      // 设备移除
      oldDevices.forEach((oldDevice) => {
        // 原有设备在新的列表中找不到，就是删除的设备
        if (!devices.find(device => device.deviceId === oldDevice.deviceId)) {
          if (oldDevice.type === TTrtcDeviceType.Camera) {
            if (oldDevice.deviceId && oldDevice.deviceId === this._cameraId) {
              this._info('deviceChange', `remove current camera ${JSON.stringify(oldDevice)}, _cameraId ${this._cameraId} ${this._cameraLabel}`);
            } else {
              this._info('deviceChange', `remove camera ${JSON.stringify(oldDevice)}, not current _cameraId ${this._cameraId} ${this._cameraLabel}`);
            }
            cameraDeviceChanged = true;
            this._cameraId = null;
            this._cameraLabel = null;
          } else if (oldDevice.type === TTrtcDeviceType.Mic) {
            if (oldDevice.deviceId && oldDevice.deviceId === this._micId) {
              this._info('deviceChange', `remove current mic ${JSON.stringify(oldDevice)}, _micId ${this._micId}`);
            } else {
              this._info('deviceChange', `remove mic ${JSON.stringify(oldDevice)}, not current _micId ${this._micId}`);
            }
            micDeviceChanged = true;
            this._micId = null;
            this._micLabal = null;
          } else if (oldDevice.type === TTrtcDeviceType.Speaker) {
            this._speakerId = null;
            this._speakerLabel = null;
          } else {
            console.log('deviceChange', 'remove other device', oldDevice);
          }
          notifyDevices.push({
            deviceId: oldDevice.deviceId,
            type: oldDevice.type,
            state: TTrtcDeviceState.Remove,
            name: oldDevice.deviceName,
          });
        }
      });
      // 设备增加
      devices.forEach((newDevice) => {
        // 新的设备在原有列表中找不到，就是新增的设备
        if (
          !oldDevices.find(device => device.deviceId === newDevice.deviceId)
        ) {
          if (newDevice.type === TTrtcDeviceType.Camera) {
            this._info('deviceChange', `add camera ${JSON.stringify(newDevice)}, _cameraId ${this._cameraId} ${this._cameraLabel}`);
            cameraDeviceChanged = true;
          } else if (newDevice.type === TTrtcDeviceType.Mic) {
            this._info('deviceChange', `add mic ${JSON.stringify(newDevice)}, _micId ${this._micId}`);
            micDeviceChanged = true;
          } else if (newDevice.type === TTrtcDeviceType.Speaker) {
            this._speakerId = newDevice.deviceId;
            this._speakerLabel = newDevice.deviceName;
          } else {
            console.log('deviceChange', 'add other device', newDevice);
          }
          notifyDevices.push({
            deviceId: newDevice.deviceId,
            type: newDevice.type,
            state: TTrtcDeviceState.Add,
            name: newDevice.deviceName,
          });
        }
      });

      // 更新是否notfound
      this._updateDeviceStatusByDeviceList('deviceChange');

      // 设置默认设备
      // const changedCamera = await this._setDefaultCameraDevice(devices);
      // const changedMic = await this._setDefaultMicDevice(devices);
      // const changedSpeaker = await this._setDefaultSpeakerDevice(devices);
      // if (changedCamera) {
      //   notifyDevices.push({
      //     deviceId: changedCamera.deviceId,
      //     type: changedCamera.type,
      //     state: TTrtcDeviceState.Active,
      //     name: changedCamera.deviceName,
      //   });
      // };
      // if (changedMic) {
      //   notifyDevices.push({
      //     deviceId: changedMic.deviceId,
      //     type: changedMic.type,
      //     state: TTrtcDeviceState.Active,
      //     name: changedMic.deviceName,
      //   });
      // };
      // if (changedSpeaker) {
      //   notifyDevices.push({
      //     deviceId: changedSpeaker.deviceId,
      //     type: changedSpeaker.type,
      //     state: TTrtcDeviceState.Active,
      //     name: changedSpeaker.deviceName,
      //   });
      // };
      this._info('deviceChange', `notifyDevices is ${JSON.stringify(notifyDevices)}`);
      // 通知设备变化
      if (notifyDevices.length) {
        notifyDevices.forEach((device: any) => {
          TEvent.instance.notify(TTrtcEvent.Device_Changed, device);
        });
      }
    });
  }
  @logMethod
  private getDefaulDevice(devices: TTrtcDeviceInfo[], type: TTrtcDeviceType): DeviceChangedInfo {
    const typeDevice = devices.filter(dvc => dvc.type === type);
    const defaultDevice = typeDevice.find(dvc => dvc.deviceId.toLocaleLowerCase() === 'default');
    let resDevice = null;
    if (defaultDevice) {
      this._info('deviceChange', `use default device ${JSON.stringify(defaultDevice)}`);
      resDevice = defaultDevice;
    }
    if (typeDevice.length) {
      this._info('deviceChange', `use first device ${JSON.stringify(typeDevice[0])}`);
      resDevice = typeDevice[0];
    }
    let changed = false;
    if (resDevice) {
      let usedDeviceId = null;
      let usedDeviceLable = null;
      if (type == TTrtcDeviceType.Camera) {
        usedDeviceId = this._cameraId;
        usedDeviceLable = this._cameraLabel;
      } else if (type == TTrtcDeviceType.Mic) {
        usedDeviceId = this._micId;
        usedDeviceLable = this._micLabal;
      } else if (type == TTrtcDeviceType.Speaker) {
        usedDeviceId = this._speakerId;
        usedDeviceLable = this._speakerLabel;
      }
      if (resDevice.deviceId != usedDeviceId || resDevice.deviceName != usedDeviceLable) {
        changed = true;
      }
    }
    return {
      device: resDevice,
      changed,
    };
  }
  @logMethod
  private async _setDefaultSpeakerDevice(devices: TTrtcDeviceInfo[]): Promise<TTrtcDeviceInfo> {
    const { device: defaultSpeaker, changed } = this.getDefaulDevice(devices, TTrtcDeviceType.Speaker);
    if (defaultSpeaker) {
      this._info('deviceChange', `set default speaker start: ${JSON.stringify(defaultSpeaker)}`);
      try {
        await this.switchSpeaker(defaultSpeaker.deviceId);
        this._info('deviceChange', `set default speaker success, now _speakerId: ${defaultSpeaker.deviceId}`);
      } catch (err) {
        this._info('deviceChange', `set default speaker end, now _speakerId: ${defaultSpeaker.deviceId}`);
      }
      if (changed) {
        return defaultSpeaker;
      }
    } else {
      const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
      const isAbnormal = isDeviceAbnormal(deviceStatus);
      const newDeviceStatus = isAbnormal ? deviceStatus : TDeviceStatus.Not_Found;
      this._info('deviceChange', `no mic device, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}, newDeviceStatus ${newDeviceStatus}`);
      this._setState(TMainState.Audio_Device_Status, newDeviceStatus);
      await this.stopLocalAudio();
    }
  }
  @logMethod
  private async _setDefaultMicDevice(devices: TTrtcDeviceInfo[]): Promise<TTrtcDeviceInfo> {
    const { device: defaultMic, changed } = this.getDefaulDevice(devices, TTrtcDeviceType.Mic);
    if (defaultMic) {
      this._info('deviceChange', `set default mic start: ${JSON.stringify(defaultMic)}`);
      try {
        await this.switchMic(defaultMic.deviceId);
        this._info('deviceChange', `set default mic success, now _micId: ${defaultMic.deviceId}`);
      } catch (err) {
        this._info('deviceChange', `set default mic end, now _micId: ${defaultMic.deviceId}`);
      }
      if (changed) {
        return defaultMic;
      }
    } else {
      const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
      const isAbnormal = isDeviceAbnormal(deviceStatus);
      const newDeviceStatus = isAbnormal ? deviceStatus : TDeviceStatus.Not_Found;
      this._info('deviceChange', `no mic device, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}, newDeviceStatus ${newDeviceStatus}`);
      this._setState(TMainState.Audio_Device_Status, newDeviceStatus);
      await this.stopLocalAudio();
    }
  }
  @logMethod
  private async _setDefaultCameraDevice(devices: TTrtcDeviceInfo[]): Promise<TTrtcDeviceInfo> {
    const { device: defaultCamera, changed } = this.getDefaulDevice(devices, TTrtcDeviceType.Camera);
    if (defaultCamera) {
      this._info('deviceChange', `set default camera start: ${JSON.stringify(defaultCamera)}`);
      try {
        await this.switchCamera(defaultCamera.deviceId);
        this._info('deviceChange', `set default camera success, now _cameraId: ${defaultCamera.deviceId} ${defaultCamera.deviceName}`);
      } catch (err) {
        this._info('deviceChange', `set default camera error, now _cameraId: ${defaultCamera.deviceId} ${defaultCamera.deviceName}`);
      }
      if (changed) {
        return defaultCamera;
      }
    } else {
      const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
      const isAbnormal = isDeviceAbnormal(deviceStatus);
      const newDeviceStatus = isAbnormal ? deviceStatus : TDeviceStatus.Not_Found;
      this._info('deviceChange', `no camera device, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}, newDeviceStatus ${newDeviceStatus}`);
      this._setState(TMainState.Video_Device_Status, newDeviceStatus);
      await this.stopLocalVideo();
    }
  }
  // 更新屏幕分享流发布状态
  @logMethod
  private _updateScreenPublishStatus(publishStatus: TStreamStatus) {
    const screenPublishState = publishStatus.screen;
    let screenStatus = 2;
    this._publishStatus = Object.assign({}, publishStatus); // 保存新的状态
    if (publishStatus.screen) {
      screenStatus = 0;
      if (publishStatus.screenPaused) {
        // 开启了屏幕分享，但是处于暂停状态
        screenStatus = 1;
      }
    } else if (this._publishStatus.screen) {
      // 屏幕分享关闭事件
      setTimeout(() => {
        // 异步通知
        TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
      }, 10);
    }
    // 屏幕分享状态更新
    this._setState(TMainState.Screen_Share, screenStatus);
    // 屏幕分享流推送
    this._setTrtcState(TTrtcWeb.ScreenPublishState, screenPublishState);
  }
  // 更新本地流发布状态
  @logMethod
  private _updatePublishStatus(publishStatus: TStreamStatus) {
    const publishState = publishStatus.audio || publishStatus.video;
    this._info(
      'actualStatusUpdated',
      `${JSON.stringify(this._publishStatus)} -> ${JSON.stringify(publishStatus)}`,
    );
    this._publishStatus = { ...this._publishStatus, ...publishStatus }; // 保存新的状态
    this._updatePublishState(TMainState.Audio_Publish);
    this._updatePublishState(TMainState.Video_Publish);
    this._setTrtcState(TTrtcWeb.PublishState, publishState);
  }

  private updatePublishStatus(muteStatus: TStreamStatus) {
    const oldMuteStatus = Object.assign({}, this._localStatus); // 深拷贝记录原来的状态
    const newMuteStatus = Object.assign({}, oldMuteStatus, muteStatus); // 深拷贝计算新的状态
    this._localStatus = Object.assign({}, newMuteStatus);
    this._info(
      'localStatusUpdated',
      `${JSON.stringify(muteStatus)} | ${JSON.stringify(oldMuteStatus)} -> ${JSON.stringify(newMuteStatus)}`,
    );
    // 计算推流状态
    const publishStatus = Object.assign({}, newMuteStatus); // 深拷贝本地mute状态后，做进一步处理
    this._updatePublishStatus(publishStatus);
  }

  // 更新[辅路视频/共享屏幕]流
  @logMethod
  private _updateScreenStream(muteStatus: TStreamStatus) {
    // 计算并更新本地mute状态
    const oldMuteStatus = Object.assign({}, this._localStatus); // 深拷贝记录原来的状态
    const newMuteStatus = Object.assign({}, oldMuteStatus, muteStatus); // 深拷贝计算新的状态
    this._localStatus = Object.assign({}, newMuteStatus); // 深拷贝保存新的状态
    this._info(
      'localStatusUpdated',
      `${JSON.stringify(muteStatus)} | ${JSON.stringify(oldMuteStatus)} -> ${JSON.stringify(newMuteStatus)}`,
    );
    const publishStatus = Object.assign({}, newMuteStatus); // 深拷贝本地mute状态后，做进一步处理
    publishStatus.screenAudio = publishStatus.screenAudio && publishStatus.screen; // 开启屏幕分享后才允许分享系统声音
    // 流推送状态未发生任何变化，直接返回
    if (
      publishStatus.screen === this._publishStatus.screen
      && publishStatus.screenAudio === this._publishStatus.screenAudio
      && publishStatus.screenPaused === this._publishStatus.screenPaused
    ) {
      return Promise.resolve();
    }

    if (!publishStatus.join || !publishStatus.screen) {
      return this._cancelPublishShareStream();
    }
    return TState.instance
      .promiseState(TTrtcWeb.ScreenPublishState, true)
      .then(() => {
        // 屏幕分享暂停状态发生了变化，不重新推流，避免出现屏幕分享选择窗口，直接切换流净画状态
        if (
          publishStatus.screen
          && publishStatus.screenPaused !== this._publishStatus.screenPaused
        ) {
          this._client.updateScreenShare({
            publish: !publishStatus.screenPaused,
          });
        }
        this._updateScreenPublishStatus(publishStatus); // 更新推流状态
        return Promise.resolve();
      });
  }

  // 取消[辅路视频/共享屏幕]流
  @logMethod
  private _cancelPublishShareStream(updateStatus = true): Promise<void> {
    this._info(
      '_cancelPublishShareStream',
      this._shareStream ? i18next.t('取消发布') : i18next.t('无变化'),
    );
    this._info('[TRTC]', `_cancelPublishShareStream: ${this._shareStream}`);
    return TState.instance
      .promiseState(TTrtcWeb.ScreenPublishState, true)
      .then(async () => {
        await this._client.stopScreenShare();
        if (updateStatus) {
          this._updateScreenPublishStatus({
            screen: false,
            screenPaused: false,
          });
        }
        return Promise.resolve();
      });
  }

  // 流镜像与旋转
  @logMethod
  private renderLocalStreamByMirrorFlag() {
    const transform = this
      ._mirror
      ? 'rotateY(180deg)'
      : '';
    const renderMirror = (dom: HTMLElement) => {
      const videoDom = dom.querySelector('video') || dom.querySelector('canvas');
      if (videoDom) videoDom.style.transform = transform;
    };
    this._testVideoDom && renderMirror(this._testVideoDom);
    this._localVideoDom && renderMirror(this._localVideoDom);
  }

  /**
   * 视频流异常处理
   * [1、本地设备丢失，2、本地采集流冲突、3、发布、推流失败，4、拉流失败，5、渲染失败，6、浏览器渲染限制]
   */
  @logMethod
  private streamErrorHandler(error: any, deviceType = '') {
    this._error('error', `${TUtil.getErrorDetailMessage(error)}, ${deviceType}`);
    let errorName = '';
    let errorCode = 0;
    let needBreakingChange = true; // 是否要阻止主流程
    if (typeof error === 'number') {
      errorCode = error;
    } else if (typeof error === 'string') {
      errorName = error;
    } else if (typeof error === 'object') {
      if (error && typeof error.getCode() === 'number') {
        errorCode = error.getCode();
        if (errorCode === 0x1004) {
          // 0x1004 要根据错误名再细分，详见 https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/module-ErrorCode.html
          errorName = error.name;
        }
      }
    }

    let errObj = {
      deviceStatus: TDeviceStatus.Fail,
      errMsg: '',
      isError: false,
    };
    if (errorName) {
      errObj = this.streamErrorNameHandler(errorName, deviceType);
    }
    if (errorCode) {
      if (errorCode === 0x1002) {
        needBreakingChange = true;
      }
      errObj = this.streamErrorCodeHandler(errorCode, deviceType);
    }
    // 错误信息事件上报.
    if (errObj.errMsg) {
      if (errObj.isError) {
        TCIC.SDK.instance.reportLog('showToast', `${errObj.errMsg},${error}`);
        TEvent.instance.notify(
          TMainEvent.Error,
          new TCICError(
            errorCode,
            errObj.errMsg,
            JSON.stringify(errorCode),
          ),
        );
      } else {
        TEvent.instance.notify(
          TMainEvent.Warn, // toast 提示不阻断主流程
          new TCICError(
            errorCode,
            errObj.errMsg,
            JSON.stringify(errorCode),
          ),
        );
      }
    }
    return {
      name: errorName,
      ...errObj,
    };
  }

  /**
   * 视频流错误码区别处理
   * 设备类型: 'video' | 'audio' | ''
   */
  @logMethod
  private streamErrorNameHandler(errorName: string, deviceType = '', errObj?: any) {
    this._error('streamErrorNameHandler', `${errorName},${deviceType}, ${TUtil.getErrorDetailMessage(errObj)}`);
    // TODO : 解决 start failed: -1001 |
    // The device cannot be used for unknown reasons | RtcError: please call join() before publish().
    // Refer to: https://web.sdk.qcloud.com/trtc/webrtc/doc/en/index.html
    // &lt;INVALID_OPERATION 0x1001&gt;
    let deviceStatus = TDeviceStatus.Fail;
    let errMsg = '';
    const isError = false;
    const deviceName = deviceType === 'video'
      ? i18next.t('摄像头') : deviceType === 'audio'
        ? i18next.t('麦克风') : `${i18next.t('麦克风')}/${i18next.t('摄像头')}`;
    switch (errorName) {
      case 'NotFoundError':
        deviceStatus = TDeviceStatus.Not_Found;
        errMsg = i18next.t('没有找到对应的设备');
        break;
      case 'NotAllowedError':
      case 'SecurityError':
        deviceStatus = TDeviceStatus.No_Permission;
        errMsg = i18next.t('无法访问你的{{name}}设备，请检查系统权限设置', { name: deviceName });
        break;
      case 'NotReadableError':
        deviceStatus = TDeviceStatus.Busy;
        errMsg = i18next.t('请确保没有其他应用占用了{{name}}设备', { name: deviceName });
        break;
      case 'OverConstrainedError':
        errMsg = i18next.t('设备参数配置异常，请重新选择设备');
        break;
      case 'AbortError':
        errMsg = `${i18next.t('未知原因导致设备无法被使用')}`;
        if (errObj && errObj.code) {
          errMsg += `[Abort:${errObj.code}]`;
        }
        break;
      case 'RtcError':
        errMsg = i18next.t('未知原因导致设备无法被使用');
        if (errObj && errObj.code) {
          errMsg += ` [RTC:${errObj.code}]`;
        }
        break;
      case 'TypeError':
        errMsg = i18next.t('打开{{name}}设备遇到一些问题', { name: deviceName });
        break;
      default:
        console.debug(errorName);
        break;
    }
    return { deviceStatus, errMsg, isError };
  }

  /**
   * 视频流错误码区别处理
   * https://cloud.tencent.com/document/product/647/34342
   */
  @logMethod
  private streamErrorCodeHandler(errorCode: number, deviceType = '') {
    let deviceStatus = TDeviceStatus.Fail;
    let errMsg = '';
    let isError = false;
    const deviceName = deviceType === 'video'
      ? i18next.t('摄像头') : deviceType === 'audio'
        ? i18next.t('麦克风') : `${i18next.t('麦克风')}/${i18next.t('摄像头')}`;
    switch (errorCode) {
      case 5302:
        deviceStatus = TDeviceStatus.No_Permission;
        errMsg = deviceType === 'video' ? i18next.t('没有权限打开摄像头') : i18next.t('没有权限打开麦克风');
        break;
      case 5200:
      case 0x1002:
        deviceStatus = TDeviceStatus.Not_Supported;
        errMsg = i18next.t('当前浏览器不支持，请升级或切换其他浏览器');
        break;
      case 5301:
      case 0x1003:
        deviceStatus = TDeviceStatus.Not_Found;
        errMsg = i18next.t('当前设备没有可用的{{name}}', { name: deviceName });
        break;
      case 5501:
      case 0x4003:
        errMsg = i18next.t('数据传输通道错误，请检查防火墙端口限制');
        break;
      case 5502:
      case 5503:
      case 0x4006: // WebSocket信令重连失败
      case 0x4007: // 上行PeerConnection重连失败
      case 0x4008: // 下行PeerConnection重连失败
      case 0x4040: // 用户被踢出房间
      case 0x4041: // 媒体传输服务超时
      case 0x4042: // 远端流订阅超时
        isError = true;
        errMsg = `(Error Code: ${errorCode}) ${i18next.t('网络不佳，连接已超时。请检查您的网络情况或尝试重新进入。')}`;
        break;
      case 0x4043: // 自动播放被禁止错误
        errMsg = i18next.t('音视频自动播放被系统限制');
        break;
      case 5303:
      case 0x4044:
        deviceStatus = TDeviceStatus.Busy;
        errMsg = i18next.t('{{name}}采集失败，请检查连接线或设备是否被其他应用占用', { name: deviceName });
        break;
      default:
        break;
    }
    return { deviceStatus, errMsg, isError };
  }

  // 获取美颜插件
  @logMethod
  private _getBeautyPlugin(useWebAR?: boolean): any {
    if (this._webARInstance) {
      return this._webARInstance;
    }
    const videoTrack = this._client.getVideoTrack();
    const stream = new MediaStream();
    stream.addTrack(videoTrack);

    const authData = {
      licenseKey: WebarUtil.getLicenseKey(),
      appId: '**********',
      authFunc: WebarUtil.getSignature,
    };
    const initBeautify = {
      whiten: 0, // 美白 0-1
      dermabrasion: 0, // 磨皮 0-1
      lift: 0, // 瘦脸 0-1
      shave: 0, // 削脸 0-1
      eye: 0, // 大眼 0-1
      chin: 0, // 下巴 0-1
    };
    const config = {
      // input: this._localVideoStream.mediaStream_,
      module: {
        beautify: true, // 是否启用美颜模块，启用后可以使用美颜、美妆、贴纸等功能
        segmentation: true, // 是否启用人像分割模块，启用后可以使用背景功能
      },
      auth: authData, // 鉴权参数
      beautify: initBeautify,
      input: stream,
      initReport: false,
      logLevel: 'ERROR',
    };
    Object.assign(config.beautify, this._beautyCfg);
    this._webARInstance = new TWebAR(config);
    return this._webARInstance;
    // 废弃
    // if (!this._rtcLocalBeautyPlugin) {
    //   // 初始化美颜插件，rtc-beauty-plugin
    //   // https://web.sdk.qcloud.com/trtc/webrtc/doc/zh-cn/tutorial-28-advanced-beauty.html
    //   this._rtcLocalBeautyPlugin = new (window as any).RTCBeautyPlugin();
    // }
    // return this._rtcLocalBeautyPlugin;
  }

  @logMethod
  private _destroyBeautyPlugin() {
    // this._rtcLocalBeautyPlugin?.destroy();
    // this._rtcLocalBeautyPlugin = null;
    // this._webARInstance?.destroy();
    // this._localBeautyStream?.stop();
    // this._localBeautyStream?.close();
    // this._localBeautyStream = null;
    // this._webARInstance = null;
  }

  @logMethod
  private async startAiDenoiser() {
    await this._client.startPlugin('AIDenoiser', {
      assetsPath: `${TRTCAIDenoiserPath}/assets`, // 例：denoiser-wasm.js 文件存放在 assets 目录下
      sdkAppId: this._sdkAppId,
      userId: this._userId,
      userSig: this._userSig,
    });
  }

  @logMethod
  private async stopAiDenoiser() {
    await this._client.stopPlugin('AIDenoiser');
  }


  @logMethod
  private async _getDeviceLabelByDeviceId(deviceId: string): Promise<string> {
    try {
      this._info('_getDeviceLabelByDeviceId', `start ${deviceId}`);
      const devices = await this._getDevices();
      // const devices: MediaDeviceInfo[] = await this._trtc.getDevices();
      const device = devices.find((item: any) => item.deviceId === deviceId);
      this._info('_getDeviceLabelByDeviceId', device ? `device ${deviceId} found, label=${device.label}` : 'device ${deviceId} not found');
      return device ? device.label : '';
    } catch (err) {
      this._warn('_getDeviceLabelByDeviceId', `getDevices error: ${err}`);
      return '';
    }
  }
  private async _getDevices(requestPermission = true) {
    const [cameraDevices, micDevices, speakerDevices] = await Promise.all([
      TRTC.getCameraList(requestPermission),
      TRTC.getMicrophoneList(requestPermission),
      TRTC.getSpeakerList(requestPermission),
    ]);
    // 移除虚拟设备
    const devices = [...cameraDevices, ...micDevices, ...speakerDevices];
    // 去重
    return devices.filter(item => item.deviceId != 'default');
  }

  // 一些摄像头设备有兼容性问题，需要对视频 profile 做降级
  @logMethod
  private async _applyCameraCompatibleVideoProfile(deviceLabel: string) {
    if (!deviceLabel) return;

    const COMPATIBLE_SOLUTIONS = [
      {
        // Dell Inspiron 5537 内置摄像头(vid=064e, pid=812e) 不支持标准画质 960x540@15fps，需要降级到 640x480@15fps
        // https://github.com/linuxhw/LsUSB/blob/master/Notebook/Dell/Inspiron/Inspiron%205537/1C3EEF77A4AB/UBUNTU-21.10/5.13.0-30-GENERIC/X86_64/72D6263861
        name: 'Dell Inspiron 5537 Integrated Webcam',
        match: (deviceLabel: string) => deviceLabel.endsWith('(064e:812e)'),
        videoProfile: {
          width: 640,
          height: 480,
          frameRate: 15,
          bitrate: 500,
        },
      },
    ];

    const solution = COMPATIBLE_SOLUTIONS.find(solution => solution.match(deviceLabel));

    if (solution) {
      this._info('_applyCameraCompatibleVideoProfile', `deviceLabel [${deviceLabel}] matched solution [${solution.name}], apply videoProfile ${JSON.stringify(solution.videoProfile)}`);
      await this._client.updateLocalVideo({
        option: {
          profile: solution.videoProfile,
        },
      });
    }
  }

  // 只清理内部数据，不处理 executor
  @logMethod
  private async _stopCameraTestInner() {
    const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    await this._client.stopLocalVideo();
    this._cameraTestStarted = false;
    this._testVideoDom = null;
    this._setState(TMainState.Video_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Video_Capture, false);
    return Promise.resolve();
  }

  // 只清理内部数据，不处理 executor
  @logMethod
  private async _stopMicTestInner() {
    const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    await this._client.stopLocalAudio();
    this._micTestStarted = false;
    this._setState(TMainState.Audio_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Audio_Capture, false);
    return Promise.resolve();
  }

  @logMethod
  private async _processCameraResult(
    executor: TPromiseExecutor,
    open: boolean,
    err?: any,
    { isTest, startTime }: { isTest?: boolean; startTime?: number; } = {},
  ) {
    const actionName = isTest ? 'startCameraTest' : 'startLocalVideo';

    if (open) {
      this._setState(TMainState.Video_Device_Status, TDeviceStatus.Open);
      this._setState(TMainState.Video_Capture, true);
      this.updatePublishStatus({
        video: true,
      });
      this._info(actionName, `success, timeCost ${Date.now() - startTime}`);
      executor.resolve();
    } else {
      let info;
      if (err && err.name) {
        // RtcError 判断 code
        if (err.code && err.extraCode) {
          info = this.streamErrorHandler(err.extraCode, 'video');
        }
        // 未知 code 或者其他错误判断 name
        if (!info?.errMsg) {
          info = this.streamErrorHandler(err.name, 'video');
        }
      }

      // 可能本地成功但是publish失败，本地失败的才设置 Video_Device_Status
      const localSucc = TState.instance.getState(TMainState.Video_Capture);
      if (!localSucc) {
        this._setState(TMainState.Video_Device_Status, info?.deviceStatus || TDeviceStatus.Fail);
      }

      if (!isTest) {
        try {
          await this.stopLocalVideo();
        } catch (stopErr) {
          console.warn('stopLocalVideo err', stopErr);
        }
      }
      if (info) {
        const statusCode = info.deviceStatus || -5;
        executor.reject(new TCICError(statusCode, info.errMsg || err.msg || err.errorMsg, err.toString()));
      } else {
        // 确保非预期的异常能正确的上抛
        executor.reject(new TCICError(
          err.errorCode || err.code,
          i18next.t('打开本地摄像头遇到一些问题'),
          err.toString(),
        ));
      }
    }
  }

  @logMethod
  private async _processMicResult(
    executor: TPromiseExecutor,
    open: boolean,
    err?: any,
    { isTest, startTime }: { isTest?: boolean; startTime?: number; } = {},
  ) {
    const actionName = isTest ? 'startMicTest' : 'startLocalAudio';
    if (open) {
      this._setState(TMainState.Audio_Device_Status, TDeviceStatus.Open);
      this._setState(TMainState.Audio_Capture, true);
      this.updatePublishStatus({
        audio: true,
      });
      this._info(actionName, `success, timeCost ${Date.now() - startTime}`);
      executor.resolve();
    } else {
      let info;
      if (err && err.name) {
        // RtcError 判断 code
        if (err.code && err.extraCode) {
          info = this.streamErrorHandler(err.extraCode, 'audio');
        }
        // 未知 code 或者其他错误判断 name
        if (!info?.errMsg) {
          info = this.streamErrorHandler(err.name, 'audio');
        }
      }
      // 可能本地成功但是publish失败，本地失败的才设置 Audio_Device_Status
      const localSucc = TState.instance.getState(TMainState.Audio_Capture);
      if (!localSucc) {
        this._setState(TMainState.Audio_Device_Status, info?.deviceStatus || TDeviceStatus.Fail);
      }
      if (!isTest) {
        try {
          await this.stopLocalAudio();
        } catch (stopErr) {
          console.warn('stopLocalAudio err', stopErr);
        }
      }
      if (info) {
        const statusCode = info.deviceStatus || -5;
        executor.reject(new TCICError(statusCode, info.errMsg || err.msg || err.errorMsg, err.toString()));
      } else {
        // 确保非预期的异常能正确的上抛
        executor.reject(new TCICError(
          err.errorCode || err.code,
          i18next.t('打开本地麦克风遇到一些问题'),
          err.toString(),
        ));
      }
    }
  }
}
