import { TCameraResolution } from '../business/tbusiness_class';
import { TMain } from '../tmain';
import { TSession } from '../tsession';
import {
  TTrtcBase,
  TTrtcDeviceType,
  TTrtcVideoResolution,
  getVideoSizeFromResolution,
  getResolutionFromVideoSize,
  isDeviceAbnormal,
} from './ttrtc_base';
import { TTrtcElectron } from './ttrtc_electron';
import { TTrtcMobile } from './ttrtc_mobile';
import { TTrtcWeb } from './ttrtc_web';

export const DeviceStr = {
  [TTrtcDeviceType.Unknown]: 'unknown',
  [TTrtcDeviceType.Mic]: 'micId',
  [TTrtcDeviceType.Speaker]: 'speakerId',
  [TTrtcDeviceType.Camera]: 'cameraId',
};
export const DeviceNameStr = {
  [TTrtcDeviceType.Unknown]: 'unknown',
  [TTrtcDeviceType.Mic]: 'micName',
  [TTrtcDeviceType.Speaker]: 'speakerName',
  [TTrtcDeviceType.Camera]: 'cameraName',
};

export class TTrtcUtil {
  public static getVideoSizeFromResolution = getVideoSizeFromResolution;
  public static getResolutionFromVideoSize = getResolutionFromVideoSize;
  public static isDeviceAbnormal = isDeviceAbnormal;

  private static _instance: TTrtcBase = null;

  /**
   * 将后台返回的摄像头分辨率类型转为视频分辨率类型
   * @param resolution 摄像头分辨率
   */
  public static getVideoResolutionFromCameraResolution(
    resolution: TCameraResolution,
    opts?: {autoReduce: boolean},
  ): TTrtcVideoResolution {
    // console.trace('TRTC_UTIL:setVideoEncoderParam', resolution);
    const autoReduce = opts !== undefined ? opts.autoReduce :  true;

    const resolutionMapping  = new Map();
    resolutionMapping.set(
      TCameraResolution.Resolution_640_360,
      autoReduce ? TTrtcVideoResolution.Resolution_320_180 : TTrtcVideoResolution.Resolution_640_360,
    );
    resolutionMapping.set(
      TCameraResolution.Resolution_960_540,
      autoReduce ? TTrtcVideoResolution.Resolution_320_180 : TTrtcVideoResolution.Resolution_960_540,
    );
    resolutionMapping.set(
      TCameraResolution.Resolution_1280_720,
      autoReduce ? TTrtcVideoResolution.Resolution_640_360 : TTrtcVideoResolution.Resolution_1280_720,
    );
    resolutionMapping.set(
      TCameraResolution.Resolution_1920_1080,
      autoReduce ? TTrtcVideoResolution.Resolution_1280_720 : TTrtcVideoResolution.Resolution_1920_1080,
    );
    resolutionMapping.set(5 /* TCameraResolution.Resolution_480_480 */, TTrtcVideoResolution.Resolution_480_480);
    resolutionMapping.set(TCameraResolution.Resolution_320_180, TTrtcVideoResolution.Resolution_320_180);

    return resolutionMapping.get(resolution) || TTrtcVideoResolution.Resolution_320_180;
  }

  /**
   * 行业参考值
   * 分辨率    帧率(fps)   基准码率(Kbps)   直播码率(Kbps)
   * 160*120	15	        65	            130
   * 120*120	15	        50	            100
   * 320*180	15	        140	            280
   * 180*180	15	        100	            200
   * 240*180	15	        120	            240
   * 320*240	15	        200	            400
   * 240*240	15	        140	            280
   * 424*240	15	        220	            440
   * 640*360	15	        400	            800
   * 360*360	15	        260	            520
   * 640*360	30	        600	            1200
   * 360*360	30	        400	            800
   * 480*360	15	        320	            640
   * 480*360	30	        490	            980
   * 640*480	15	        500	            1000
   * 480*480	15	        400	            800
   * 640*480	30	        750	            1500
   * 480*480	30	        600	            1200
   * 848*480	15	        610	            1220
   * 848*480	30	        930	            1860
   * 640*480	10	        400	            800
   * 1280*720	15	        1130            2260
   * 1280*720	30	        1710            3420
   * 960*720	15	        910	            1820
   * 960*720	30	        1380            2760
   */
  /**
   * 参考 TRTC 推荐值
   * https://cloud.tencent.com/document/product/647/32236
   * https://cloud.tencent.com/document/product/647/79641
   */
  public static getVideoBitrate(resolution: TTrtcVideoResolution, fps: number, level: number) {
    let baseBitrate = 150;  // 默认基准码率
    let levelBitrate = 100; // 每增加一档清晰度增加的码率
    switch (resolution) {
      case TTrtcVideoResolution.Resolution_120_120:
        baseBitrate = 80;
        levelBitrate = 40;
        break;
      case TTrtcVideoResolution.Resolution_160_160:
        baseBitrate = 100;
        levelBitrate = 50;
        break;
      case TTrtcVideoResolution.Resolution_270_270:
        baseBitrate = 200;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_480_480:
        baseBitrate = 350;
        levelBitrate = 150;
        break;
      case TTrtcVideoResolution.Resolution_160_120:
        baseBitrate = 100;
        levelBitrate = 50;
        break;
      case TTrtcVideoResolution.Resolution_240_180:
        baseBitrate = 150;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_280_210:
        baseBitrate = 200;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_320_240:
        baseBitrate = 200;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_400_300:
        baseBitrate = 300;
        levelBitrate = 150;
        break;
      case TTrtcVideoResolution.Resolution_480_360:
        baseBitrate = 400;
        levelBitrate = 200;
        break;
      case TTrtcVideoResolution.Resolution_640_480:
        baseBitrate = 600;
        levelBitrate = 300;
        break;
      case TTrtcVideoResolution.Resolution_960_720:
        baseBitrate = 1000;
        levelBitrate = 500;
        break;
      case TTrtcVideoResolution.Resolution_160_90:
        baseBitrate = 150;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_256_144:
        baseBitrate = 200;
        levelBitrate = 100;
        break;
      case TTrtcVideoResolution.Resolution_320_180:
        baseBitrate = 250;
        levelBitrate = 150;
        break;
      case TTrtcVideoResolution.Resolution_480_270:
        baseBitrate = 350;
        levelBitrate = 200;
        break;
      case TTrtcVideoResolution.Resolution_640_360:
        baseBitrate = 300;
        levelBitrate = 150;
        break;
      case TTrtcVideoResolution.Resolution_960_540:
        baseBitrate = 850;
        levelBitrate = 450;
        break;
      case TTrtcVideoResolution.Resolution_1280_720:
        baseBitrate = 900;
        levelBitrate = 450;
        break;
      case TTrtcVideoResolution.Resolution_1920_1080:
        baseBitrate = 2000;
        levelBitrate = 1000;
        break;
    }
    return Math.round(((baseBitrate + levelBitrate * level) / 15) * fps); // 码率以15FPS为基准，所以要按实际帧率换算
  }

  /**
   * 获取实例
   */
  public static getInstance(): TTrtcBase {
    if (!TTrtcUtil._instance) {
      if (TSession.instance.isIOSNative() || TSession.instance.isAndroidNative()) {
        if (TSession.instance.isIOSNative()) {
          TTrtcUtil._instance = new TTrtcMobile();
        } else {
          if (TSession.instance.isX5Webview()) {
            TTrtcUtil._instance = new TTrtcMobile();
          } else {
            if (TSession.instance.isInFaithOrDemo()) {
              TTrtcUtil._instance = new TTrtcWeb();
              TMain.instance.reportLog('UseSystemWebViewAndWebRTC', '');
            } else {
              TMain.instance.reportLog('UseSystemWebViewAndNativeRTC', '');
              TTrtcUtil._instance = new TTrtcMobile();
            }
          }
        }
      } else if (TSession.instance.isElectron()) {
        TTrtcUtil._instance = new TTrtcElectron();
      } else {
        TTrtcUtil._instance = new TTrtcWeb();
      }
    }
    return TTrtcUtil._instance;
  }

  /**
   * 返回该类实例
   */
  public static get instance(): TTrtcBase {
    return this.getInstance();
  }

  /**
   * 存储上次设备基本信息，下次获取默认值
   * @private
   */
  public static saveDeviceConfig(config: any) {
    localStorage.setItem('device', JSON.stringify(config));
  }
  /**
   * TODO: 缓存设备信息
   */
  public static getDeviceLocalStorageConfig() {
    try {
      return JSON.parse(localStorage.getItem('device') || '{}');
    } catch {
      console.error('get Device local config failed');
      return {};
    }
  }

  public static setLocalStorageDeviceId(deviceType: TTrtcDeviceType, data: any) {
    if (deviceType !== TTrtcDeviceType.Unknown) {
      localStorage.setItem(DeviceStr[deviceType], data);
    }
  }
  public static setLocalStorageDeviceName(deviceType: TTrtcDeviceType, data: any) {
    if (deviceType !== TTrtcDeviceType.Unknown) {
      localStorage.setItem(DeviceNameStr[deviceType], data);
    }
  }

  /**
   * 获取插件列表
   */
  public static getSDKJS(): string {
    if (TSession.instance.isMobileNative()) {
      return TTrtcMobile.getSDKJS();
    }
    if (TSession.instance.isElectron()) {
      return TTrtcElectron.getSDKJS();
    }
    return TTrtcWeb.getSDKJS();
  }

  /**
   * 获取插件列表
   */
  public static getPluginList(): { name: string, url: string }[] {
    if (TSession.instance.isMobileNative()) {
      return TTrtcMobile.getPluginList();
    }
    if (TSession.instance.isElectron()) {
      return TTrtcElectron.getPluginList();
    }
    return TTrtcWeb.getPluginList();
  }
}
