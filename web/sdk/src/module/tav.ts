import { TEvent } from '../base/tevent';
import { TLoggerModule, TModule } from '../base/tmodule';
import { TDeviceStatus, TMainEvent, TMainState, TPlatform } from '../constants';
import { TCameraResolution, TClassStatus } from './business/tbusiness_class';
import { TScreenState, TStreamInfo, TStreamType } from './business/tbusiness_member';
import { TMain } from './tmain';
import { TSession } from './tsession';
import { TTrtcEvent, TTrtcStatistics, TTrtcVideoResolution, TTrtcVideoStreamType } from './trtc/ttrtc_base';
import { TTrtcUtil, TTrtcUtil as TTrtc } from './trtc/ttrtc_util';
import { TLiveUtil as TLive } from './live/live_util';

let remoteAVInfoReq = 0;

class AsyncQueue {
  private queue: Promise<void>;

  constructor() {
    this.queue = Promise.resolve(); // 初始化一个已解决的 Promise
  }

  // 默认超时800ms
  public enqueue(asyncFunction: () => Promise<any>, timeout = 800): void {
    // 将新的异步函数添加到队列中
    this.queue = this.queue.then(() => this.withTimeout(asyncFunction(), timeout));
  }

  // 修改超时处理的函数
  private withTimeout<T>(promise: Promise<T>, timeout: number, timeoutValue: T = null): Promise<T> {
    const controller = new AbortController();
    return Promise.race([
      promise.then((res) => {
        controller.abort();
        return res;
      }),
      new Promise<T>((resolve) => {
        setTimeout(() => {
          controller.abort();
          resolve(timeoutValue);
        }, timeout);
      }),
    ]);
  }
}

/**
 * 远端音视频信息
 */
class TRemoteAVInfo extends TLoggerModule {
  readonly objId: string;
  public sequence = 0;      // 主路序列号
  public subSequence = 0;   // 辅路序列号
  public userId = '';
  public audioAvailable = false;
  public audioMute = false;
  public audioRendering: boolean = undefined;
  public videoAvailable = false;
  public videoRendering = false;
  public screen = false;
  public screenRendering = false;
  public videoDom: HTMLElement;
  public screenDom: HTMLElement;
  public mainLive = false;
  public mainLiveType: TTrtcVideoStreamType;
  public mainLiveUrl = '';
  public mainFullMode = false;
  public subLive = false;
  public subLiveUrl = '';
  public subFullMode = true;
  public tav: TAV = null;
  public isTrtcFitMode = false;     // 当前是否使用Fit自适应模式渲染

  constructor(userId: string, tav: TAV) {
    super();
    this.userId = userId;
    this.tav = tav;

    // eslint-disable-next-line no-plusplus
    remoteAVInfoReq++;
    this.objId = `TRemoteAVInfo_${remoteAVInfoReq}_${userId}`;
  }

  public genNewSequence(): number {
    this.sequence = window.performance.now();
    return this.sequence;
  }

  public genNewSubSequence(): number {
    this.subSequence = window.performance.now();
    return this.subSequence;
  }

  public adjustMainLiveUrl() {
    // 只调整混流
    if (this.userId !== 'mix' || !this.mainLiveUrl) {
      return;
    }
    if (this.mainLiveType === TTrtcVideoStreamType.QLive && !/^webrtc/.test(this.mainLiveUrl)) {
      this.mainLiveUrl = this.mainLiveUrl.replace(/^https?/, 'webrtc').replace('_mix.flv', '_mix');
      console.log(`[${this.objId}][mix] adjustMainLiveUrl, QLive`, this.mainLiveUrl);
    } else if (this.mainLiveType !== TTrtcVideoStreamType.QLive && /^webrtc/.test(this.mainLiveUrl)) {
      this.mainLiveUrl = this.mainLiveUrl.replace(/^webrtc?/, 'https').replace('_mix', '_mix.flv');
      console.log(`[${this.objId}][mix] adjustMainLiveUrl, Big`, this.mainLiveUrl);
    }
  }

  public updateAVState(videoDomUpdated = false, screenDomUpdated = false): Promise<boolean> {
    const promiseList: Promise<any>[] = [];
    let somethingChanged = false;

    const stack = new Error().stack;

    const myUserId = TMain.instance.getUserId();
    const isRTCMode = TMain.instance.getState(TMainState.RTC_Mode, true);
    const isClassStarted = TMain.instance.getState(TMainState.Class_Status) === TClassStatus.Already_Start;
    const permission = TMain.instance.getPermission(this.userId);
    const hasLiveStream = this.mainLive && !isRTCMode;  // 有快直播流
    // this._info(
    //   'updateAVState:: ',
    //   `${this.objId}, isClassStarted ${isClassStarted}, permission ${JSON.stringify(permission)}`,
    // );

    // #region 更新音频播放状态
    const hasMicPermissionAndMicNotClosed = isClassStarted
      && permission.mic
      && (permission.micState === TDeviceStatus.Unknown || permission.micState === TDeviceStatus.Open);
    const hasScreenPermissionAndScreenSharing = isClassStarted
      && !!permission.screen
      && permission.screenState === TScreenState.Sharing;
    const isPlayingMusic = isClassStarted
      && permission.screenState === TScreenState.MusicPlaying;
    // 有麦克风权限且麦克风开启，或者有屏幕共享权限且正在共享，或者正在播放音乐
    const conditionRenderAudio = hasMicPermissionAndMicNotClosed
      || hasScreenPermissionAndScreenSharing
      || isPlayingMusic;

    const shouldAudioRendering = (this.audioAvailable || hasLiveStream)
      && conditionRenderAudio
      && !this.tav.isMuteAllRemoteAudio
      && !this.audioMute;

    const shouldUpdateAudioRendering = this.audioRendering !== shouldAudioRendering;
    const reportAudioAction = `updateAVState.audio.${isRTCMode ? 'rtc' : 'live'}`;
    const reportAudioData: any = {
      objId: this.objId,
      myUserId,
      targetUserId: this.userId,
      targetUserName: permission?.userName,
      isClassStarted,
      isRTCMode,
      'this.audioAvailable': this.audioAvailable,
      hasLiveStream,
      'this.audioMute': this.audioMute,
      'this.tav.isMuteAllRemoteAudio': this.tav.isMuteAllRemoteAudio,
      'this.audioRendering': this.audioRendering,
      shouldAudioRendering,
      shouldUpdateAudioRendering,
      hasMicPermissionAndMicNotClosed,
      hasScreenPermissionAndScreenSharing,
      isPlayingMusic,
      conditionRenderAudio,
      mute: !shouldAudioRendering,
      stack,
    };

    // 如果老师的audio应该播放但没播放, 说明有问题
    const isTeacher = TMain.instance.isTeacher(this.userId);
    if (isTeacher && !shouldAudioRendering && isClassStarted && myUserId !== this.userId) {
      if (permission && permission.mic) {
        if (permission.micState === TDeviceStatus.Unknown
            || permission.micState === TDeviceStatus.Open) { // 已开麦但没有播放， 或者麦状态异常都算异常
          this._error(reportAudioAction, `${JSON.stringify(reportAudioData, null, 2)}`);
        } else if (permission.micState === TDeviceStatus.Closed) { // 自己主动关闭麦，非异常
          this._warn(reportAudioAction, `${JSON.stringify(reportAudioData, null, 2)}`);
        } else {
          this._error(reportAudioAction, `${JSON.stringify(reportAudioData, null, 2)}`);
        }
      } else { // 无麦克风权限， 算异常
        this._error(reportAudioAction, `${JSON.stringify(reportAudioData, null, 2)}`);
      }
    }

    this._info(reportAudioAction, `${shouldAudioRendering ? 'rendering' : 'not rendering'} ${JSON.stringify(reportAudioData, null, 2)}`);
    const isTicPushUser = this.userId.startsWith('tic_push_user');
    // 如果是白板推流机器人,一定要mute 掉音频，否则会有重音出现
    if (isTicPushUser) {
      TTrtc.instance.muteRemoteAudio(this.userId, true);
      TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Big);
      TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Sub);
    }
    if (shouldUpdateAudioRendering) {
      let updateAudioPromise;
      this.audioRendering = shouldAudioRendering;
      somethingChanged = true;

      if (isRTCMode) {
        updateAudioPromise = TTrtc.instance.muteRemoteAudio(this.userId, !shouldAudioRendering)
          .then(() => {
            this._info(reportAudioAction, `${this.objId} TTrtc.instance.muteRemoteAudio ${!shouldAudioRendering} success`);
          })
          .catch((err) => {
            let reportErrInfo = err;
            if (err instanceof Error) {
              reportErrInfo = { message: err.message, stack: err.stack };
            }
            this._error(reportAudioAction, `${this.objId} TTrtc.instance.muteRemoteAudio ${!shouldAudioRendering} error, ${JSON.stringify(reportErrInfo)}`);
          });
        promiseList.push(updateAudioPromise);
      } else {
        // 不用处理
      }
    } else {
      // console.log(reportAudioAction, JSON.stringify(reportAudioData, null, 2));
    }
    // #endregion

    // #region 更新视频播放状态
    let shouldVideoRendering = isClassStarted
      && permission.camera
      && (this.videoAvailable || hasLiveStream)
      && !!this.videoDom;
    let isWebScreen = false;      // 是Web端的屏幕共享
    if (isClassStarted
      && permission.platform === TPlatform.Web
      && permission.screenState < 2
      && this.videoAvailable
      && !!this.videoDom
    ) {
      // web端屏幕分享使用主路
      shouldVideoRendering = true;
      isWebScreen = true;  // web端也支持主流和辅流了
    }

    const shouldUpdateVideoRendering = shouldVideoRendering !== this.videoRendering
      || (shouldVideoRendering && videoDomUpdated);
    const reportVideoAction = `updateAVState.video.${isRTCMode ? 'rtc' : 'live'}`;
    const reportVideoData: any = {
      objId: this.objId,
      myUserId,
      targetUserId: this.userId,
      targetUserName: permission?.userName,
      isClassStarted,
      isRTCMode,
      'this.videoAvailable': this.videoAvailable,
      'this.videoRendering': this.videoRendering,
      shouldVideoRendering,
      shouldUpdateVideoRendering,
      mainSeq: this.sequence,
      videoDomName: this.videoDom ? (this.videoDom.dataset?.domName || 'noDomName') : undefined,
      videoDomUpdated,
      isWebScreen,
      permission,
    };

    // 如果老师的video应该播放但没播放, 说明有问题
    if (isTeacher && !shouldVideoRendering && isClassStarted && myUserId !== this.userId) {
      if (permission && permission.camera) {
        if (permission.cameraState === TDeviceStatus.Unknown || permission.cameraState === TDeviceStatus.Open) {
          this._error(reportVideoAction, `${JSON.stringify(reportVideoData, null, 2)}`);
        } else if (permission.cameraState === TDeviceStatus.Closed) { // 手动关闭的
          this._warn(reportVideoAction, `${JSON.stringify(reportVideoData, null, 2)}`);
        } else {
          this._error(reportVideoAction, `${JSON.stringify(reportVideoData, null, 2)}`);
        }
      } else {
        this._error(reportVideoAction, `${JSON.stringify(reportVideoData, null, 2)}`);
      }
    }

    this._info(reportVideoAction, `${shouldVideoRendering ? 'rendering' : 'not rendering'}, $${JSON.stringify(reportVideoData, null, 2)}`);
    if (shouldUpdateVideoRendering) {
      let updateVideoPromise;
      if (shouldVideoRendering) {
        if (isRTCMode) {
          reportVideoData.trtcStreamType = TTrtcVideoStreamType.Big;
        } else {
          reportVideoData.mainLiveType = this.mainLiveType;
          reportVideoData.mainLiveUrl = this.mainLiveUrl;
        }
      }

      this.videoRendering = shouldVideoRendering;

      if (isRTCMode) {
        if (shouldVideoRendering) {
          TLive.instance.stopPlay(this.mainLiveUrl);
          updateVideoPromise = TTrtc.instance.startRemoteVideo(
            this.userId, TTrtcVideoStreamType.Big,
            this.videoDom,
            true,
          )
            ?.then(() => {
              this._info(reportVideoAction, `${this.objId} TTrtc.instance.startRemoteVideo success`);
            })
            ?.catch((err) => {
              // 播放失败时设置false
              this.videoRendering = false;
              this._error(reportVideoAction, `${this.objId} TTrtc.instance.startRemoteVideo error, ${err?.toString()}`);
            });
          this.isTrtcFitMode = isWebScreen;
        } else {
          updateVideoPromise = TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Big);
        }
      } else {
        if (shouldVideoRendering) {
          TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Big);
          // eslint-disable-next-line max-len
          updateVideoPromise = TLive.instance.startPlay(this.videoDom, this.mainLiveUrl, this.mainFullMode, false, this.userId, false)
            ?.then(() => {
              this._info(reportVideoAction, `${this.objId} TLive.instance.startPlay success`);
            })
            ?.catch((err) => {
              this._error(reportVideoAction, `${this.objId} TLive.instance.startPlay error, ${err?.toString()}`);
            });
        } else {
          updateVideoPromise = TLive.instance.stopPlay(this.mainLiveUrl);
        }
      }
      somethingChanged = true;
      promiseList.push(updateVideoPromise);
    } else {
      // console.log(reportVideoAction, JSON.stringify(reportVideoData, null, 2));
      if (shouldVideoRendering && this.videoRendering && isWebScreen && !this.isTrtcFitMode) {
        // 当前是Web端屏幕共享且渲染方式不匹配
        if (isRTCMode) {   // 互动课堂更新为Fit模式
          TTrtc.instance.updateRemoteVideoFitMode(this.userId, TTrtcVideoStreamType.Big, true);
        }
      }
    }
    // #endregion

    // #region 更新屏幕分享播放状态
    const hasSubLiveStream = this.subLive && !isRTCMode;  // 有快直播流
    const screenRendering = isClassStarted
      && !!(permission.screen && (this.screen || hasSubLiveStream) && !!this.screenDom);
    const reportScreenData: any = {
      objId: this.objId,
      userId: this.userId,
      userName: permission?.userName,
      subSeq: this.subSequence,
      screenDomName: this.screenDom ? (this.screenDom.dataset?.domName || 'noDomName') : undefined,
      screenDomUpdated,
      'this.screenRendering': this.screenRendering,
      screenRendering,
      isRTCMode,
      isClassStarted,
      permission,
    };
    if (screenRendering !== this.screenRendering || (screenRendering && screenDomUpdated)) {
      this.screenRendering = screenRendering;
      let screenPromise;
      if (screenRendering) {
        if (!isRTCMode) {
          reportScreenData.subLiveUrl = this.subLiveUrl;
          this._info('updateAVState screen', JSON.stringify(reportScreenData, null, 2));
          TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Sub);
          screenPromise = TLive.instance
            .startPlay(this.screenDom, this.subLiveUrl, this.subFullMode, true, this.userId, true)
            ?.then(() => {
              this._info('updateAVState screen success', `${this.objId} TLive.instance.startPlay success`);
            })
            ?.catch((err) => {
              this._info('updateAVState screen error', `${this.objId} TLive.instance.startPlay error, ${err?.toString()}`);
            });
        } else {
          reportScreenData.trtcStreamType = TTrtcVideoStreamType.Sub;
          this._info('updateAVState screen', JSON.stringify(reportScreenData, null, 2));
          // TLive.instance.stopPlay(this.subLiveUrl);
          screenPromise = TTrtc.instance.startRemoteVideo(this.userId, TTrtcVideoStreamType.Sub, this.screenDom, true)
            ?.then(() => {
              this._info('updateAVState screen success', `${this.objId} TTrtc.instance.startRemoteVideo success`);
            })
            ?.catch((err) => {
              this._info('updateAVState screen error', `${this.objId} TTrtc.instance.startRemoteVideo error, ${err?.toString()}`);
            });
          this.isTrtcFitMode = true;
        }
      } else {
        this._info('updateAVState screen', JSON.stringify(reportScreenData, null, 2));
        if (!isRTCMode) {
          screenPromise = TLive.instance.stopPlay(this.subLiveUrl);
        } else {
          screenPromise = TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Sub);
        }
      }
      somethingChanged = true;
      promiseList.push(screenPromise);
    } else {
      // console.log('updateAVState screen', JSON.stringify(reportScreenData, null, 2));
    }
    // #endregion

    if (somethingChanged) {
      this._info('updateAVState',  JSON.stringify({
        sequence: this.sequence,
        audioAvailable: this.audioAvailable,
        video: this.videoAvailable,
        screen: this.screen,
        videoDom: !!this.videoDom,
        screenDom: !!this.screenDom,
        objId: this.objId,
        permission,
      }));
    }
    return Promise.all(promiseList).then(() => somethingChanged);
  }

  public stopRendering() {
    const isRTCMode = TMain.instance.getState(TMainState.RTC_Mode, true);
    const isClassStarted = TMain.instance.getState(TMainState.Class_Status) === TClassStatus.Already_Start;
    const permission = TMain.instance.getPermission(this.userId);
    this._info('stopRendering', JSON.stringify({
      objId: this.objId,
      userId: this.userId,
      userName: permission?.userName,
      'this.audioRendering': this.audioRendering,
      'this.videoRendering': this.videoRendering,
      'this.screenRendering': this.screenRendering,
      isRTCMode,
      isClassStarted,
      permission,
    }, null, 2));
    this.audioRendering = false;
    this.videoRendering = false;
    this.screenRendering = false;
    TTrtc.instance.muteRemoteAudio(this.userId, true);
    if (!isRTCMode) {
      TLive.instance.stopPlay(this.mainLiveUrl);
      TLive.instance.stopPlay(this.subLiveUrl);
    } else {
      TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Big);
      TTrtc.instance.stopRemoteVideo(this.userId, TTrtcVideoStreamType.Sub);
    }
  }

  protected _getClassName() {
    return 'TAV';
  }
}

enum ResolutionMode {
  Landscape,
  Portrait
}

export class TAV extends TModule {
  /**
   * 返回该类实例
   */
  public static get instance(): TAV {
    return this.getInstance();
  }
  private static lowFpsRemoteVideoCounts = [16, 8, 5, 3, 2, 1];  // 增加分辨率类型时，这里要对应的增加最低帧率对应的最大下行路数

  public lastVideoResolution: TTrtcVideoResolution;
  public lastVideoFps = 0;
  public lastVideoBitrate = 0;
  public isMuteAllRemoteAudio = false;

  private _remoteAVInfos: Map<string, TRemoteAVInfo> = new Map();    // 远端流信息保存
  private _resolutionMode: ResolutionMode = 0;
  private _expectCameraResolution = TCameraResolution.Resolution_1280_720;
  private _expectCameraFps = 10;
  private _baseBitrateLevel = 0;  // 基础码率级别
  private _highBitrateLevel = 1;  // 高清码率级别，调用setVideoEncodeQuality设置为高清后适用
  private _userHighDefinition = new Map<string, boolean>();
  private _recentlyUpVideoPixelsArray: number[] = [];
  private _recentlyDownVideoPixelsArray: number[] = [];
  private _averageUpVideoPixels = 0;
  private _averageDownVideoPixels = 0;
  private _isClassStarted = false;
  private _autoReduceResolution = false;
  constructor() {
    super();
    const asyncQueue = new AsyncQueue();
    TEvent.instance.on(TMainEvent.Permission_Update, (permissionList) => {
      this._remoteAVInfos.forEach((remoteAVInfo) => {
        asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
      });
    }, { capture: true });
    TEvent.instance.on(TTrtcEvent.Audio_Changed, (param) => {
      const remoteAVInfo = this._getRemoteAVInfo(param.userId);
      remoteAVInfo.audioAvailable = param.available;
      asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
    }, { capture: true });
    TEvent.instance.on(TTrtcEvent.Audio_Mute, (param) => {
      const remoteAVInfo = this._getRemoteAVInfo(param.userId);
      remoteAVInfo.audioMute = param.mute;
      asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
    }, { capture: true });
    TEvent.instance.on(TTrtcEvent.Video_Changed, (param) => {
      const remoteAVInfo = this._getRemoteAVInfo(param.userId);
      remoteAVInfo.videoAvailable = param.available;
      asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
    }, { capture: true });
    TEvent.instance.on(TTrtcEvent.SubStream_Changed, (param) => {
      const remoteAVInfo = this._getRemoteAVInfo(param.userId);
      remoteAVInfo.screen = param.available;
      asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
    }, { capture: true });
    TEvent.instance.on(TMainEvent.Stream_Update, (streams) => {
      this._remoteAVInfos.forEach((remoteAVInfo) => {   // 重置所有流状态
        remoteAVInfo.mainLive = false;
        remoteAVInfo.subLive = false;
      });

      streams.forEach((stream: TStreamInfo) => {    // 添加流状态
        console.log('[TAV] Stream_Update', stream.userId, stream.type, stream);
        const remoteAVInfo = this._getRemoteAVInfo(stream.userId);
        if (stream.type === TStreamType.Sub) {
          remoteAVInfo.subLive = true;
          remoteAVInfo.subLiveUrl = stream.url;
        } else {
          remoteAVInfo.mainLive = true;
          remoteAVInfo.mainLiveUrl = stream.url;
          remoteAVInfo.adjustMainLiveUrl();
        }
      });

      this._remoteAVInfos.forEach((remoteAVInfo) => { // 刷新流状态
        asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
      });
    });

    TEvent.instance.on(TTrtcEvent.Stream_Added, (param) => {
      this._info('TTrtcEvent.Stream_Added', JSON.stringify(param));

      if (this._remoteAVInfos.has(param.userId)) {
        const remoteAVInfo = this._getRemoteAVInfo(param.userId);
        // 清除旧的 audioRendering 状态，使得下一次 updateAVState 时重设 muteAudio 状态
        remoteAVInfo.audioRendering = undefined;
      }
    }, { capture: true });

    TMain.instance.subscribeState(TMainState.RTC_Mode, (isRTCMode) => {
      this._remoteAVInfos.forEach((remoteAVInfo) => { // 刷新流状态
        remoteAVInfo.videoRendering = false; // 重置拉流标识
        remoteAVInfo.screenRendering = false;
        asyncQueue.enqueue(() => remoteAVInfo.updateAVState()) ;
      });
    });

    this._isClassStarted = TMain.instance.getState(TMainState.Class_Status) === TClassStatus.Already_Start;
    TMain.instance.subscribeState(TMainState.Class_Status, (status) => {
      const isClassStarted = status === TClassStatus.Already_Start;
      if (isClassStarted === this._isClassStarted) {
        return;
      }
      this._isClassStarted = isClassStarted;
      if (!this._isClassStarted) {
        // 下课后全部 stopRendering
        this._remoteAVInfos.forEach((remoteAVInfo) => {
          remoteAVInfo.stopRendering();
        });
      }
    });
  }

  public setLocalVideoEncodeParams(
    cameraResolution: TCameraResolution,
    fps: number,
    baseBitrateLevel: number,
    highBitrateLevel: number,
    resolutionMode: 0 | 1 = 0,
    autoReduceResolution = false,
  ) {
    this._expectCameraResolution = cameraResolution;
    this._resolutionMode = resolutionMode;
    this._expectCameraFps = Math.min(Math.max(fps, 5), 30);  // FPS取值范围限制在[5, 30]
    this._baseBitrateLevel = baseBitrateLevel;
    this._highBitrateLevel = highBitrateLevel;
    this._autoReduceResolution = autoReduceResolution;
    this._updateLocalVideoEncodeParams();
  }

  /**
   * 初始化(用于初始化实例开启监听)
   */
  public init() {
    // TODO:
  }

  /**
   * 设置用户的视频编码清晰度
   * @param {string} userId 要设置的用户ID
   * @param {boolean} highDefinition 是否启用高清晰度模式，启用高清晰度模式后，视频推流码率会增加一个级别
   *
   * 针对当前用户，影响视频推流码率
   * 针对远端用户，影响Web端码率估算精准度
   */
  public setVideoEncodeQuality(userId: string, highDefinition: boolean) {
    this._userHighDefinition.set(userId, highDefinition);
    if (userId === TSession.instance.getUserId()) {
      this._updateLocalVideoEncodeParams();
    }
  }

  /**
   * 获取用户的视频比特率级别
   * @param {string} userId 要获取的用户ID
   * @return {number} 视频比特率级别
   */
  public getVideoBitrateLevel(userId: string) {
    // 高清模式下码率使用不同级别
    if (this._userHighDefinition.get(userId)) {
      return this._highBitrateLevel;
    }
    return this._baseBitrateLevel;
  }

  /**
   * 是否屏蔽所有远端音频
   * @param {boolean} mute 是否屏蔽
   */
  public muteAllRemoteAudio(mute: boolean) {
    this._info('muteAllRemoteAudio', `${mute}: ${Array.from(this._remoteAVInfos.keys()).join(',')}`);
    this.isMuteAllRemoteAudio = mute;
    this._remoteAVInfos.forEach((remoteAVInfo) => {
      remoteAVInfo.updateAVState();
    });
  }

  /**
   * 禁言白板机器人的音频流
   */

  public muteTicPusherUser() {
    this._remoteAVInfos.forEach((remoteAVInfo) => {
      const isTicPushUser = remoteAVInfo.userId.startsWith('tic_push_user');
      // 如果是白板推流机器人,一定要mute 掉音频，否则会有重音出现
      if (isTicPushUser) {
        TTrtc.instance.muteRemoteAudio(remoteAVInfo.userId, true);
      }
    });
  }

  /**
   * 绑定远端视频画面DOM节点(有流则自动渲染)
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param fullMode    是否使用裁剪模式渲染
   * @return {number}   返回绑定序列
   */
  public bindRemoteVideoDom(userId: string, type: TTrtcVideoStreamType, dom: HTMLElement, fullMode = true) {
    if (!userId) {
      return;
    }
    const remoteAVInfo = this._getRemoteAVInfo(userId);
    let seq;
    let videoDomUpdated = false;
    let screenDomUpdated = false;
    let url = '';
    if (type === TTrtcVideoStreamType.Big || type === TTrtcVideoStreamType.QLive) {
      seq = remoteAVInfo.genNewSequence();
      remoteAVInfo.videoDom = dom;
      remoteAVInfo.mainFullMode = fullMode;
      remoteAVInfo.mainLiveType = type;
      remoteAVInfo.adjustMainLiveUrl();
      videoDomUpdated = true;
      url = remoteAVInfo.mainLiveUrl;
    } else if (type === TTrtcVideoStreamType.Sub) {
      seq = remoteAVInfo.genNewSubSequence();
      remoteAVInfo.screenDom = dom;
      remoteAVInfo.subFullMode = fullMode;
      screenDomUpdated = true;
      url = remoteAVInfo.subLiveUrl;
    }
    this._info('bindRemoteVideoDom', `userId: ${userId}, type: ${type}, dom: ${dom}, fullMode: ${fullMode}, seq: ${seq}, url: ${url}`);
    if (videoDomUpdated || screenDomUpdated) {
      remoteAVInfo.updateAVState(videoDomUpdated, screenDomUpdated);
    }
    return seq;
  }

  /**
   * 解绑远端视频画面DOM节点
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param sequence    要移除的绑定序列
   */
  public unbindRemoteVideoDom(userId: string, type: TTrtcVideoStreamType, sequence: number) {
    if (!userId) {
      return;
    }
    this._info('unbindRemoteVideoDom', `userId: ${userId} type: ${type} seq: ${sequence}`);
    const remoteAVInfo = this._getRemoteAVInfo(userId);
    if (type === TTrtcVideoStreamType.Big || type === TTrtcVideoStreamType.QLive) {
      if (remoteAVInfo.sequence !== sequence) {
        this._warn('unbindRemoteVideoDom', `ignore expire sequence: ${sequence} / ${remoteAVInfo.sequence}`);
        return;
      }
      remoteAVInfo.videoDom = null;
    } else if (type === TTrtcVideoStreamType.Sub) {
      if (remoteAVInfo.subSequence !== sequence) {
        this._warn('unbindRemoteVideoDom', `ignore expire sequence: ${sequence} / ${remoteAVInfo.subSequence}`);
        return;
      }
      remoteAVInfo.screenDom = null;
    }

    remoteAVInfo.audioRendering = undefined;

    remoteAVInfo.updateAVState();
  }

  /**
   * 重置视频渲染
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  public resetVideoRender(userId: string, type: TTrtcVideoStreamType) {
    this._info('resetVideoRender', 'do nothing');
  }

  /**
   * 启用自适应调整视频参数功能
   */
  public enableVideoParamsAutoAdjust() {
    TEvent.instance.on(TTrtcEvent.Network_Statistis, (statistics: TTrtcStatistics) => {
      // 滑窗算法取平均值
      this._recentlyUpVideoPixelsArray.push(statistics.upVideoPixels);
      this._recentlyDownVideoPixelsArray.push(statistics.downVideoPixels);
      if (this._recentlyUpVideoPixelsArray.length > 15) {  // 只保留最近15个采样点
        this._recentlyUpVideoPixelsArray.shift();
        this._recentlyDownVideoPixelsArray.shift();
      }
      let totalUpVideoPixels = 0;
      this._recentlyUpVideoPixelsArray.forEach((bitrate) => {
        totalUpVideoPixels += bitrate;
      });
      let totalDownVideoPixels = 0;
      this._recentlyDownVideoPixelsArray.forEach((bitrate) => {
        totalDownVideoPixels += bitrate;
      });
      this._averageUpVideoPixels = totalUpVideoPixels / this._recentlyUpVideoPixelsArray.length;
      this._averageDownVideoPixels = totalDownVideoPixels / this._recentlyDownVideoPixelsArray.length;
      this._updateLocalVideoEncodeParams();
    });
  }

  public resetLocalVideoEncodeParams() {
    this._updateLocalVideoEncodeParams(true);
  }


  public updateRemoteAVInfo(userId: string) {
    if (!userId) {
      return;
    }
    const remoteAVInfo = this._getRemoteAVInfo(userId);
    remoteAVInfo.updateAVState();
  }

  // 获取类名
  protected _getClassName() {
    return 'TAV';
  }

  /**
   * 获取远端音视频信息
   * @param {string} userId   远端用户ID
   * @return {TRemoteAVInfo}  返回的远端音视频信息
   * @private
   */
  private _getRemoteAVInfo(userId: string): TRemoteAVInfo {
    if (!this._remoteAVInfos.has(userId)) {
      this._remoteAVInfos.set(userId, new TRemoteAVInfo(userId, this));
    }
    return this._remoteAVInfos.get(userId);
  }

  private _updateLocalVideoEncodeParams(force = false) {
    const bitrateLevel = this.getVideoBitrateLevel(TSession.instance.getUserId());
    console.log('setVideoEncoderParam:: getVideoResolutionFromCameraResolution', this._autoReduceResolution);
    const resolution = TTrtc.getVideoResolutionFromCameraResolution(
      this._expectCameraResolution,
      {
        autoReduce: this._autoReduceResolution,
      },
    );
    const resolutionSize = TTrtc.getVideoSizeFromResolution(resolution, this._resolutionMode);
    const resolutionPixels = resolutionSize.width * resolutionSize.height;
    // 计算编解码合计像素数，视频编码相比视频解码的复杂度高5~10倍，这里按5倍算
    const codecPixels = this._averageUpVideoPixels * 5 + this._averageDownVideoPixels;
    // 1路上行+1路下行时，保持预期帧率，1路上行+N路下行时，帧率降到5帧，N取TAV.lowFpsRemoteVideoCounts内保存的值
    const lowFpsVideoCountsIndex = Math.min(this._expectCameraResolution, TAV.lowFpsRemoteVideoCounts.length - 1);
    const remoteVideoCount = TAV.lowFpsRemoteVideoCounts[lowFpsVideoCountsIndex];
    const minPixels = resolutionPixels * (5 + 1);
    const maxPixels = resolutionPixels * (5 + remoteVideoCount);
    const subPixels = maxPixels - minPixels;
    const fpsReduceFactor = Math.min(Math.max(codecPixels - minPixels, 0) / subPixels, 1);
    const fps = this._expectCameraFps - Math.floor((this._expectCameraFps - 5) * fpsReduceFactor);
    const bitrate = TTrtc.getVideoBitrate(resolution, fps, bitrateLevel);
    if (force
      || this.lastVideoResolution !== resolution
      || this.lastVideoFps !== fps
      || this.lastVideoBitrate !== bitrate) {
      this.lastVideoResolution = resolution;
      this.lastVideoFps = fps;
      this.lastVideoBitrate = bitrate;
      this._info('setVideoEncoderParam', JSON.stringify({
        resolution: TTrtcUtil.getVideoSizeFromResolution(resolution, this._resolutionMode),
        fps,
        bitrate,
        bitrateLevel,
        baseBitrateLevel: this._baseBitrateLevel,
        highBitrateLevel: this._highBitrateLevel,
        expectFps: this._expectCameraFps,
        upPixels: this._averageUpVideoPixels,
        downPixels: this._averageDownVideoPixels,
        codecPixels,
        expectResolution: this._expectCameraResolution,
      }));
      TTrtc.instance.setVideoEncoderParam(resolution, fps, bitrate, this._resolutionMode).then();
    }
  }
}
