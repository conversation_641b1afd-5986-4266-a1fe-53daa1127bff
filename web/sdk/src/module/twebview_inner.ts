import { TClassStatus } from './business/tbusiness_class';
import {
  TLogger,
} from './tlogger';
import { TSession } from './tsession';

/**
 * WebView专用接口模块
 */
export class TWebView {
  private static _instance: any = null;

  private static _errorMap = new Map();

  private static _retryTimes = new Map();

  private static _maxRetryTimes = 3;

  private static _getLogMsg(action: string, message: string) {
    return {
      module: 'TWebView',
      action,
      param: message,
    };
  }

  private _seqId = 1;
  private _log: TLogger;
  private _clientInfo: any = {};

  // 构建
  constructor() {
    this._log = TLogger.getInstance('TCIC');
    this._log.mergeInfoLog('TWebView', 'trtc@classHeartBeat', 15);
    this._log.mergeInfoLog('TWebView', 'trtc_classHeartBeat', 15);
    // this._log.mergeInfoLog('TWebView', 'trtc@getTRTCStats');
    // this._log.mergeInfoLog('TWebView', 'trtc_getTRTCStats');
  }

  // 获取实例
  public static get instance(): TWebView {
    if (TWebView._instance === null) {
      TWebView._instance = new TWebView();
    }
    return TWebView._instance;
  }

  // 注册全局回调
  public generateStaticCb(prefix: string, callback: (params: any) => void, report = true) {
    const functionName = `${prefix}_${new Date().getTime()
      .toString()}_${this._seqId}`;
    (window as any)[functionName] = (params: any) => {
      report && this._log.info(TWebView._getLogMsg(prefix, `callback: ${JSON.stringify(params)}`));
      callback(params);
    };
    return functionName;
  }

  /**
   * 调用Native接口
   * @param module 模块名称
   * @param action  接口名称
   * @param params  参数
   * @param callback  回调
   * @param noreport 来上报日志
   */
  public call(module: string, action: string, params?: any, callback?: (params: any) => void, noreport?: boolean) {
    interface Request {
      head: any,
      params: any,
      nativecb?: any
    }

    const callReq: Request = {
      head: {
        module,
        action,
        seqid: this._generateSeqId(),
      },
      params: {},
    };
    if (params) {
      callReq.params = params;
    }
    if (callback) {
      callReq.nativecb = this._generateFuncCallback(`${module}_${action}`, callback);
    }
    if (!noreport) {
      this._log.info(TWebView._getLogMsg(`${module}@${action}`, `call: ${JSON.stringify(callReq)}`));
      this._log.info(TWebView._getLogMsg(`CallNativeFunction`, `${module}@${action}@call: ${JSON.stringify(callReq)}`));
    }
    this._sendChannel(callReq);
  }

  // /**
  //  * 弹Native消息框
  //  * @param title  标题
  //  * @param message  消息
  //  * @param btnArr  按钮数组
  //  * @param callback  回调
  //  */
  // public showMessageBox(title: string, message: string, btnArr: string[], callback: (index:number)=>void){
  //     this.call("os", "openSystemAlert", {
  //         title: title,
  //         message: message,
  //         actions: btnArr
  //     }, (info: any)=>{
  //         if (info && info.retval){
  //             callback(btnArr.indexOf(info.retval.action));
  //         }else{
  //             callback(-1);
  //         }
  //     })
  // }

  /**
   * 退出(移动端专用)
   */
  public closeWebView() {
    if (TSession.instance.isMobileNative()) {
      TWebView.instance.call('trtc', 'uninitSDK');
      TWebView.instance.call('os', 'closeWebView');
    }
  }

  /**
   * 显示弹窗
   * @param url 网页地址
   * @param rect  位置信息
   * @param callback 回调
   */
  public showSubWindow(url: string, rect: DOMRect, callback: (code: number, data: any) => void) {
    this.call('os', 'openSubWebView', {
      webId: 'subWindow',
      url,
      x: Math.floor(rect.x * window.devicePixelRatio),
      y: Math.floor(rect.y * window.devicePixelRatio),
      width: Math.floor(rect.width * window.devicePixelRatio),
      height: Math.floor(rect.height * window.devicePixelRatio),
      webConfig: {
        os: true,
      },
    }, (info: any) => {
      if (callback) {
        if (info && info.retval) {
          callback(0, info.retval);
        } else {
          callback(-1, undefined);
        }
      }
    });
  }

  /**
   * 关闭弹窗自己(由弹窗调用)
   */
  public closeWebViewSelf() {
    this.call('os', 'subWebViewRemoveSelf');
  }

  /**
   * 发消息到webview
   * @param event 事件名称
   * @param message 消息内容
   * @param webid webview标识
   * @param fromWebId 源webviewId
   */
  public sendMsgToWebView(event: string, message: any, webid: string, fromWebId: string) {
    this.call('os', 'sendMsgToWebView', {
      webId: webid,
      fromWebId,
      sendMsg: {
        event,
        msg: message,
      },
    });
  }

  /**
   * 注册webview消息回调
   * @param callback 回调方法
   * 此方法robinwwang说已废弃, 用 registerNativeRecv 代替.
   */
  public registerMsgRecv(callback: (event: string, message: any) => void) {
    this.call('os', 'registerMsgRecvFunc', {
      msgRecvFunc: this.generateStaticCb('registMsgRecvFunc', (info: any) => {
        try {
          // let data = info//JSON.parse(info);
          callback(info.sendMsg.event, info.sendMsg.msg);
        } catch (err) {
          this._log.warn({
            module: 'TWebView',
            action: 'registerMsgRecv',
            param: err.message,
          });
        }
      }),
    });
  }

  /**
   * 发消息到native
   * @param event 事件名称
   * @param message 消息内容
   */
  public sendCustomMessage(message: any) {
    this.call('custom', 'sendCustomMsg', {
      customId: 'custom',
      customMsg: message,
    });
  }

  /**
   * 注册native消息回调
   * @param callback 回调方法
   */
  public registerNativeRecv(callback: (event: any) => void) {
    this.call('custom', 'registerCustomRecvCb', {
      customId: 'custom',
      recvCallBack: this.generateStaticCb('registerCustomRecvCb', (info: any) => {
        try {
          // let data = info//JSON.parse(info);
          callback(info);
        } catch (err) {
          this._log.warn({
            module: 'TWebView',
            action: 'registerCustomRecvCb',
            param: err.message,
          });
        }
      }),
    });
  }

  /**
   * 注册软键盘监听事件
   * @param callback 回调方法
   */
  public registerKeyBoardEvent(callback: (event: any) => void) {
    this.call('os', 'registerKeyBoardEventFunc', {
      kbEventRecvFunc: this.generateStaticCb('registerKeyBoardEventFunc', (info: any) => {
        callback(info);
      }),
    });
  }

  // 更新设备信息
  public setClientInfo(info: any) {
    this._clientInfo = info;
  }
  // 获取设备信息
  public getClientInfo() {
    return this._clientInfo;
  }
  // 检测是否在TV白名单
  public inTVList() {
    return ['nb6797_6c_m'].includes(this._clientInfo.osDevName);
  }

  // 生成序列号
  private _generateSeqId() {
    return this._seqId += 1;
  }

  // 生成临时全局回调
  private _generateFuncCallback(prefix: string, callback: (params: any) => void) {
    const functionName = `${prefix}_${new Date().getTime()
      .toString()}_${this._seqId}`;
    (window as any)[functionName] = (params: any) => {
      this._log.info(TWebView._getLogMsg(prefix, `callback: ${JSON.stringify(params)}`));
      this._log.info(TWebView._getLogMsg('NativeFunctionCallBack', `${prefix}_callback: ${JSON.stringify(params)}`));
      callback(params);
      delete (window as any)[functionName];
    };
    return functionName;
  }
  private _getUniqueKeyFromData(data: any): string {
    return `${data['module']}_${data['action']}_${data['seqid']}`;
  }
  private _retry(key: string) {
    const data = TWebView._errorMap.get(key);
    let retryTime = TWebView._retryTimes.get(key) ?? 0;
    if (data) {
      if (retryTime <= TWebView._maxRetryTimes) {
        TWebView._errorMap.delete(key);
        retryTime = retryTime + 1;
        TWebView._retryTimes.set(key, retryTime);
        const timmer = setTimeout(() => {
          this._sendChannel(data);
          clearTimeout(timmer);
        }, 300);
      }
      this._log.info(TWebView._getLogMsg('_sendChannel', `retry ios data ${JSON.stringify(data)} retryTime: ${retryTime}`));
    } else {
      this._log.info(TWebView._getLogMsg('_sendChannel', `retry ios data is null`));
    }
  }
  // 发送消息
  private _sendChannel(data: any) {
    if (TSession.instance.isIOSNative()) {
      if ((window as any).webkit && (window as any).webkit.messageHandlers) {
        (window as any).webkit.messageHandlers.sendChannel.postMessage(data);
      } else {
        const status = TSession.instance.getClassInfo().status;
        if (status !== TClassStatus.Has_Ended) {
          this._log.warn(TWebView._getLogMsg('_sendChannel', `send ios channel fail. class status ${status}`));
          const key = this._getUniqueKeyFromData(data['head']);
          if (key) {
            TWebView._errorMap.set(key, data);
            this._retry(key);
          };
          this._log.info(TWebView._getLogMsg('_sendChannel', `retry ios action ${key}`));
        }
      }
    } else if (TSession.instance.isAndroidNative()) {
      // trtc的相关Bridge移除
      if (!TSession.instance.isX5Webview() && TSession.instance.isInFaithOrDemo()) {
        if (data.head.module === 'trtc') {
          this._log.info(TWebView._getLogMsg('_sendChannel', `is not x5 webview ignore ${data.head.module}-${data.head.action}`));
          return;
        }
      }
      if ((window as any).container && (window as any).container.sendChannel) {
        (window as any).container.sendChannel(JSON.stringify(data));
      } else {
        const status = TSession.instance.getClassInfo().status;
        if (status !== TClassStatus.Has_Ended) {
          this._log.warn(TWebView._getLogMsg('_sendChannel', `send android channel fail. class status ${status}`));
          const key = this._getUniqueKeyFromData(data['head']);
          if (key) {
            TWebView._errorMap.set(key, data);
            this._retry(key);
          };
          this._log.info(TWebView._getLogMsg('_sendChannel', `retry android action ${key}`));
        }
      }
    } else {
      // this._log.warn(this._getLogMsg("sendChannel", `send ${TSession.instance.getPlatform()} channel fail`));
    }
  }
}
