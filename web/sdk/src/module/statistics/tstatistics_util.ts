import { TSession } from '../tsession';
import { TStatisticsBase } from './tstatistics_base';
import { TStatisticsMobile } from './tstatistics_mobile';
import { TStatisticsElectron } from './tstatistics_electron';
import { TStatisticsWeb } from './tstatistics_web';

export class TStatisticsUtil {
  private static _instance: TStatisticsBase = null;

  /**
     * 获取实例
     */
  public static getInstance(): TStatisticsBase {
    if (!TStatisticsUtil._instance) {
      if (TSession.instance.isIOSNative() || (TSession.instance.isAndroidNative())) {
        TStatisticsUtil._instance = new TStatisticsMobile();
      } else if (TSession.instance.isElectron()) {
        TStatisticsUtil._instance = new TStatisticsElectron();
      } else {
        TStatisticsUtil._instance = new TStatisticsWeb();
      }
    }
    return TStatisticsUtil._instance;
  }

  /**
     * 返回该类实例
     */
  public static get instance(): TStatisticsBase {
    return this.getInstance();
  }
}
