import { TClassLayout } from '../../constants';
import { TModel } from '../../base/tmodel';
import { TBusiness } from './tbusiness';
import { TModule } from '../../base/tmodule';
import { TUtil } from '../tutil_inner';

/**
 * 课堂发言模式
 * @enum {number}
 * @property {0} Free_Chat 自由聊天
 * @property {1} Public_Only 仅允许公开聊天
 * @property {2} Private_Only 仅允许私聊
 * @property {3} All_Mute 全员禁言
 */
export enum TClassSilenceMode {

  Free_Chat = 0,

  Public_Only = 1,

  Private_Only = 2,

  All_Mute = 3,
}

/**
 * 课堂状态
 * @enum {number}
* @property {0} Not_Start 未开始
* @property {1} Already_Start 已经开始
* @property {2} Has_Ended 已经结束
* @property {3} Has_Expired 已过期
 */
export enum TClassStatus {

  Not_Start = 0,

  Already_Start = 1,

  Has_Ended = 2,

  Has_Expired = 3,
}

/**
 * 课堂类型
 * @enum {number}
 */
export enum TClassType {
  /**
   * 互动课
   */
  Interactive = 0,
  /**
   * 公开课
   */
  Live = 1,
  /**
   * 统一课堂
   */
  United = 2,
}

/**
 * 课堂子类型
 * @enum {String}
 */
export enum TClassSubType {
  /**
   * 未知课程
   */
  UnKnow = '',

  /**
   * 互动课
   */
  Interactive = 'interactive',

  /**
   * 公开课
   */
  Live = 'live',

  /**
   * 大教学
   */
  College = 'college',

  /**
   * 双师
   */
  CoTeaching = 'coteaching',

  /**
   * 1v1
   */
  OneOnOne = 'oneonone',

  /**
   * 视频+文档
   */
  VideoDoc = 'videodoc',

  /**
   * 纯视频
   */
  Video = 'video',

  /**
   * 纯音频
   */
  AudioOnly ='audioOnly'
}

/**
 * @enum {number}
* @property {1} Portrait 竖屏
* @property {0} Landscape 横屏
 *
 */
export enum VideoOrientation {
  Portrait = 1,
  Landscape = 0,
}

/**
 * 观看类型
 * @enum {number}
* @property {0} Unknown 未知
* @property {1} RTC 互动观看
* @property {2} CDN CDN观看
 */
export enum TAudienceType {
  Unknown = 0,
  RTC = 1,
  CDN = 2,
}

/**
 * 观看混流类型
 * @enum {number}
 * @property {0} Normal 单流
 * @property {1} Mix 混流
 */
export enum TAudienceMixType {
  Normal = 0,
  Mix = 1,
}

/**
 * 互动模式
 * @enum {number}
 * @property {0} Normal 观看所有参与者的音视频
 * @property {1} Fake_1v1 仅看老师和助教
 */
export enum TInteractionMode {
  Normal = 0,
  Fake_1v1 = 1,
}

/**
 * 班型，小班课/大班课/……
 * @enum {number}
 */
export enum TRoomType {
  /**
   * 小班课
   */
  Small = 0,
  /**
   * 大班课
   */
  Big = 1,
}

/**
 * 检查某个值是否为对象或enum 的有效值
 * @returns boolean
 */
export const CheckValueInEnum = (enumObject: any, value: string | number | boolean): boolean => {
  let resultStatus = false;
  Object.keys(enumObject).forEach((key) => {
    if (enumObject[key] === value) {
      resultStatus = true;
    }
  });
  return resultStatus;
};
/**
 * 摄像头分辨率
 * @enum {number}
 * @property {number} Resolution_320_180 320x180
 * @property {number} Resolution_640_360 640x360
 * @property {number} Resolution_960_540 960x540
 * @property {number} Resolution_1280_720 1280x720
 * @property {number} Resolution_1920_1080 1920x1080
 */
export enum TCameraResolution {
  Resolution_320_180 = 0,
  Resolution_640_360 = 1,
  Resolution_960_540 = 2,
  Resolution_1280_720 = 3,
  Resolution_1920_1080 = 4,
  Resolution_480_480 = 5,
  // !!! 注意：增加分辨率类型必须对应修改TAV.lowFpsRemoteVideoCounts字段的值
}

export class TGetClassListParam extends TModel {
  public teacherId = '';
  public schoolId = 0;
  public page = 1;
  public limit = 0;
  public classroomCode: number[] = [];
  public classTypes: number[] = [0, 1, 2];
  public classStatus: number[] = [0, 1, 2];

  /**
   * @ignore
   */
  public deserialize(json: any): void {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public serialize(): any {
    throw new Error('Method not implemented.');
  }
}

/**
 * 课堂信息
 * @param {number} classId 课堂 ID
 * @param {number} classType 课堂类型
 * @param {string} className 课堂名称
 * @param {number} startTime 课堂开始时间
 * @param {number} endTime 课堂结束时间
 * @param {string} teacherId 老师 ID
 * @param {string} schoolId 学校 ID
 * @param {number} realStartTime 课堂实际开始时间
 * @param {number} realEndTime 课堂实际结束时间
 * @param {string} replayUrl 回放地址
 * @param {string} chatGroupId 聊天群组
 * @param {string} cmdGroupId 信令群组
 * @param {TClassStatus} status 课堂状态
 * @param {string} classroomCode 授课范围 ID
 */
export class TClassListInfo extends TModel {
  public classId = 0;
  public classType = 0;
  public className = '';
  public startTime = 0;
  public endTime = 0;
  public teacherId = '';
  public schoolId = 0;
  public realStartTime = 0;
  public realEndTime = 0;
  public replayUrl = '';
  public chatGroupId = '';
  public cmdGroupId = '';
  public status: TClassStatus = TClassStatus.Not_Start;
  public classroomCode = '';
  public liveUrl = '';

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.classId = json.class_id;
    this.classType = json.class_type;
    this.className = json.name;
    this.startTime = json.start_time;
    this.endTime = json.end_time;
    this.teacherId = json.teacher_id;
    this.schoolId = json.school_id;
    this.realStartTime = json.real_start_time;
    this.realEndTime = json.real_end_time;
    this.replayUrl = json.replay_url;
    this.chatGroupId = json.chat_group;
    this.cmdGroupId = json.cmd_group;
    this.status = json.status;
    this.classroomCode = json.classroom_code;
    this.liveUrl = json.live_url;
  }
}

export class TClassHistroyMessage extends TModel {
  public message = '';
  /**
   * @ignore
   */
  public deserialize(json: any): void {
    this.message = json.messages;
  }

  /**
   * @ignore
   */
  public serialize(): any {
    throw new Error('Method not implemented.');
  }
}

/**
 * 跑马灯信息
 */
export class TClassMarquee extends TModel {
  /* 课堂ID */
  public classId: number = null;

  /* 跑马灯类型：
  1为固定值，
  2为用户昵称，
  3为固定值+用户昵称，
  4为用户ID，
  5为用户ID+固定值
  6为用户昵称（用户ID）
 */
  public marqueeType: number = null;

  /* 固定值内容（如选择了含固定值的类型） */
  public content: string = null;

  /* 字体大小（数字，像素单位） */
  public fontSize: number = null;

  /* 字体粗细：1为粗体，0为细体 */
  public fontWeight: number = null;

  /* 字体颜色（十六进制颜色值） */
  public fontColor: string = null;

  /* 字体透明度（数字，范围 0.0 到 1.0） */
  public fontOpacity: number = null;

  /* 背景颜色（十六进制颜色值） */
  public backgroundColor: string = null;

  /* 背景透明度（数字，范围 0.0 到 1.0） */
  public backgroundOpacity: number = null;

  /* 显示方式：1为滚动，2为闪烁 */
  public displayMode: number = null;

  /*
  跑马灯文字移动/闪烁指定像素所需时间
  范围：1-10；数值越小，跑马灯滚动/闪烁速度越快
    */
  public duration: number = null;

  /* 跑马灯个数：目前仅支持1或2, 单排或双排 */
  public marqueeCount: number = null;

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.classId = json.ClassId;
    this.fontSize = json.FontSize;
    this.marqueeType = json.MarqueeType;
    this.content = json.Content;
    this.fontColor = json.FontColor;
    this.fontWeight = json.FontWeight;
    this.fontOpacity = json.FontOpacity;
    this.backgroundColor = json.BackgroundColor;
    this.backgroundOpacity = json.BackgroundOpacity;
    this.displayMode = json.DisplayMode;
    this.duration = json.Duration;
    this.marqueeCount = json.MarqueeCount;
  }
}

/**
 * 课堂信息
 */
export class TClassInfo extends TModel {
  /**
   * 学校 ID
   */
  public schoolId = 0;
  /**
   * 课堂 ID
   */
  public classId = 0;
  /**
   * 课堂类型
   */
  public classType = 0;
  /**
   * 课堂名称
   */
  public className = '';
  /**
   * 课堂开始时间
   */
  public startTime = 0;
  /**
   * 拖堂时间
   */
  public  endDelayTime = 0;
  /**
   * 课堂结束时间
   */
  public endTime = 0;
  /**
   * 老师 ID
   */
  public teacherId = '';
  /**
   * 助教ID数组
   */
  public assistants: string[] = [];
  /**
   * 课堂实际开始时间
   */
  public realStartTime = 0;
  /**
   * 课堂实际结束时间
   */
  public realEndTime = 0;
  /**
   * 回放地址
   */
  public replayUrl = '';
  /**
   * 回放布局
   */
  public replayLayout = 0;
  /**
   * 聊天群组ID
   */
  public chatGroupId = '';
  /**
   * 信令群组ID
   */
  public cmdGroupId = '';
  /**
   * 课堂状态
   */
  public status = TClassStatus.Not_Start;
  /**
   * 横竖屏课堂
   */
  public videoOrientation = VideoOrientation.Landscape;
  /**
   * 授课范围 ID
   */
  public classroomCode = '';
  /**
   * 摄像头分辨率
   */
  public cameraResolution = TCameraResolution.Resolution_1280_720;
  /**
   * 摄像头 FPS
   */
  public cameraFPS = 0;
  /**
   * 摄像头码率
   */
  public cameraBitrate = 0;
  /**
   * 自动上麦, 0 不自动连麦, 1自动连麦
   */
  public autoOpenMic = 0;
  /**
   * 是否允许访客进房
   */
  public forbidVisitor = 0;
  /**
   * 最大人数限制
   */
  public maxMember = 0;
  /**
   * 最大上麦人数限制，不包括老师
   */
  public maxRtcMember = 4;
  /**
   * 是否纯音频课程
   */
  public micOnly = 0;
  /**
   * 白板比例
   */
  public boardRatio = '';
  /**
   * 是否开启全体静音
   */
  public muteAll = 0;
  /**
   * 是否开启全体视频
   */
  public muteVideoAll = 0;
  /**
   * 是否开启全体禁言
   */
  public silenceAll = 0;
  /**
   * 全员发言状态
   */
  public silenceMode = 0;
  /**
   * 是否开启连麦
   */
  public enableStage = 0;
  /**
   * 直播地址
   */
  public liveUrl = '';
  /**
   * 备用播放地址
   */
  public backupPlayUrl = '';
  /**
   * 释放音视频权限后是否自动下台
   */
  public offstageMicControl = 0;
  /**
   * 禁止自动补位
   */
  public forbidFillInMic = 0;
  /**
   * 课堂子类型
   */
  public classSubType = TClassSubType.UnKnow;
  /**
   * 自定义信息
   */
  public customData: { [key: string]: any } = {};
  /**
   * 最新的任务序列号
   */
  public lastTaskSeq = 0;
  /**
   * 课堂任务列表
   */
  public tasks: TTaskInfo[] = [];
  /**
   * 课堂信息更新时间
   */
  public updateAt = 0;
  /**
   * 禁用录制
   */
  public disableRecord = 0;
  /**
   * 高清音质
   */
  public audioQuality = 0;

  /**
   * 封面
   */
  public cover = '';

  /**
   * 分享封面
   */
  public shareCover = '';
  /**
   * 观看类型
   */
  public audienceType = TAudienceType.RTC;
  /**
   * 课中布局(统一课堂)
   */
  public layout = TClassLayout.Top;
  /**
   * 聊天记录地址
   */
  public messageRecordUrl = '';
  public enableDirectControl = 0;
  /**
   * 跑马灯参数
   */
  public marquee = '';

  // 开启专注模式。0 收看全部角色音视频(默认)；1 只看老师和助教
  public interactionMode = TInteractionMode.Normal;
  // 是否开启了课后评价
  public gradingAfterClass = 0;
  public whiteBoardSnapshotMode = 0;

  public liveType = 0;


  /**
   * 班型
   */
  public roomType = TRoomType.Small;

  /**
   * 观看混流类型
   */
  public audienceMixType = TAudienceMixType.Normal;

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    const classInfo = json.class_info;
    this.classId = classInfo.class_id;
    this.classType = classInfo.class_type;
    this.classSubType = classInfo.class_sub_type;
    this.marquee = classInfo.room_info.marquee_param;
    this.endDelayTime = classInfo.room_info.end_delay_time;
    if (classInfo.custom_data) {
      try {
        this.customData = JSON.parse(classInfo.custom_data);
      } catch (err) {
        console.error(err);
      }
    }
    if (classInfo.class_type === TClassType.Interactive) {
      const rtcInfo = classInfo.rtc_class_info;
      this.className = rtcInfo.name;
      this.startTime = rtcInfo.start_time;
      this.endTime = rtcInfo.end_time;
      this.teacherId = rtcInfo.teacher_id;
      this.schoolId = rtcInfo.school_id;
      this.realStartTime = rtcInfo.real_start_time;
      this.realEndTime = rtcInfo.real_end_time;
      this.replayUrl = rtcInfo.replay_url;
      this.chatGroupId = rtcInfo.chat_group;
      this.cmdGroupId = rtcInfo.cmd_group;
      this.status = rtcInfo.status;
      this.classroomCode = rtcInfo.classroom_code;
      this.cameraResolution = rtcInfo.camera_resolution;
      this.cameraFPS = rtcInfo.camera_fps;
      this.cameraBitrate = rtcInfo.camera_bitrate;
      this.silenceAll = rtcInfo.silence_all;
      this.silenceMode = rtcInfo.silence_mode;
      this.enableStage = rtcInfo.enable_stage;
      this.assistants = [rtcInfo.assistant_id];
      this.autoOpenMic = rtcInfo.auto_open_mic;
      this.forbidVisitor = rtcInfo.forbid_visitor;
      this.maxMember = rtcInfo.max_member;
      this.maxRtcMember = rtcInfo.max_rtc_member;
      this.micOnly = rtcInfo.mic_only;
      this.replayLayout = rtcInfo.replay_layout;
      this.boardRatio = rtcInfo.board_ratio;
      this.muteAll = rtcInfo.mute_all;
      this.muteVideoAll = rtcInfo.mute_video_all;
      this.lastTaskSeq = rtcInfo.last_task_seq;
      this.liveUrl = rtcInfo.live_url;
      this.backupPlayUrl = rtcInfo.backup_play_url;
      this.offstageMicControl = rtcInfo.offstage_mic_control;
      this.forbidFillInMic = rtcInfo.forbid_fill_in_mic;
      this.updateAt = rtcInfo.updated_at;
      this.audioQuality = rtcInfo.audio_quality;

      rtcInfo.tasks.forEach((item: any) => {
        const info = new TTaskInfo();
        info.deserialize({ task: item });
        this.tasks.push(info);
      });
    } else if (classInfo.class_type === TClassType.Live) {
      const liveInfo = classInfo.live_class_info;
      this.className = liveInfo.name;
      this.startTime = liveInfo.start_time;
      this.endTime = liveInfo.end_time;
      this.teacherId = liveInfo.teacher_id;
      this.schoolId = liveInfo.school_id;
      this.realStartTime = liveInfo.real_start_time;
      this.realEndTime = liveInfo.real_end_time;
      this.replayUrl = liveInfo.replay_url;
      this.chatGroupId = liveInfo.chat_group;
      this.cmdGroupId = liveInfo.cmd_group;
      this.status = liveInfo.status;
      this.classroomCode = liveInfo.classroom_code;
      // 公开课有且仅有分辨率
      this.cameraResolution = liveInfo.camera_resolution;
      // this.cameraFPS = liveInfo.camera_fps;
      // this.cameraBitrate = liveInfo.camera_bitrate;
      this.silenceAll = liveInfo.silence_all;
      this.silenceMode = liveInfo.silence_mode;
      this.enableStage = liveInfo.enable_stage;
      this.assistants = [liveInfo.assistant_id];
      // this.autoOpenMic = rtcInfo.auto_open_mic;
      this.forbidVisitor = liveInfo.forbid_visitor;
      // this.maxMember = rtcInfo.max_member;
      // this.maxRtcMember = rtcInfo.max_rtc_member;
      // this.micOnly = rtcInfo.mic_only;
      // this.replayLayout = rtcInfo.replay_layout;
      // this.boardRatio = rtcInfo.board_ratio;
      this.muteAll = liveInfo.mute_all;
      this.muteVideoAll = liveInfo.mute_video_all;
      this.lastTaskSeq = liveInfo.last_task_seq;
      this.liveUrl = liveInfo.live_url;
      // his.backupPlayUrl = rtcInfo.backup_play_url;
      // this.offstageMicControl = rtcInfo.offstage_mic_control;
      // this.forbidFillInMic = rtcInfo.forbid_fill_in_mic;
      this.updateAt = liveInfo.updated_at;
      this.disableRecord = liveInfo.disable_record;
      this.audioQuality = liveInfo.audio_quality;
      this.cover = liveInfo.cover;
      this.shareCover = liveInfo.share_cover;
      liveInfo.tasks.forEach((item: any) => {
        const info = new TTaskInfo();
        info.deserialize({ task: item });
        this.tasks.push(info);
      });
    } else if (classInfo.class_type === TClassType.United) {
      const roomInfo = classInfo.room_info;
      const qualitySet = classInfo.quality_set;
      this.className = roomInfo.name;
      this.startTime = roomInfo.start_time;
      this.endTime = roomInfo.end_time;
      this.teacherId = roomInfo.teacher_id;
      this.schoolId = roomInfo.school_id;
      this.realStartTime = roomInfo.real_start_time;
      this.realEndTime = roomInfo.real_end_time;
      this.replayUrl = roomInfo.replay_url;
      this.chatGroupId = roomInfo.chat_group;
      this.cmdGroupId = roomInfo.cmd_group;
      this.videoOrientation = roomInfo.video_orientation;
      this.status = roomInfo.status;
      this.classroomCode = roomInfo.classroom_code;
      this.enableDirectControl = roomInfo.enable_direct_control;
      this.interactionMode = roomInfo.interaction_mode || TInteractionMode.Normal;
      this.gradingAfterClass = roomInfo.is_grading_required_post_class;
      this.roomType = roomInfo.room_type || TRoomType.Small;
      // mock 大班课混流
      if (this.roomType === TRoomType.Big) {
        const audienceMixType = parseInt(TUtil.getParamFromQuery('audiencemixtype', window.location.search), 10);
        if (audienceMixType === TAudienceMixType.Normal || audienceMixType === TAudienceMixType.Mix) {
          roomInfo.audience_mix_type = audienceMixType;
          console.warn(`mock audienceMixType ${audienceMixType}`);
        }
      }
      this.audienceMixType = roomInfo.audience_mix_type || TAudienceMixType.Normal;

      /**
       * 给所有课堂降分辨率和码率，
       * trtc_utils.ts 文件的 getVideoResolutionFromCameraResolution
       * 方法也要调整
       * @todo 待重构..多处转换分辨率也不知道是为什么.
       */
      const { cam_resolution, cam_bitrate } = qualitySet ?? {};
      this.cameraResolution = cam_resolution;
      this.cameraBitrate = cam_bitrate;
      this.cameraFPS = 15;
      this.audienceType = roomInfo.audience_type;
      this.silenceAll = roomInfo.silence_all;
      this.whiteBoardSnapshotMode = roomInfo.tiw_snapshot_mode;
      this.silenceMode = roomInfo.silence_mode;
      this.assistants = roomInfo.assistants;
      this.autoOpenMic = roomInfo.auto_open_mic;
      this.forbidVisitor = roomInfo.forbid_visitor;
      this.maxRtcMember = roomInfo.max_rtc_member;
      this.replayLayout = roomInfo.replay_layout;
      this.muteAll = roomInfo.mute_all;
      this.muteVideoAll = roomInfo.mute_video_all;
      this.lastTaskSeq = roomInfo.last_task_seq;
      this.liveUrl = roomInfo.live_url;
      this.liveType = roomInfo.live_type;
      this.backupPlayUrl = roomInfo.backup_play_url;
      this.offstageMicControl = roomInfo.offstage_mic_control;
      this.forbidFillInMic = roomInfo.forbid_fill_in_mic;
      this.updateAt = roomInfo.updated_at;
      this.audioQuality = roomInfo.audio_quality;
      const layoutKey = Object.keys(TClassLayout)
        .find(l => TClassLayout[l as keyof typeof TClassLayout] === roomInfo.layout) || TClassLayout.Top;
      this.layout = TClassLayout[layoutKey as keyof typeof TClassLayout];
      this.messageRecordUrl = roomInfo.message_record_url;
      this.disableRecord = roomInfo.disable_record;

      roomInfo.tasks.forEach((item: any) => {
        const info = new TTaskInfo();
        info.deserialize({ task: item });
        this.tasks.push(info);
      });
    }
  }

  public mergeInfo(info: any) {
    if (info.hasOwnProperty('name')) {
      this.className = info.name;
    }
    if (info.hasOwnProperty('start_time')) {
      this.startTime = info.start_time;
    }
    if (info.hasOwnProperty('end_time')) {
      this.endTime = info.end_time;
    }
    if (info.hasOwnProperty('teacher_id')) {
      this.teacherId = info.teacher_id;
    }
    if (info.hasOwnProperty('school_id')) {
      this.schoolId = info.school_id;
    }
    if (info.hasOwnProperty('real_start_time')) {
      this.realStartTime = info.real_start_time;
    }
    if (info.hasOwnProperty('real_end_time')) {
      this.realEndTime = info.real_end_time;
    }
    if (info.hasOwnProperty('replay_url')) {
      this.replayUrl = info.replay_url;
    }
    if (info.hasOwnProperty('chat_group')) {
      this.chatGroupId = info.chat_group;
    }
    if (info.hasOwnProperty('cmd_group')) {
      this.cmdGroupId = info.cmd_group;
    }
    if (info.hasOwnProperty('status')) {
      this.status = info.status;
    }
    if (info.hasOwnProperty('classroom_code')) {
      this.classroomCode = info.classroom_code;
    }
    if (info.hasOwnProperty('silence_all')) {
      this.silenceAll = info.silence_all;
    }
    if (info.hasOwnProperty('silence_mode')) {
      this.silenceMode = info.silence_mode;
    }
    if (info.hasOwnProperty('enable_stage')) {
      this.enableStage = info.enable_stage;
    }
    if (info.hasOwnProperty('forbid_visitor')) {
      this.forbidVisitor = info.forbid_visitor;
    }
    if (info.hasOwnProperty('mute_all')) {
      this.muteAll = info.mute_all;
    }
    if (info.hasOwnProperty('last_task_seq')) {
      this.lastTaskSeq = info.last_task_seq;
    }
    if (info.hasOwnProperty('live_url')) {
      this.liveUrl = info.live_url;
    }
    if (info.hasOwnProperty('layout')) {
      const layoutKey = Object.keys(TClassLayout)
        .find(l => TClassLayout[l as keyof typeof TClassLayout] === info.layout) || TClassLayout.Top;
      this.layout = TClassLayout[layoutKey as keyof typeof TClassLayout];
    }
    if (info.hasOwnProperty('updated_at')) {
      this.updateAt = info.updated_at;
    }
    if (info.hasOwnProperty('custom_data')) {
      if (info.custom_data) {
        try {
          this.customData = JSON.parse(info.custom_data);
        } catch (err) {
          console.error(err);
        }
      }
    }
    // if (info.hasOwnProperty('tasks')) {   // 更新任务信息，先忽略task更新，依赖task变更事件通知
    //   this.tasks = [];
    //   info.tasks.forEach((item: any) => {
    //     const info = new TTaskInfo();
    //     info.deserialize({ task: item });
    //     this.tasks.push(info);
    //   });
    // }
    if (this.classType === TClassType.Interactive) {    // 互动课
      if (info.hasOwnProperty('camera_resolution')) {
        this.cameraResolution = info.camera_resolution;
      }
      if (info.hasOwnProperty('camera_fps')) {
        this.cameraFPS = info.camera_fps;
      }
      if (info.hasOwnProperty('camera_bitrate')) {
        this.cameraBitrate = info.camera_bitrate;
      }
      if (info.hasOwnProperty('assistant_id')) {
        this.assistants = [info.assistant_id];
      }
      if (info.hasOwnProperty('assistants')) {
        this.assistants = info.assistants;
      }
      if (info.hasOwnProperty('auto_open_mic')) {
        this.autoOpenMic = info.auto_open_mic;
      }
      if (info.hasOwnProperty('max_member')) {
        this.maxMember = info.max_member;
      }
      if (info.hasOwnProperty('max_rtc_member')) {
        this.maxRtcMember = info.max_rtc_member;
      }
      if (info.hasOwnProperty('mic_only')) {
        this.micOnly = info.mic_only;
      }
      if (info.hasOwnProperty('replay_layout')) {
        this.replayLayout = info.replay_layout;
      }
      if (info.hasOwnProperty('board_ratio')) {
        this.boardRatio = info.board_ratio;
      }
      if (info.hasOwnProperty('backup_play_url')) {
        this.backupPlayUrl = info.backup_play_url;
      }
      if (info.hasOwnProperty('offstage_mic_control')) {
        this.offstageMicControl = info.offstage_mic_control;
      }
      if (info.hasOwnProperty('forbid_fill_in_mic')) {
        this.forbidFillInMic = info.forbid_fill_in_mic;
      }
    } else if (this.classType === TClassType.Live) {    // 公开课
      if (info.hasOwnProperty('disable_record')) {
        this.disableRecord = info.disable_record;
      }
    }
  }
}

/**
 * 获取用户列表结果
 * @param {TUserInfo[]} users 用户列表
 */
export class TGetClassListResult extends TModel {
  public total = 0;
  public classes: TClassListInfo[] = [];

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.total = json.total;
    json.classes.forEach((item: any) => {
      const info = new TClassListInfo();
      info.deserialize(item);
      this.classes.push(info);
    });
  }
}

/**
 * 任务信息
 * @param {number} seq          任务序列号，每次修改任务序列号都会更新，序列号单调递增，课堂内全局唯一
 * @param {string} taskId       任务ID，用于课堂内唯一索引一个任务
 * @param {number} status       任务状态，0表示已停止，1表示进行中
 * @param {string} content      任务内容
 * @param {number} createTime   任务创建的服务器时间，UNIX时间戳
 * @param {number} updateTime   任务更新的服务器时间，UNIX时间戳
 * @param {number} expireTime   任务结束的服务器时间，UNIX时间戳，具体值为设置duration的时间点加上duration。如果duration为-1，则本值为0
 * @param {string} bindingUser  任务绑定的用户ID，未绑定则为空字符串
 */
export class TTaskInfo extends TModel {
  public seq = 0;
  public taskId = '';
  public status = 0;
  public content = '';
  public createTime = 0;
  public updateTime = 0;
  public expireTime = 0;
  public bindingUser = '';
  // 标记是自己触发的本地更新
  public isSelfUpdate = false;

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    const task = json.task;
    this.seq = task.seq;
    this.taskId = task.task_id;
    this.status = task.status;
    this.content = task.content;
    this.createTime = task.create_time;
    this.updateTime = task.update_time;
    this.expireTime = task.expire_time;
    this.bindingUser = task.binding_user;
  }
}

/**
 * 获取任务列表结果
 * @param {number} lastSeq      最新seq
 * @param {TTaskInfo[]} tasks   符合条件的任务列表
 */
export class TGetTaskListResult extends TModel {
  public lastSeq = 0;
  public tasks: TTaskInfo[] = [];

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.lastSeq = json.last_seq;
    json.tasks.forEach((item: any) => {
      const info = new TTaskInfo();
      info.deserialize({ task: item });
      this.tasks.push(info);
    });
  }
}

export class TBusinessClass extends TModule {
  private translateConfig: any = {};
  public static get instance(): TBusinessClass {
    return this.getInstance();
  }

  /**
   * 查询课堂列表
   * @param {TGetClassListParam}      classParam      参数
   * @param {string}                  token       token
   */
  public getClassList(classParam: TGetClassListParam, token: string): Promise<TGetClassListResult> {
    const param: any = {
      teacher_id: classParam.teacherId,
      school_id: classParam.schoolId,
      classroom_code: classParam.classroomCode,
      page: classParam.page,
      limit: classParam.limit,
      class_types: classParam.classTypes,
      class_status: classParam.classStatus,
    };
    const result = new TGetClassListResult();
    const url = 'class/getClasses';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 查询课堂详情
   * @param {number}                  classId     课堂 ID
   * @param {boolean}                     allTask     是否返回全部任务列表（包括非活跃任务)
   * @param {string}                      token       token
   */
  public getClassInfo(classId: number, allTask: boolean, token: string): Promise<TClassInfo> {
    const param: any = {
      class_id: classId,
      all_task: allTask ? 1 : 0,
    };
    const result = new TClassInfo();
    const url = 'class/getInfo';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 上课
   * @param {number}                  classId     课堂 ID
   * @param {string}                  token       token
   */
  public startClass(classId: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
    };
    const url = 'class/start';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 下课
   * @param {number}                  classId     课堂 ID
   * @param {string}                  token       token
   */
  public endClass(classId: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
    };
    const url = 'class/end';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 上报课后评价
   */
  public submitClassRate({ classId, userId, score, scoreMsg }: {
    classId: number,
    userId: string,
    score: number,
    scoreMsg: string
  }, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      user_id: userId,
      score,
      score_msg: scoreMsg,
    };
    const url = 'class/setScore';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 全体禁言
   * @param {number}                  classId     课堂 ID
   * @param {number}                  silence     0表示取消禁言，1表示禁言，默认为0
   * @param {string}                  token       token
   */
  public silenceAll(classId: number, silence: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      silence,
    };
    const url = 'class/silenceAll';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 新API替代silenceAll，更细粒度全员发言控制
   * @param {number}                  classId     课堂 ID
   * @param {number}                  silence_mode     0：自由聊天 1：仅允许公开聊天 2：仅允许私聊 3：全员禁言
   * @param {string}                  token       token
   */
  public setSilenceMode(classId: number, silence_mode: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      silence_mode,
    };
    const url = 'class/setSilenceMode';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 开启允许后，允许申请连麦
   * @param {number}                  classId     课堂 ID
   * @param {number}                  enable      0表示不允许上台，1表示允许上台，默认为0
   * @param {string}                  token       token
   */
  public setEnableStage(classId: number, enable: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      enable,
    };
    const url = 'class/setEnableStage';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 全员静音
   * @param {number}                  classId  课堂 ID
   * @param {number}                  mute     0表示取消禁音，1表示禁音，默认为0
   * @param {string}                  token    token
   */
  public muteAll(classId: number, mute: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      mute: mute ? 1 : 0,
    };
    const url = 'class/muteAll';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 全员视频
   * @param {number}                  classId  课堂 ID
   * @param {number}                  mute     0表示取消禁音，1表示禁音，默认为0
   * @param {string}                  token    token
   */
  public muteVideoAll(classId: number, mute: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      mute: mute ? 1 : 0,
    };
    const url = 'class/muteVideoAll';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 撤回消息，仅老师可以撤回
   * @param {number}                  classId     课堂 ID
   * @param {number}                  messageSeq 消息 Seq
   * @param {string}                  token       token
   */
  public revokeMessage(classId: number, messageSeq: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      message_seq: messageSeq,
    };
    const url = 'class/recallMessage';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 更新课堂任务
   * @param {number}                  classId     课堂 ID
   * @param {string}                  taskId      任务 ID
   * @param {string}                  content     任务内容
   * @param {number}                  duration    任务持续时长，单位毫秒。-1表示不限时长
   * @param {boolean}                 createOnly  是否仅新建任务，如果任务已存在则失败
   * @param {string}                  bindingUser 生命周期绑定用户，如果用户离线则任务自动失效。空字符串表示不绑定用户
   * @param {string}                  token       token
   */
  public updateTask(
    classId: number, taskId: string, content: string, duration: number, createOnly: boolean
    , enableCallback: boolean,  bindingUser: string, token: string,
  ): Promise<TTaskInfo> {
    const param: any = {
      class_id: classId,
      task_id: taskId,
      content,
      duration,
      only_create: createOnly ? 1 : 0,  // 0表示允许更新，1表示仅创建新任务。默认为0
      enable_callback: enableCallback ? 1 : 0,
    };
    if (bindingUser) {
      param.binding_user = bindingUser;
    }
    const result = new TTaskInfo();
    const url = 'class/updateTask';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 停止课堂任务
   * @param {number}                  classId     课堂 ID
   * @param {string}                  taskId      任务 ID
   * @param {string}                  token       token
   */
  public stopTask(classId: number, taskId: string, token: string): Promise<TTaskInfo> {
    const param: any = {
      class_id: classId,
      task_id: taskId,
    };
    const result = new TTaskInfo();
    const url = 'class/stopTask';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 查询课堂任务
   * @param {number}                  classId     课堂 ID
   * @param {number}                  seq         开始的seq，返回大于seq的任务列表。最小值为0
   * @param {string}                  token       token
   */
  public getTasks(classId: number, seq: number, token: string): Promise<TGetTaskListResult> {
    const param: any = {
      class_id: classId,
      seq,
    };
    const result = new TGetTaskListResult();
    const url = 'class/getTasks';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   *
   * @param {number}                          classId         课堂 ID
   * @param {object}                          custom_data     自定义数据
   * @param {string}                          token           token
   */
  public setCustomData(classId: number, custom_data: Object, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      custom_data: JSON.stringify(custom_data),
    };
    const url = 'class/setCustomData';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   *
   * @param {number}                          classId         课堂 ID
   * @param {string}                          token           token
   * @param {userID}                          userID          以谁的视角去拉历史消息(当前用户ID)
   */
  public getClassMessageData(classId: number, token: string, userID: string): Promise<TClassHistroyMessage> {
    const param: any = {
      class_id: classId,
      user_id: userID,
    };
    const result = new TClassHistroyMessage();
    const url = 'record/getClassMessage';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 设置资源限制
   * @param {number}                  classId     课堂 ID
   * @param {string}                  name        资源名称
   * @param {number}                  limit       限制数量
   * @param {string}                  token       token
   */
  public setResourceLimit(classId: number, name: string, limit: number, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      name,
      limit,
    };
    const url = 'class/setResourceLimit';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 获取资源
   * @param {number}                  classId     课堂 ID
   * @param {string}                  name        资源名称
   * @param {string}                  token       token
   */
  public requireResource(classId: number, name: string, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      name,
    };
    const url = 'class/requireResource';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 释放资源
   * @param {number}                  classId     课堂 ID
   * @param {string}                  name        资源名称
   * @param {string}                  token       token
   */
  public releaseResource(classId: number, name: string, token: string): Promise<null> {
    const param: any = {
      class_id: classId,
      name,
    };
    const url = 'class/releaseResource';
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 设置翻译参数
   * @param {string}                  state      开关状态，0关闭，1开启
   * @param {string}                  lang       语言
   * @return {Promise<void>}
   */
  public setTranslate(classId: number, state: number, lang: string, token: string): Promise<void> {
    const url = 'record/setTranslate';
    const param: any = {
      class_id: classId,
      state,
      lang,
    };
    this.translateConfig = {
      state,
      lang,
    };
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * 获取翻译机器人参数
   * @param {number}                          classId         课堂 ID
   * @param {string}                          token           token
   */
  public getTranslateConfig() {
    return this.translateConfig;
  }

  /**
   * 请求翻译具体消息（重试逻辑）
   * @param {number}                          classId         课堂 ID
   * @param {number}                          seq             消息 seq
   * @param {string}                          from            消息发送方
   * @param {string}                          message         消息内容
   * @param {string}                          lang            语言，en表示英文，zh表示中文.....
   * @param {string}                          token           msg
   */
  // eslint-disable-next-line max-len
  public translate(classId: number, from: string, seq: number, message: string, lang: string, token: string): Promise<void> {
    const url = 'record/translate';
    const param: any = {
      class_id: classId,
      from,
      seq,
      message,
      lang,
    };
    return TBusiness.instance.request(url, param, token, null);
  }

  /**
   * @param classId 课堂 ID
   * @param layout 课堂布局
   * @param token token
   */
  public setLayout(classId: number, layout: TClassLayout, token: string): Promise<void> {
    const url = 'class/setLayout';
    const param: any = {
      class_id: classId,
      layout,
    };
    return TBusiness.instance.request(url, param, token, null);
  }
}
