import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TCICError,
  TModule,
  TRTCElectronError,
  TRTCMobileError,
  TRTCWebError,
} from '../base/tmodule';
import {
  CheckValueInEnum,
  TAudienceMixType,
  TAudienceType,
  TBusinessClass,
  TClassHistroyMessage,
  TClassInfo,
  TClassMarquee, TClassSilenceMode,
  TClassStatus,
  TClassSubType,
  TClassType,
  TGetTaskListResult,
  TRoomType,
  TTaskInfo,
  VideoOrientation,
} from './business/tbusiness_class';
import { RECORD_PAGE_VERSION, SDK_MAIN_VERSION } from './tversion';
import { Subject, combineLatest, distinctUntilChanged, map, throttleTime } from 'rxjs';
import {
  TAnswerQuestionParam,
  TAnswerQuestionResult,
  TBusinessExam,
  TCreateQuestionParam,
  TCreateQuestionResult,
  TGetQuestionInfoR<PERSON>ult,
  TGetQuestionResultResult,
  TGetQuestionStatsResult,
  TGetQuestionsResult,
} from './business/tbusiness_exam';
import {
  TBatchDocumentInfo,
  TBusinessDocument,
  TCreateDocumentResult,
  TDocumentInfo,
  TGetDocumentListParam,
  TGetDocumentListResult,
  TUploadProgressCallback,
  TUploadReadyCallback,
  TUploadResponseCallback,
} from './business/tbusiness_document';
import {
  TBoard,
  TBoardInitParams,
} from './tboard';
import { TBusinessCommand, TCommandID, TCommandList, TCommandReq, TCommandReqResult, TCommandStatus, TGetUserCommandReqResult } from './business/tbusiness_command';
import {
  TBusinessMember,
  TGetHandUpListResult,
  TGetMemberListResult,
  THandUpListFilter,
  TJoinType,
  TMemberActionParam,
  TMemberActionType,
  TMemberHeartbeat,
  TMemberInfo,
  TMemberJoinResult,
  TMemberListFilter,
  TMemberRole,
  TMemberRtcInfo,
  TMemberStateReportParam,
  TPermissionListResult,
  TStreamInfo,
  TStreamType,
  TSyncInfo,
} from './business/tbusiness_member';
import {
  TBusinessSchool,
  TSchoolInfo,
  TSchoolWaterMark,
} from './business/tbusiness_school';
import {
  TBusinessUser,
  TGetUserListResult,
  TPermissionInfo,
  TUserInfo,
} from './business/tbusiness_user';
import {
  TClassLayout,
  TClassLayoutMainFrameMap,
  TComponentLayout,
  TDevice,
  TDeviceNameMap,
  TDeviceStatus,
  TFeatureDefaultConfig,
  TIMConvType,
  TIMEvent,
  TIMMsg,
  TMainEvent,
  TMainState,
  TMemberStateConfig,
  TPackageFeatureConfigMap,
  TPackageType,
  TPermissionFlag,
  TPermissionUpdateReason,
  TPlatform,
  TPlatformCategory,
  TPlatformNameMap,
  TPlatformToPlatformCategoryMap,
  TStateType,
  packageFeatureEmptyConfig,
} from '../constants';
import {
  TEvent,
  TEventListener,
  TEventOptions,
} from '../base/tevent';
import {
  TIM,
  TSendProgressCallback,
} from './tim';
import {
  TLevel,
  TLogger,
  TLoggerConfig,
  TLoggerParam,
} from './tlogger';
import { TNetworkQuality, TNetworkStatistics, TStatisticsEvent } from './statistics/tstatistics_base';
import {
  TRTCVideoQosPreference,
  TScreenCaptureSourceInfo,
  TTrtcDeviceInfo,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcLocalVideoParams,
  TTrtcMode,
  TTrtcSnapshot,
  TTrtcVideoResolution,
  TTrtcVideoRotation,
  TTrtcVideoStreamType,
} from './trtc/ttrtc_base';
import {
  TState,
  TStateOptions,
  TStateUpdateListener,
} from './tstate';
import { errorCodePromise, i18nextPromise } from './i18n';

import { TAV } from './tav';
import { TBase } from '../base/tbase';
import { TBusiness } from './business/tbusiness';
import { TLiveUtil as TLive } from './live/live_util';
import { TRBAC } from './trbac';
import { TResourcePath } from './business/tbusiness_rbac';
import { TSession } from './tsession';
import { TStatisticsUtil as TStatistics } from './statistics/tstatistics_util';
import { TStore } from './tstore';
import { TTrtcUtil as TTrtc } from './trtc/ttrtc_util';
import { TUtil } from './tutil_inner';
import { TVueComponent } from '../base/tbasevue';
import { TWebView } from './twebview_inner';
import browserVersion from '../utils/browserVersion';
import { getQuery } from '../utils/query';
import i18next from 'i18next';
import pako from 'pako';
import TencentCloudChat from '@tencentcloud/chat';

// import { rum } from '../utils/aegis';

// 只有在Chrome中需要使用Hacktimer
if (browserVersion().includes('Chrome')) {
  import('hacktimer');
}

/**
 * 矩形
 */
export interface TRect {
  height: number;
  width: number;
  left: number;
  top: number;
  right: number;
  bottom: number;
}

/**
 * 设备方向
 */
export enum TDeviceOrientation {
  /**
   * 横屏（默认）
   */
  Landscape,
  /**
   * 竖屏
   */
  Portrait,
}

const EXT_CTRL = 'CTRL';

// 声明window下有Electron，ActiveXObject，XMLHttpRequest 三个属性
declare global {
  interface Window {
    saveSnapshot: any; // 申明window对象下有保存截图的功能
    Electron: any;
    ActiveXObject: any;
    XMLHttpRequest: any;
    HTMLElement: any;
    polyfillHTMLElement: any;
    wx: any;
  }
}

declare const __TCIC_SDK_VERSION__: string;

class TPromise {
  public _promise: any = null;
  public _resolve: any = null;
  public _reject: any = null;

  constructor() {
    this._promise = new Promise((resolve, reject) => {
      this._resolve = resolve;
      this._reject = reject;
    });
  }

  resolve(data: any) {
    this._resolve(data);
  }

  reject(err: any) {
    this._reject(err);
  }

  getPromise() {
    return this._promise;
  }
}

export type ComponentEvent = 'drag_start' | 'drag_end' | 'visible_changed' | 'rect_changed';
export type ComponentEventListener = (eventName: ComponentEvent, params: any[]) => void;

interface ComponentDraggableInfo {
  draggable: boolean,
  focusTouch: Touch,
  downTouchEvent: EventListenerOrEventListenerObject,
  getDraggableRect: () => DOMRect,
  startDragXY: any,
  offset: any,
  forceUpdate: boolean,  // 发生旋转时是否强制更新
}

class ComponentInfo {
  private static draggableComponentList: any[] = [];
  public name: string;
  public label: string;
  public loaded = false;
  // public commonLayout: TComponentLayout = {};
  public dom: HTMLElement;
  public parentDomId: string;
  public remove = false;
  public create = false;
  public update = false;
  public visible = false;
  public rect: DOMRect = null;
  public resolves: ((result: boolean) => void)[] = [];
  public listeners: ComponentEventListener[] = [];


  private _layout: TComponentLayout = {};
  private draggableCount = 0;          // 开启了可拖动的DOM节点数
  private draggableInfos: Map<string, ComponentDraggableInfo> = null;     // 记录节点的可拖动状态
  private updateDraggableLayoutTimeout = 0;  // 更新可拖动布局定时器，用于避免更新可拖动布局接口调用过于频繁

  constructor(name: string, label: string) {
    this.name = name;
    this.label = label;
    this.updateStatus();
  }

  public get layout(): TComponentLayout {
    return {
      ...this._layout,
      // ...this.commonLayout,
    };
  }

  public set layout(layout: TComponentLayout) {
    this._layout = layout;
  }

  public fullName() {
    return `${this.name}-${this.label}`;
  }

  public resolve(result: boolean) {
    this.resolves.forEach(resolve => resolve(result));
    this.resolves = [];
  }

  public updateStatus() {
    let visible;
    let rect;
    if (this.dom) {
      visible = (this.dom.offsetParent !== null);
      if (this.dom.children.length > 0) {
        rect = TMain.makeDomRect(this.dom.children[0].getBoundingClientRect());  // 第一个子节点
      } else {
        rect = TMain.makeDomRect(this.dom.getBoundingClientRect());
      }
    } else {
      visible = false;
      rect = TMain.makeDomRect({
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        width: 0,
        height: 0,
      });
    }
    if (this.visible !== visible) {
      this.visible = visible;
      this.listeners.forEach((callback) => {
        callback('visible_changed', [this.visible]);
      });
      // 更新在可拖动组件列表中的层级
      if (visible && this.draggableCount > 0) {
        this.appendToDraggableComponentList();
      } else {
        this.removeFromDraggableComponentList();
      }
    }
    if (this.rect === null
      || this.rect.x !== rect.x
      || this.rect.y !== rect.y
      || this.rect.width !== rect.width
      || this.rect.height !== rect.height) {
      this.rect = rect;
      this.listeners.forEach((callback) => {
        callback('rect_changed', [this.rect]);
      });
    }
  }

  public destroy() {
    // 遍历所有开启了拖动的节点，逐个取消
    if (this.draggableInfos) {
      Array.from(this.draggableInfos.keys()).forEach((domSelector) => {
        this.toggleComponentDraggable(false, domSelector);
      });
    }
    this.dom.parentElement.removeChild(this.dom);
    this.dom = null;
  }

  public toggleComponentDraggable(
    draggable: boolean,
    domSelector: string,
    draggableRect: DOMRect | (() => DOMRect) = null,
    forceUpdate = true,
  ) {
    const draggableInfo = this.getDraggableInfo(domSelector);
    if (draggableInfo.draggable === draggable) {  // 已经保存的状态和目标状态一致，忽略
      return;
    }
    draggableInfo.draggable = draggable;
    draggableInfo.forceUpdate = forceUpdate;
    if (draggable) {
      if (draggableRect) {
        if (typeof draggableRect === 'function') {
          // 如果传入的参数是一个可调用的函数，每次直接调用它去获取可拖动矩形
          draggableInfo.getDraggableRect = draggableRect;
        } else {
          // 如果传入的参数不是一个可调用的函数，将至视为矩形对象每次获取时直接返回
          draggableInfo.getDraggableRect = () => draggableRect;
        }
      }
      this.draggableCount += 1;
      this.initDragEvent(domSelector);
    } else {
      this.draggableCount -= 1;
      if (this.draggableCount === 0) {    // 没有可拖动区域(关闭拖动)时
        this.removeFromDraggableComponentList();    // 删除组件位置信息
      }
      this.destroyDragEvent(domSelector);
    }
  }

  /**
   * 将当前组件添加到可拖动组件列表，并将层级移动到所有组件之上，其它组件层级填充空位
   * @param update 是否马上更新层级展示
   */
  public appendToDraggableComponentList(update = true) {
    // 从列表中移除，不马上更新
    this.removeFromDraggableComponentList(false);
    // 添加到列表
    const count = ComponentInfo.draggableComponentList.length;
    ComponentInfo.draggableComponentList.push({
      name: this.name,
      label: this.label,
      zIndex: count > 0 ? ComponentInfo.draggableComponentList[count - 1].zIndex + 1 : 350,  // 可拖动组件层级从350开始
    });
    if (update) {
      this.updateDraggableComponentList();
    }
  }

  /**
   * 将当前组件从可拖动组件列表移除，并使其它组件层级填充空位
   * @param update 是否马上更新层级展示
   */
  public removeFromDraggableComponentList(update = true) {
    // 在列表中查找组件
    const comIndex = ComponentInfo.draggableComponentList
      .findIndex(com => com.name === this.name && com.label === this.label);
    const count = ComponentInfo.draggableComponentList.length;
    // 如果存在，删除，并规整所有组件的层级
    if (comIndex !== -1) {
      for (let index = comIndex + 1; index < count; ++index) {
        ComponentInfo.draggableComponentList[index].zIndex -= 1;
      }
      ComponentInfo.draggableComponentList.splice(comIndex, 1);
    }
    if (update) {
      this.updateDraggableComponentList();
    }
  }

  /**
   * 将当前组件从可拖动组件最高层级展示
   */
  public makeTopmostOfDraggableComponentList() {
    // 更新本组件层级到500
    TMain.instance.updateComponent(this.name, { zIndex: 500 }, this.label).then();
    // 从列表中移除，马上更新
    this.removeFromDraggableComponentList();
  }

  /**
   * 更新可拖动区域并重新布局组件，该接口性能消耗较大，因此内部做了10次/s的限频
   */
  public updateDraggableLayout() {
    if (this.draggableCount <= 0) {
      return;
    }
    if (!this.updateDraggableLayoutTimeout) {
      this.updateDraggableLayoutTimeout = window.setTimeout(() => {
        this.updateDraggableLayoutTimeout = 0;
        if (!this.visible) return;
        if (this.draggableCount <= 0) return;
        const domRect = Object.assign({}, this.rect);
        const pageDraggableRect = TMain.instance.getAppClientRect();
        this.draggableInfos.forEach((draggableInfo) => {
          if (draggableInfo.draggable && draggableInfo.forceUpdate) {
            // 尝试获取传入的可拖动区域
            let draggableRect;
            if (draggableInfo.getDraggableRect) {
              draggableRect = draggableInfo.getDraggableRect();
            } else {
              draggableRect = pageDraggableRect;
            }
            const offset = this.offsetAdjust(0, 0, domRect, draggableRect);
            if (offset.x !== 0 || offset.y !== 0) {
              // 计算拖动前组件左上角的变换后坐标
              const componentOffset = this.getComponentOffset();
              // 重置transform，并将offset加到组件位移上，避免transform变换在拖动完成后产生其它影响
              TMain.instance.updateComponent(this.name, {
                left: `${componentOffset.x + offset.x}px`,
                top: `${componentOffset.y + offset.y}px`,
              }, this.label).then(() => {
              });
            }
          }
        });
        // console.log('updateDragableLayout', this.tcicComponentName, this.tcicComponentLabel);
      }, 100);
    }
  }

  private updateDraggableComponentList() {
    ComponentInfo.draggableComponentList.forEach((com) => {
      TMain.instance.updateComponent(com.name, { zIndex: com.zIndex }, com.label).then();
    });
  }

  private getDraggableInfo(domSelector: string) {
    if (!this.draggableInfos) {
      this.draggableInfos = new Map();
    }
    if (!this.draggableInfos.has(domSelector)) {
      this.draggableInfos.set(domSelector, {
        draggable: false,
        focusTouch: null,
        downTouchEvent: null,
        getDraggableRect: null,
        startDragXY: null,
        offset: null,
        forceUpdate: true,
      });
    }
    return this.draggableInfos.get(domSelector);
  }

  private offsetAdjust(offsetX: number, offsetY: number, domRect: DOMRect, draggableRect: DOMRect) {
    // 顶部不允许超过导航栏
    const showHeader = TMain.instance.getState('TStateHeaderVisible', true);
    const draggableRectTop = 0;
    // const draggableRectTop = Math.max(draggableRect.top, showHeader ? 45 : 0);

    const adjust = (
      offset: number,
      startMin: number,
      startMax: number,
      containerMin: number,
      containerMax: number,
    ) => {
      let adjustedOffset = offset;
      // 上界
      if (adjustedOffset + startMax > containerMax) {
        adjustedOffset = containerMax - startMax - 1;
      }
      // 下界
      if (adjustedOffset + startMin < containerMin) {
        adjustedOffset = containerMin - startMin + 1;
      }
      // 避免临界值在1px之间抖动
      if (Math.abs(adjustedOffset) < 1) {
        adjustedOffset = 0;
      }
      return adjustedOffset;
    };

    return {
      x: adjust(offsetX, domRect.left, domRect.right, draggableRect.left, draggableRect.right),
      y: adjust(offsetY, domRect.top, domRect.bottom, draggableRectTop, draggableRect.bottom),
    };
  }

  // 将经过transform变化的坐标点还原回变换前的坐标点
  private getComponentOffset() {
    let offsetTop = 0;
    let offsetLeft = 0;
    let el: any = this.dom;
    while (el) {
      offsetTop += el.offsetTop;
      offsetLeft += el.offsetLeft;
      el = el.offsetParent;
    }
    return {
      x: offsetLeft - window.pageXOffset,
      y: offsetTop - window.pageYOffset,
    };
  }

  private destroyDragEvent(domSelector: string) {
    const draggableInfo = this.getDraggableInfo(domSelector);
    const dragEl = this.dom.querySelector(domSelector);
    if (dragEl && draggableInfo.downTouchEvent) {
      dragEl.removeEventListener('mousedown', draggableInfo.downTouchEvent, true);
      dragEl.removeEventListener('touchstart', draggableInfo.downTouchEvent, true);
    }
  }

  private initDragEvent(domSelector: string) {
    const draggableInfo = this.getDraggableInfo(domSelector);
    const dragEl = this.dom.querySelector(domSelector);
    if (dragEl) {
      const getPageXY = (event: any) => {
        if (!event.touches) {
          return {
            pageX: event.pageX,
            pageY: event.pageY,
          };
        }
        if (!draggableInfo.focusTouch) {
          // 刚开始拖动，选中第一个手指，返回其位置
          draggableInfo.focusTouch = event.touches[0];
        } else {
          // 查找开始拖动时的第一个手指，获取其位置
          // eslint-disable-next-line @typescript-eslint/prefer-for-of
          for (let i = 0; i < event.touches.length; ++i) {
            if (draggableInfo.focusTouch.identifier === event.touches[i].identifier) {
              draggableInfo.focusTouch = event.touches[i];
              break;
            }
          }
        }
        // 未找到手指，返回之前的位置
        return {
          pageX: draggableInfo.focusTouch.pageX,
          pageY: draggableInfo.focusTouch.pageY,
        };
      };
      const matrixToCSS = (matrix: any) => `matrix(${matrix.a},${matrix.b},${matrix.c},${matrix.d},${matrix.e},${matrix.f})`;
      draggableInfo.downTouchEvent = (event: any) => {
        if (event instanceof MouseEvent) {
          event.preventDefault();
        }
        if (draggableInfo.focusTouch) {  // 多指拖动，只响应第一个手指
          return;
        }

        // button, a等元素，禁止进行拖动
        const targetElName = event.target.tagName.toLocaleLowerCase();
        if (['button', 'a'].indexOf(targetElName) > -1) {
          return;
        }
        // 层级移动到最高
        this.makeTopmostOfDraggableComponentList();
        this.listeners.forEach((callback) => {
          callback('drag_start', []);
        });
        // 记录开始拖动位置
        draggableInfo.startDragXY = getPageXY(event);
        draggableInfo.offset = { x: 0, y: 0 };
        // 记录开始拖动时页面大小
        const startDragPageRect = TMain.instance.getAppClientRect();
        // 记录节点初始位置
        const startDragDomRect = Object.assign({}, this.rect);
        // 计算拿到transform相关样式
        const style = window.getComputedStyle(this.dom);
        const startDragTransform = style.transform;
        const onDragMove = (event: any) => {
          if (event instanceof MouseEvent) {
            event.preventDefault();
          }
          const { pageX, pageY } = getPageXY(event);
          let draggableRect;
          // 尝试获取传入的可拖动区域
          if (draggableInfo.getDraggableRect) {
            draggableRect = draggableInfo.getDraggableRect();
          } else {
            // 默认取整个页面区域作为默认可拖动区域
            draggableRect = startDragPageRect;
          }
          draggableInfo.offset = this.offsetAdjust(
            pageX - draggableInfo.startDragXY.pageX,
            pageY - draggableInfo.startDragXY.pageY,
            startDragDomRect,
            draggableRect,
          );
          // 构造新的transform矩阵
          const matrix = new window.WebKitCSSMatrix(startDragTransform);
          matrix.e += draggableInfo.offset.x;
          matrix.f += draggableInfo.offset.y;

          // 更新transform
          TMain.instance.updateComponent(this.name, {
            transform: matrixToCSS(matrix),
          }, this.label).then(() => {
          });
        };

        const onDragUp = (event: any) => {
          const onDragDone = () => {
            document.removeEventListener('mousemove', onDragMove);
            document.removeEventListener('touchmove', onDragMove);
            document.removeEventListener('mouseup', onDragUp);
            document.removeEventListener('touchend', onDragUp);
            document.removeEventListener('touchcancel', onDragUp);
            // 将组件层级提到其它可拖动组件之上
            this.appendToDraggableComponentList();
            // 有发生移动时，将transform转为left、top位置
            if (draggableInfo.offset.x !== 0 || draggableInfo.offset.y !== 0) {
              // 获取组件左上角坐标
              const componentOffset = this.getComponentOffset();
              // 重置transform，并将offset加到组件位移上，避免transform变换在拖动完成后产生其它影响
              TMain.instance.updateComponent(this.name, {
                left: `${componentOffset.x + draggableInfo.offset.x}px`,
                top: `${componentOffset.y + draggableInfo.offset.y}px`,
                transform: startDragTransform,
              }, this.label).then(() => {
              });
            }
            this.listeners.forEach((callback) => {
              callback('drag_end', []);
            });
            draggableInfo.focusTouch = null;
          };
          if (event instanceof MouseEvent) {  // 鼠标事件直接处理
            onDragDone();
            return;
          }
          if (!draggableInfo.focusTouch) {  // 没有选中的手指，直接忽略
            return;
          }
          // 清除记录下来的触摸点
          if (event.type === 'touchcancel') {
            onDragDone();
          } else {
            if (event.changedTouches && draggableInfo.focusTouch) { // 查找抬起的是不是按下的手指
              // eslint-disable-next-line @typescript-eslint/prefer-for-of
              for (let i = 0; i < event.changedTouches.length; ++i) {
                if (draggableInfo.focusTouch.identifier === event.changedTouches[i].identifier) {
                  // 如果此时抬起的手指 === 选中的手指
                  onDragDone();
                  break;
                }
              }
            }
          }
        };

        document.addEventListener('mouseup', onDragUp, {
          passive: true,
        });
        document.addEventListener('touchend', onDragUp, {
          passive: true,
        });
        document.addEventListener('touchcancel', onDragUp, {
          passive: true,
        }); // 当touch取消，一般发生在来电话，弹出了其他窗口覆盖了
        document.addEventListener('mousemove', onDragMove);
        document.addEventListener('touchmove', onDragMove);
      };
      dragEl.addEventListener('mousedown', draggableInfo.downTouchEvent, true);
      dragEl.addEventListener('touchstart', draggableInfo.downTouchEvent, true);
    } else {
      console.warn(`not found drag element ${domSelector}`);
    }
  }
}

// 在页面加载完成后马上使用URL内的参数做参数初始化一次
// rum.aegis.setConfig({
//   uin: TSession.instance.getUserId(),
//   ext1: String(TSession.instance.getClassId()),
// });

type CustomFeature = 'ScreenShareAdvanceMode' | 'MobileImmerseMode' | 'WhiteBoardTOOL' | 'WhiteBoardList' | 'WhiteBoardPPT';

/**
 * SDK功能类，SDK的主要功能入口
 * @hideconstructor
 */
export class TMain extends TModule {
  /**
   * 返回该类实例CIC
   */
  public static get instance(): TMain {
    return this.getInstance();
  }
  /**
   * 注册组件
   * @param {string} name 组件名称
   * @param {} component 组件对象
   */
  public static registerComponent(name: string, component: any) {
    component.name = name;
    TVueComponent.instance.register(name, component);
  }

  public static makeDomRect(rect: DOMRect | ClientRect | TRect): DOMRect {
    return {
      left: rect.left,
      top: rect.top,
      right: rect.right,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height,
      x: rect.left,
      y: rect.top,
      toJSON() {
        return this;
      },
    };
  }

  /**
   * 是否有安装Electron美颜插件.
   * @returns boolean
   */
  public static hasElectronBeautyPlugin() {
    return !!window.Electron?.beautySdk?.hasPlugin?.();
  }
  private static _touchScreenAdjust() {
    if (TSession.instance.supportTouch()) { // remove all the :hover stylesheets
      try { // prevent exception on browsers not supporting DOM styleSheets properly
        // eslint-disable-next-line no-restricted-syntax
        for (const si in document.styleSheets) {
          const styleSheet: any = document.styleSheets[si];
          if (!styleSheet.cssRules) return;

          for (let ri = styleSheet.cssRules.length - 1; ri >= 0; ri--) {
            if (!styleSheet.cssRules[ri].selectorText) continue;
            if (styleSheet.cssRules[ri].selectorText.search(':hover|:focus|:active') !== -1) {
              // 将:hover样式之外的其它规则提取出来，添加到样式表后面
              const selectors: string[] = styleSheet.cssRules[ri].selectorText.split(',');
              const selectorsExcludeHover = selectors
                .filter(selector => selector.search(':hover|:focus|:active') === -1
                  || selector.search('^\\.(dark|light) :focus$') !== -1);
              if (selectorsExcludeHover.length > 0) {
                const newSelectorStr = selectorsExcludeHover.join(',');
                const rule = `${newSelectorStr} { ${styleSheet.cssRules[ri].style.cssText} }`;
                styleSheet.insertRule(rule, styleSheet.cssRules.length);
              }

              styleSheet.deleteRule(ri);  // 删除整条规则
            }
          }
        }
      } catch (ex) {
      }
    }
  }

  private static _getClientHeartbeatInterval(x: number): number {
    if (x >= 5000) return Math.ceil(4000 + (x - 5000) * 0.64);
    if (x >= 3000) return Math.ceil(3000 + (x - 3000) * 0.5);
    return x;
  }

  // 平台信息
  private _platformInfo: {
    device: TDevice;
    deviceName: string;
    platform: TPlatform;
    platformName: string;
    platformCategory: TPlatformCategory;
  } = null;
  private _rootDom: HTMLElement = null;
  private _appClientRect: DOMRect = null;
  private _componentsMap: Map<string, ComponentInfo> = new Map();
  private _componentsUpdateTask = 0;
  private _memberInfoList = new Map();    // 缓存用户信息
  private _permissionList: TPermissionInfo[] = [];
  private _permissionListSequence = -1;  // 记录权限信息当前的序列号
  private _streamList: TStreamInfo[] = [];
  private _streamListSequence = -1;  // 记录流信息当前的序列号
  private _userAvMap = new Map();
  private _isJoinedClass = false;
  private _heartbeatInterval = 0;
  private _heartBeatTimer = 0;
  private _curDialogId = 1;   // 弹窗id
  private _tipDialogId = 1;
  private _enterTimeStamp = 0;
  private _clientInfo: any = null; // 客户端系统信息
  private _joinRtt = 0;  // 记录 rtt，用来近似修正延迟
  private _serverTimestampAfterJoin = 0;  // 记录登录完成后的服务器时间戳
  private _localTimestampAfterJoin = 0;  // 记录登录完成后的本地时间戳
  private _lastTaskInfos: any = []; // 记录收到最新的任务信息
  private _lastTaskSeqOfClass = 0;  // 记录收到的任务最新序列号
  private _lastTaskSeq: { [key: string]: number } = {};  // 记录每个任务的最新序列号，解决可能存在的通知乱序问题
  private _missTaskSeqSet = new Set<number>();  // 记录缺失的任务序列号，方便必要时补拉
  private _updateTasksTimer = 0;  // 记录更新任务定时器
  private _boardInitParams: TBoardInitParams;  // 记录传入的白板初始化参数
  private _classDurationInterval = 0;  // 课堂时长定时器
  private _isAnchor = false;     // 当前是否主播角色
  private _classVisibleChangeEventName: string = null; // 页面可见性事件名
  private _screenSharing = false;  // 是否正在进行屏幕分享（记录接口调用状态）
  private _queryMap = new Map();    // 查询用户缓存列表
  private _queryTimer = 0;       // 延时查询定时器
  private _waterMark: TSchoolWaterMark = null;      // 水印信息
  private _marquee: TClassMarquee = null;        // 跑马灯信息
  private _useCustomWaterMark = false;    // 使用自定义水印(通过接口设置的水印，将覆盖学校信息中的水印)
  private _switchRolePromise: Promise<any> = null;    // 切换角色
  private _isIgnoreBackPressed = false;   // 是否忽略移动端返回事件
  private _packageFeatureConfig: { [feature: string]: boolean } = packageFeatureEmptyConfig; // 套餐包限制的功能
  private _localFeatureMap: Map<string, boolean> = new Map(); // 本地高级功能列表
  private _joinType: TJoinType = TJoinType.Unknown;   // 加入课堂方式
  private _position: any = null;
  private _startLocalVideoPromise: Promise<void> = null;
  private _startLocalAudioPromise: Promise<void> = null;
  private _classLayoutConfig: {
    fixedLayout: TClassLayout | '';
    defaultLayout: TClassLayout;
  } = null;
  private nano_timestamp = -1;  // 记录踢人时间戳
  private _isDocumentAudioPlaying = false;
  private _isDocumentVideoPlaying = false;
  private _member_join_exit_filter: Function = null;
  private _stage_media_option: {
    micAutoOpen: boolean,
    cameraAutoOpen: boolean
  } = null;

  constructor() {
    super();

    // 获取平台信息
    this.getPlatformInfo();

    // 捕获异常事件并上报, 本地调试不用上报
    window.addEventListener('error', (event: ErrorEvent) => {
      try {
        if (window.location.href && TSession.instance.isLocalUrl(window.location.href)) {
          event.preventDefault();
          return;
        }
        if (event.error) {
          TMain.instance.reportException(TSession.instance.getGlobalRandom(), {
            message: `msg:${event.error.message}|file:${event.filename}|line:${event.lineno}|col:${event.colno}`,
            stack: event.error.stack,
          });
        } else {
          TMain.instance.reportException(TSession.instance.getGlobalRandom(), {
            message: `msg:${event.message}|file:${event.filename}|line:${event.lineno}|col:${event.colno}`,
          });
        }
      } catch (error) {
        console.error('%c [ error ]-897', 'font-size:13px; background:pink; color:#bf2c9f;', error);
        // if (TSession.instance.isMobileNative()) {
        //   TWebView.instance.call('os', 'fatalError', {
        //     fatalError: JSON.stringify(error),
        //   });
        // }
      }
      // 防止事件处理完成后多一次控制台打印
      /**
       * 阻止默认事件会导致控制台看不到异常错误信息来源
       */
    });

    // 捕获未处理异常做上报
    window.addEventListener('unhandledrejection', (event: any) => {
      console.log('rtcinfos unhandledrejection');
      if (event.reason) {
        if (event.reason && event.reason instanceof Error) {
          const errorReport = {
            message: event.reason.message,
            stack: event.reason.stack,
            reason: JSON.stringify(event.reason),
          };
          TMain.instance.reportException(TSession.instance.getGlobalRandom(), {
            reason: JSON.stringify(errorReport),
          });
        } else {
          TMain.instance.reportException(TSession.instance.getGlobalRandom(), {
            reason: JSON.stringify(event.reason),
          });
        }
      }
      console.error(event);
      /**
       * 阻止默认事件会导致控制台看不到异常错误信息来源
       */
    });

    // 实例化数据统计模块
    TStatistics.getInstance();
    this._initFeature();
  }

  /**
   * 收集系统信息用于异常上报，该接口耗时较长（可能长达10秒以上），必须异步处理
   */
  public getSystemInfo(): Promise<any> {
    if (TSession.instance.isElectron()) {
      if (window.Electron) {
        if (window.Electron.hasOwnProperty('getSystemInfo')) {
          return window.Electron.getSystemInfo({
            cpu: '*',
            mem: '*',
            graphics: '*',
            osInfo: '*',
            system: '*',
            currentLoad: '*',
            processes: '*',
          });
        }
      }
    }
    return Promise.reject(i18next.t('不支持该方法'));
  }
  // 旋转视频画面90度
  public  rotate90(userId: string, streamType: TStreamType) {
    const currentRoate = TSession.instance.getCurrentRotate();
    let setRoate;
    if (currentRoate == TTrtcVideoRotation.Rotation_270) {
      setRoate = TTrtcVideoRotation.Rotation_0;
    } else {
      setRoate = currentRoate + 1;
    }
    this.setLocalVideoParams({
      rotation: setRoate,
    }).then(() => {
      this.setRemoteRenderParams({
        rotation: setRoate,
      }, userId, streamType);
    });
    TSession.instance.setCurrentRotate(setRoate);
    this._info('Rotate90', `rotate:${setRoate}`);
  }
  public async showDeviceDetectDailog(): Promise<void> {
    // TODO 关闭设置面板
    const header = this.getComponent('header-component');
    if (header) {
      // @ts-ignore
      const refs = header.getVueInstance().$refs;
      if (refs.header) {
        const subHeader = refs.header;
        if (subHeader.$refs.setting) {
          if (subHeader.$refs.setting.length) {
            const settings = subHeader.$refs.setting;
            for (let index = 0; index < settings.length; index++) {
              const element = settings[index];
              if (element.localComponent) {
                element.localComponent.active = false;
              }
            }
          }
        }
      }
    }
    if (this.getComponent('device-detect-tools')) {
      await this.updateComponent('device-detect-tools', {
        display: 'block',
        zIndex: 2016,
      });
    } else {
      await this.loadComponent('device-detect-tools', {
        display: 'block',
        zIndex: 2016,
      });
    }
    return;
  }
  public async hideDeviceDetectDailog() {
    await this.removeComponent('device-detect-tools');
  }
  public getDesktopWindows(params: any): Promise<void> {
    return new Promise(async (resolve, reject) => {
      if (window.Electron) {
        await window.Electron.getDesktopWindows(params).then(() => {
          resolve();
        })
          .catch(() => {
          });
      }
    });
  }

  /**
   * 获取应用客户区矩形
   *
   * @return {DOMRect} 应用客户区矩形
   */
  public getAppClientRect(): DOMRect {
    if (!this._isDomRectValid(this._appClientRect)) {  // 没有或无效矩形时重新获取
      this._updateAppClientRect();
    }
    return this._appClientRect;
  }

  /**
   * 加载组件
   * @param {string} name 组件名称
   * @param {TComponentLayout} layout 初始布局
   * @param {string} parentDomId 父节点 ID
   * @param {string} label 标识
   * @return {Promise<HTMLElement | null>} HTML元素
   */
  public loadComponent(name: string, layout?: TComponentLayout, parentDomId?: string, label = 'default'): Promise<HTMLElement | null> {
    // this._debug('loadComponent', `${name}-${label} -> ${JSON.stringify(layout)}`);
    return new Promise<HTMLElement>((resolve) => {
      const comInfo = this._getComponentInfo(name, label);
      comInfo.loaded = true;  // 标记为已加载
      if (layout) {
        comInfo.layout = layout;
      }
      if (parentDomId) {
        comInfo.parentDomId = parentDomId;
      }
      comInfo.create = true;
      comInfo.resolves.push((result) => {
        if (result) {
          resolve(comInfo.dom);
        } else {
          resolve(null);
        }
      });
      this._updateAllComponents();
    });
  }

  /**
   * 更新组件
   * @param name 组件名
   * @param layout 布局
   * @param label 标识
   *
   * @return Promise 更新是否成功
   */
  public updateComponent(name: string, layout: TComponentLayout, label = 'default'): Promise<boolean> {
    this._debug('updateComponent', `${name}-${label} -> ${JSON.stringify(layout)}`);
    return new Promise<boolean>((resolve) => {
      const comInfo = this._getComponentInfo(name, label);
      // 如果layout所有字段完全没变化，忽略
      if (!Object.keys(layout).some((key: keyof TComponentLayout) => comInfo.layout[key] !== layout[key])) {
        resolve(true);
        return;
      }
      comInfo.layout = Object.assign(comInfo.layout, layout);
      comInfo.update = true;
      comInfo.resolves.push(resolve);
      this._updateAllComponents();
    });
  }

  public updateComponents(name: string, layout: TComponentLayout): Promise<boolean> {
    this._debug('updateComponents', `${name}-* -> ${JSON.stringify(layout)}`);
    return new Promise<boolean>((resolve) => {
      let hasComp = false;
      for (const [key, comInfo] of this._componentsMap) {
        if (!key.startsWith(`${name}-`)) {
          continue;
        }
        // 如果layout所有字段完全没变化，忽略
        if (!Object.keys(layout).some((key: keyof TComponentLayout) => comInfo.layout[key] !== layout[key])) {
          // resolve(true);
          continue;
        }
        hasComp = true;
        comInfo.layout = Object.assign(comInfo.layout, layout);
        comInfo.update = true;
        comInfo.resolves.push(resolve);
      }
      resolve(true);
      if (hasComp) {
        this._updateAllComponents();
      }
    });
  }

  /**
   * 移除组件
   * @param name  组件名称
   * @param label 标识
   *
   */
  public removeComponent(name: string, label = 'default'): Promise<boolean> {
    // this._debug('removeComponent', `${name}-${label}`);
    return new Promise<boolean>((resolve) => {
      const comInfo = this._getComponentInfo(name, label);
      comInfo.remove = true;
      comInfo.create = false;     // 清除未执行的添加组件任务
      comInfo.resolves.push(resolve);
      this._updateAllComponents();
    });
  }

  /**
   * 获取组件
   * @param name 组件名称
   * @param label 标识
   */
  public getComponent(name: string, label = 'default'): HTMLElement {
    return this._getComponentInfo(name, label).dom;
  }

  /**
   * 等待组件加载完成
   * @param name 组件名称
   * @param label 标识
   */
  public async waitComponentLoaded(name: string, label = 'default'): Promise<ComponentInfo> {
    const comp = this._getComponentInfo(name, label);
    if (comp.dom) {
      return comp;
    }
    return new Promise((resolve) => {
      comp.resolves.push((resolved) => {
        if (resolved && comp.dom) {
          resolve(comp);
        } else {
          throw new Error(`component ${name}-${label} load failed`);
        }
      });
    });
  }

  /**
   * 获取组件布局
   * @param name 组件名称
   * @param label 标识
   */
  public getComponentLayout(name: string, label = 'default'): TComponentLayout {
    return this._getComponentInfo(name, label).layout;
  }

  /**
   * 获取组件客户区矩形
   * @param name 组件名称
   * @param label 标识
   * @return {DOMRect} 组件客户区矩形
   */
  public getComponentClientRect(name: string, label = 'default'): DOMRect {
    const comInfo = this._getComponentInfo(name, label);
    if (!comInfo.rect) {
      comInfo.updateStatus();
    }
    return comInfo.rect;
  }

  /**
   * 手动更新组件客户区矩形
   * @param name 组件名称
   * @param label 标识
   * @return {DOMRect} 组件客户区矩形
   */
  public updateComponentClientRect(name: string, label = 'default'): DOMRect {
    const comInfo = this._getComponentInfo(name, label);
    comInfo.updateStatus();
    return comInfo.rect;
  }

  /**
   * 检查组件是否可见状态
   * @param {string} name   组件名称
   * @param {string} label  标识
   * @return {boolean} 组件是否处于可见状态
   */
  public isComponentVisible(name: string, label = 'default'): boolean {
    return this._getComponentInfo(name, label).visible;
  }

  /**
   * 启用或关闭组件DOM节点的拖动功能
   * @param {string} name   组件名称
   * @param {string} label  标识
   * @param draggable       是否启用拖动
   * @param domSelector     要启用拖动功能的DOM节点选择器
   * @param draggableRect   可拖动区域，可传入矩形或函数，若传入函数类型，则每次用户拖动会调用该方法获取新的可拖动区域限制
   */
  public toggleComponentDraggable(
    name: string,
    label = 'default',
    draggable = true,
    domSelector = '.drag-module-header__wrap',
    draggableRect: DOMRect | (() => DOMRect) = null,
    forceUpdate: true,
  ) {
    const comInfo = this._getComponentInfo(name, label);
    if (!comInfo.loaded) {  // 未加载的组件禁止开启拖动功能
      this._error('toggleComponentDraggable', `component not found: ${comInfo.fullName()}`);
      return;
    }
    comInfo.toggleComponentDraggable(draggable, domSelector, draggableRect, forceUpdate);
  }

  /**
   * 添加组件事件监听
   * @param {string} name   组件名称
   * @param {ComponentEventListener} listener 事件监听器
   * @param {string} label  标识
   */
  public addComponentEventListener(name: string, listener: ComponentEventListener, label = 'default') {
    const comInfo = this._getComponentInfo(name, label);
    comInfo.listeners.push(listener);
  }

  /**
   * 移除组件事件监听
   * @param {string} name   组件名称
   * @param {ComponentEventListener} listener 事件监听器
   * @param {string} label  标识
   */
  public removeComponentEventListener(name: string, listener: ComponentEventListener, label = 'default') {
    const comInfo = this._getComponentInfo(name, label);
    const index = comInfo.listeners.findIndex(item => item === listener);
    if (index !== -1) {
      comInfo.listeners = comInfo.listeners.splice(index, 1);
    }
  }

  /**
   * 获取严重错误发生次数
   */
  public getSeriousErrorTimes(): number {
    return TSession.instance.getSeriousErrorTimes();
  }

  /**
   * 检查指定功能特性是否可用
   * @param feature
   */
  public isFeatureAvailable(feature: string | CustomFeature): boolean {
    if (feature in this._packageFeatureConfig) {
      // 是受套餐限制的功能，先检查套餐配置
      if (!this._packageFeatureConfig[feature]) {
        return false;
      }
    }
    // 套餐有功能权限，或者不受套餐限制的功能，才走后面的逻辑
    if (this._localFeatureMap.has(feature)) {
      return this._localFeatureMap.get(feature);
    }
    const schoolInfo = TSession.instance.getSchoolInfo();
    if (!schoolInfo || !schoolInfo.featureBlackList) {
      return false;
    }
    return !schoolInfo.featureBlackList.includes(feature);
  }

  /**
   * 开启/关闭指定功能特性
   * @param feature 指定功能特性
   * @param available 是否开启: true 开启, false 关闭
   */
  public setFeatureAvailable(feature: string | CustomFeature, available: boolean) {
    this._localFeatureMap.set(feature, available);
  }

  /**
   * 上台多媒体设备配置
   * @param option 是否自动打开摄像头，麦克风
   */
  public setStageUpMediaOption(option: {
    micAutoOpen: boolean,
    cameraAutoOpen: boolean
  }) {
    this._stage_media_option = {
      ...this._stage_media_option,
      ...option,
    };
  }

  /**
   * 获取上台多媒体设备配置
   * @returns 上台设置
   */
  public getStageMediaOption() {
    return this._stage_media_option;
  }

  /**
   * 初始化(Demo演示用)
   * @param customJS   自定义 JS
   * @param customCSS  自定义 CSS
   */
  public init(customJS: string, customCSS: string) {
    Object.keys(this._componentsMap).forEach((component) => {
      this.loadComponent(component).then();
    });
    // 加载自定义Javascript
    this._loadJS('school', customJS).then(() => {
      this._info('init', `load javascript ${customJS} completed`);
    });

    // 加载自定义CSS
    this._loadCSS(customCSS);

    // 移动端上报onPageFinished
    if (TSession.instance.isMobileNative()) {
      TWebView.instance.call('os', 'onPageFinished');
    }
  }

  /**
   * 显示异常通知，建议使用showErrorMsgBox，参数更明确
   * @param message 消息内容
   * @param detail  异常详情
   * @param title   消息标题
   * @param report  是否上报异常
   * @param buttons 按钮列表
   * @param callback 弹窗关闭回调
   */
  public showErrorMsg(
    message: string,
    detail: string = undefined,
    title = '',
    report = true,
    buttons: string[] = null,
    callback: (index: number, options: boolean[]) => void = null,
  ) {
    this._error('showErrorMsg', `msg: ${message}, detail:${detail}, ${title}, buttons:${buttons?.join('|') || '-'}`);
    const exceptionId = '';
    if (report) {
      // exceptionId = `${TSession.instance.getGlobalRandom()}_${Math.floor(Math.random() * 1024)}`;
      this._reportException(exceptionId, { msg: message }, detail, true);
      this._reportEvent('exception', { message, detail }, 0, 'error');
    }
    // else {
    //   this._error('showErrorMsg', `msg: ${message}`);
    // }
    if (!buttons) {
      buttons = [this.getNameConfig().roomInfo.leaveRoom];
    }
    if (!callback) {
      callback = this.unInitialize.bind(this);
    }

    // 没上报过进房log，上报
    if (!TSession.instance.isLogFlushed()) {
      TSession.instance.addLog(`showErrorMsg ${title} ${message}`);
      this._flushLog();
    }
    if (!message && !detail) {
      message = `${i18next.t('当前页面出错')} (Lcic: 1011)`;
    }

    this.showMessageBox(title, message, buttons, callback, [], exceptionId);
    // TODO: ?
    // this._quitClass();      // 出现异常时先退房后提示
  }

  /**
   * 显示异常通知
   * @param errorMsgBoxParams showErrorMsg 参数的 Object 形式
   */
  public showErrorMsgBox({
    title = '',
    message = '',
    detail = undefined,
    module = '',
    code = null,
    report = true,
    buttons = null,
    callback = null,
  }: {
    title?: string;
    message?: string;
    detail?: string;
    module?: string; // 上报的模块名称，例如Board, IM, TRTC_Web, 可以为空
    code?: number; // 错误码，可以为空
    report?: boolean;
    buttons?: string[];
    callback?: (index: number, options: boolean[]) => void;
  }) {
    const defaultTitle = i18next.t('异常提示');
    title = title || defaultTitle;
    module = module || 'TCIC';
    if (message && code) { // 增加上报模块名称以及code
      message = `${message} (${module}: ${code})`;
    }
    this.showErrorMsg(message, detail, title, report, buttons, callback);
  }

  /**
   * 展示弹窗通知。该方法向UI层发送一个 Show_Msg_Box 事件，弹窗如何展示取决于UI层实现。
   * @param  title    消息标题
   * @param  message  消息内容
   * @param  buttons  按钮列表
   * @param  callback 弹窗关闭回调
   * @param  options  可选项列表
   * @param  exceptionId 异常ID
   * @param  isSafeInput 是否安全的输入
   * @param  msgBoxType  弹窗类型
   */
  public showMessageBox(
    title: string,
    message: string,
    buttons: string[],
    callback: (index: number, options: boolean[]) => void,
    options: object[] = [],
    exceptionId = '',
    isSafeInput = false,
    msgBoxType: 'dialog' | 'actionSheet' = 'dialog',
  ) {
    this._curDialogId += 1;
    TEvent.instance.notify(TMainEvent.Show_Msg_Box, {
      msgBoxId: this._curDialogId,
      title,
      message,
      buttons,
      callback,
      options,
      exceptionId,
      isSafeInput,
      msgBoxType,
    });
    return this._curDialogId;
  }

  public getSession() {
    return TSession.instance;
  }
  /**
   * 获取当前弹窗 ID
   */
  public getCurMessageBoxId() {
    return this._curDialogId;
  }

  /**
   * 关闭弹窗通知。该方法向UI层发送一个 Close_Msg_Box 事件，弹窗如何关闭取决于UI层实现。
   * @param msgBoxId  弹窗 ID
   */
  public closeMessageBox(msgBoxId: number) {
    msgBoxId = msgBoxId || this._curDialogId;  // 取当前弹窗ID为默认值
    TEvent.instance.notify(TMainEvent.Close_Msg_Box, { msgBoxId });
  }

  /**
   * 忽略移动端返回按钮事件
   * @param flag 是否忽略移动端返回按钮事件
   */
  public ignoreBackPressed(flag: boolean) {
    this._isIgnoreBackPressed = flag;
  }

  /**
   * getOrientationAngle
   */
  public getOrientationAngle(): Number {
    this._info('getOrientationAngle', `${window.orientation},${window?.screen?.orientation?.angle}`);
    return window?.screen?.orientation?.angle || window.orientation;
  }

  /**
   * getOrientationType
   */
  public getOrientationType(): OrientationType | undefined {
    this._info('getOrientationType', `${window?.screen?.orientation?.type}`);
    return window?.screen?.orientation?.type;
  }

  /**
   * 初始化
   * @param boardInitParams 白板初始化参数，至少需要包含id字段，指定用于渲染白板的DOM节点
   * @param options 其他参数，例如 webUIVersion
   */
  public initialize(boardInitParams: TBoardInitParams = null, options?: { webUIVersion?: string }) {
    let onPageFinishedCalled = false;
    try {
      this._boardInitParams = boardInitParams;
      // 注册状态，只有当前模块可写
      TState.instance.registerState(TMainState.School_Info_Ready, '加载学校信息完成', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Class_Info_Ready, '加载课堂信息完成', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Joined_Class, '已加入课堂', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Class_Status, '课堂状态', -1, [this.constructor.name]);
      TState.instance.registerState(TMainState.Joined_TRTC, '已加入trtc房间', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Class_Layout, '课堂布局', TClassLayout.Top, [this.constructor.name]);
      TState.instance.registerState(TMainState.Class_Duration, '课堂持续时间', 0, [this.constructor.name]);
      TState.instance.registerState(TMainState.Like_Count, '点赞数量', 0, [this.constructor.name]);
      TState.instance.registerState(TMainState.Member_Count, '成员数量', 0, [this.constructor.name]);
      TState.instance.registerState(TMainState.Board_Permission, '白板操作权限', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Screen_Share_Permission, '屏幕分享权限', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Mute_All, '全员静音', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Mute_Video_All, '全员视频', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Silence_All, '全员禁言', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Silence_Mode, '全员禁言状态', TClassSilenceMode.Free_Chat, [this.constructor.name]);
      TState.instance.registerState(TMainState.Network_Broken, '网络异常', false);
      TState.instance.registerState(TMainState.Network_Quality_Status, '网络质量状态', TNetworkQuality.Excellent);

      TState.instance.registerState(TMainState.Stage_Status, '上台状态', false, [this.constructor.name]);

      // 视频
      TState.instance.registerState(TMainState.Video_Device_Status, '本地视频设备状态', TDeviceStatus.Unknown);
      TState.instance.registerState(TMainState.Video_Capture, '本地视频采集', false);
      TState.instance.registerState(TMainState.Video_Publish, '本地视频推流', false);
      TState.instance.registerState(TMainState.Reporting_Camera_State, '本地视频cameraState上报值', TDeviceStatus.Closed);

      // 音频
      TState.instance.registerState(TMainState.Audio_Device_Status, '本地音频设备状态', TDeviceStatus.Unknown);
      TState.instance.registerState(TMainState.Audio_Capture, '本地音频采集', false);
      TState.instance.registerState(TMainState.Audio_Publish, '本地音频推流', false);
      TState.instance.registerState(TMainState.Reporting_Mic_State, '本地音频micState上报值', TDeviceStatus.Closed);

      // 屏幕共享
      TState.instance.registerState(TMainState.Screen_Share, '屏幕共享', 2);
      TState.instance.registerState(TMainState.Vod_Play, '视频播放状态', 2);
      TState.instance.registerState(TMainState.Sub_Camera, '辅助摄像头播放状态', 2);
      TState.instance.registerState(TMainState.Music_Play, '音乐播放状态', 2);
      TState.instance.registerState(TMainState.Reporting_Screen_State, 'screenState上报值', 2);

      // 课堂页面可见性
      TState.instance.registerState(TMainState.Class_Visible, '课堂页面可见性', true);

      TState.instance.registerState(TMainState.Vod_Time_Update, '视频时间更新', {
        duration: 0,
        currentTime: 0,
      });
      TState.instance.registerState(TMainState.Vod_Time_Seeked, '视频时间跳转', {
        duration: 0,
        currentTime: 0,
      });
      const defaultDeviceOrientation = Number(this.getParams('defaultDeviceOrientation', TDeviceOrientation.Portrait));
      TState.instance.registerState(TMainState.Device_Orientation, '设备方向', defaultDeviceOrientation);

      // 直播课，请求上麦逻辑
      TState.instance.registerState(TMainState.Enable_Stage, '开启上麦', false, [this.constructor.name]);
      TState.instance.registerState(TMainState.Wait_Stage_UserId, '同意连麦用户', '', [this.constructor.name]);
      // 直播课，请求上麦逻辑
      TState.instance.registerState(TMainState.Stage_Count, '公开课连麦人数', 0, [this.constructor.name]);
      TState.instance.registerState(TMainState.RTC_Mode, '是否RTC模式', true);
      TState.instance.registerState(TMainState.Project_Core_Process, '项目核心流程', '');

      TBusinessCommand.instance.init(); // 初始化TCommand模块
      TState.instance.registerState(TMainState.Invite_Stage_Status, '公开课老师邀请上台状态', false);
      // 课堂页面可见性监听
      this._addClassVisibleChangeListener();

      TState.instance.subscribeState(TMainState.Class_Status, (status) => {
        // 更新课堂信息的status字段
        const classInfo = this.getClassInfo();
        if (classInfo) {
          classInfo.status = status;
          TSession.instance.setClassInfo(classInfo);
          // 退房
          if (status === TClassStatus.Has_Ended) {
            this._quitClass().then();
          } else if (status === TClassStatus.Has_Expired) {
            this._quitClass().then();
          }
          // 课堂状态变化后马上更新课堂时长定时器
          this._updateClassDuration();
        }
      }, { capture: true });

      // 音视频看不到听不到的兜底逻辑, trtc监听到远端音视频改变时, 若permissions不存在,通过接口重新拉取permissions.
      const createFailoverHandler = (event: string) => (info: { userId: string; available: boolean; }) => {
        if (!info) {
          return;
        }
        // 延时检查permissions是否符合预期
        setTimeout(() => {
          try {
            const myUserId = TSession.instance.getUserId();
            if (!info.userId.startsWith('tic_push_user') && !info.userId !== myUserId
              && info.available) {
              const permission = this.getPermission(info.userId);
              console.log('%c [ remote av Changed ]-1489', 'font-size:13px; background:pink; color:#bf2c9f;', event, permission);
              if ((event === TTrtcEvent.Audio_Changed && !permission?.mic)
                || (event === TTrtcEvent.Video_Changed && !permission?.camera)
                || (event === TTrtcEvent.SubStream_Changed && !permission?.screen)
              ) {
                this._getPermissionList(event);
              }
            }
          } catch (error) {
            this._error('failover_error', TUtil.getErrorDetailMessage(error));
          }
        }, 1500);
      };
      const option = { capture: true, noEmitWhileSubscribe: true, noEmitWhileRegister: true };
      TState.instance.subscribeState(
        TTrtcEvent.Audio_Changed,
        createFailoverHandler(TTrtcEvent.Audio_Changed), option,
      );
      TState.instance.subscribeState(
        TTrtcEvent.Video_Changed,
        createFailoverHandler(TTrtcEvent.Video_Changed), option,
      );
      TState.instance.subscribeState(
        TTrtcEvent.SubStream_Changed,
        createFailoverHandler(TTrtcEvent.SubStream_Changed), option,
      );


      // 初始化日志模块
      const cfg = new TLoggerConfig();
      cfg.schoolId = TSession.instance.getSchoolId();
      cfg.roomId = TSession.instance.getClassId();
      cfg.userId = TSession.instance.getUserId();
      cfg.language = TSession.instance.getLanguage();
      cfg.globalRandom = TSession.instance.getGlobalRandom();
      cfg.sessionId = TSession.instance.getSessionID();
      cfg.webSdkVersion = this.getVersion();
      if (options?.webUIVersion) {
        cfg.webUIVersion = options.webUIVersion;
      }
      TLogger.getInstance('TCIC').setConfig(cfg);
      TLogger.getInstance('TCICUI').setConfig(cfg);

      // 处理窗口大小变化事件
      let resizeProcessing = false;
      window.addEventListener('resize', () => {
        if (resizeProcessing) return;
        resizeProcessing = true;
        window.requestAnimationFrame(() => {
          this._updateAppClientRect();
          resizeProcessing = false;
        });
      });

      // 捕获断网事件
      window.addEventListener('online', () => {
        TState.instance.setState(TMainState.Network_Broken, false, this.constructor.name, true, 'online');
      });
      window.addEventListener('offline', () => {
        TState.instance.setState(TMainState.Network_Broken, true, this.constructor.name, true, 'offline');
      });

      this._info('initialize', `_platformInfo: ${JSON.stringify(this._platformInfo)}, url: ${location.href}`);

      // 移动端上报onPageFinished
      if (TSession.instance.isMobileNative()) {
        TWebView.instance.call('os', 'onPageFinished', {
          webSdkVersion: this.getVersion(),
          forceUpdateFunc: TWebView.instance.generateStaticCb('forceUpdateFunc', (params) => {
            this._info('forceUpdateFunc', JSON.stringify(params));
            TEvent.instance.notify(TMainEvent.Version_Update, params);
          }),
        });
        onPageFinishedCalled = true;
        TWebView.instance.call('os', 'getNativeParams', {}, (params) => {
          this._clientInfo = params.retval;
          cfg.nativeSdkVersion = this._clientInfo.sdkVersion;
          TLogger.getInstance('TCIC').setConfig(cfg);
          TWebView.instance.setClientInfo(this._clientInfo);
          if (TWebView.instance.inTVList()) {
            TTrtc.instance.setLocalVideoParams({});
          }
        });
        TWebView.instance.call('trtc', 'initSDK', {
          globalrandom: TSession.instance.getGlobalRandom(),
        });
        TWebView.instance.registerMsgRecv((event: string, message: any) => {
          this._info('mobile-native-event', `recv webview msg ${event}: ${JSON.stringify(message)}`);
          TEvent.instance.notify(event, message);
        });
        TWebView.instance.registerNativeRecv((event: any) => {
          this._info('mobile-native-event', `recv native msg: ${JSON.stringify(event)}`);
          TEvent.instance.notify(TMainEvent.Recv_Custom_Msg, event);
        });
        TWebView.instance.registerKeyBoardEvent((event: any) => {
          this._info('mobile-native-event', `recv keyboard msg: ${JSON.stringify(event)}`);
          TEvent.instance.notify(TMainEvent.Soft_Keyboard, {
            kbHeight: event.kbHeight,
          });
        });
        TEvent.instance.on(TMainEvent.Back_Pressed, () => {
          if (!this._isIgnoreBackPressed) {
            // 直接发起离开课堂处理
            this.leaveClass();
          }
        });
      } else if (TSession.instance.isElectron()) {
        if (window.Electron) {
          if (window.Electron.hasOwnProperty('getClientInfo')) {
            this._clientInfo = window.Electron.getClientInfo();
            cfg.nativeSdkVersion = this._clientInfo.sdkVersion;
            TLogger.getInstance('TCIC').setConfig(cfg);
          }
          if (window.Electron.hasOwnProperty('onPageFinished')) {
            window.Electron.onPageFinished({ webSdkVersion: this.getVersion() });
          }
        }
      }

      this._processIMMsg();

      // 上报加载耗时
      const webLoad = TSession.instance.getIntParams('webloadstamp', new Date().getTime() + 100);
      this.reportEvent('load_class', {}, 0, webLoad);
      TSession.instance.clearTimeStamp();

      // 劫持trtc console.info
      if (TSession.instance.isWeb()) {
        TSession.instance.injectConsoleInfoForTRTC();
      }

      return Promise.all([i18nextPromise, errorCodePromise]).then(() => {
        // 为什么要获取又设置回去？
        const lang = TSession.instance.getLanguage();
        this.setLng(lang);

        const i18nHasLangData = !!i18next.getDataByLanguage(lang);
        if (!i18nHasLangData) {
          this._info(
            'messageLanguageError',
            `sdk, lang ${lang}, i18nLang ${i18next.language}, i18nHasLangData ${lang} ${i18nHasLangData}`,
          );
        }
        // 参数检测
        if (TSession.instance.getClassId() && TSession.instance.getUserId() && TSession.instance.getToken()) {
          this.setEnv(TSession.instance.getEnv());
          this._infoWithLog('initialize', `env: ${TBusiness.instance.getHost()}, version: ${this.getVersion()}, lang ${lang}, i18nLang ${i18next.language}`);
          this.setCookie();
          if (TSession.instance.isMobileNative() || TSession.instance.isElectron()) {
            return this._getInfoAndJoinClass();
          }
          const isNeedLocation = TSession.instance.getParams('location', false);
          return this._loadTrtcLibs()
            .then(() => new Promise((resolve, reject) => {
              if (isNeedLocation && navigator.geolocation) {
                navigator.geolocation.getCurrentPosition((pos) => { // 获取位置信息
                  this._position = {
                    latitude: pos.coords.latitude,
                    longitude: pos.coords.longitude,
                  };
                  resolve(pos);
                }, (err) => {
                  console.log(err, '获取定位信息失败');
                  this._position = null;
                  resolve(err);
                });
              } else {
                resolve(null);
              }
            }))
            .then(() => this._getInfoAndJoinClass());
        }
        let errDetail = i18next.t('缺少重要参数');
        if (!TSession.instance.getClassId()) {
          errDetail = i18next.t('缺少classid参数');
        } else if (!TSession.instance.getUserId()) {
          errDetail = i18next.t('缺少userid参数');
        } else if (!TSession.instance.getToken()) {
          errDetail = i18next.t('缺少token参数');
        }
        const errMessage = `${i18next.t('加入{{arg_0}}遇到问题，请尝试重新进入', { arg_0: this.getRoleInfo().roomInfo.name })}:${errDetail}`;
        this._warn('initialize', `error: ${errMessage}; version: ${this.getVersion()}; origin: ${TSession.instance.getParams('originurl')}`);
        return Promise.reject(new TCICError(-2, errMessage, errDetail));
      });
    } catch (error) {
      console.error('%c [ error ]-1554', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      this._error('TMain-initialize', JSON.stringify(error));
      throw error;
    } finally {
      // ios说这个不调会弹框报错 请重新进入课堂. 保证会调用到.
      if (TSession.instance.isMobileNative() && !onPageFinishedCalled) {
        TWebView.instance.call('os', 'onPageFinished', {
          webSdkVersion: this.getVersion(),
          forceUpdateFunc: TWebView.instance.generateStaticCb('forceUpdateFunc', (params) => {
            this._info('forceUpdateFunc', JSON.stringify(params));
            TEvent.instance.notify(TMainEvent.Version_Update, params);
          }),
        });
      }
    }
  }

  /**
   * 结束课堂会话(离开课堂)
   */
  public unInitialize(): Promise<void> {
    this.reportEvent('quit_class', {});
    this._removeClassVisibleChangeListener();
    return this._quitClass()
      .then(() => {
        this._closeWindow();
      })
      .catch(() => {
        this._closeWindow();
      });
  }

  /**
   * 刷新课堂
   */
  public async reloadClass() {
    const enterRtcState = this.getState(TMainState.Joined_TRTC, false);
    this.reportEvent('reload_class', { enterRtcState });
    if (!enterRtcState) {
      this._reloadClass();
    } else {
      try {
        await TTrtc.instance.quit();
        TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
      } catch (err) {
        this._warn('reloadClass', `quitRtcRoom failed: ${err.toString()}`);
      }
      // 资源释放需要一点时间，这里加上500ms延迟，否则可能导致页面crash
      await TUtil.sleep(500);
      this._reloadClass();
    }
  }

  /**
   * 获取当前权限列表
   * @returns PermissionInfo[]  权限列表数组
   */
  public getPermissionList() {
    return this._permissionList;
  }

  /**
   * 弹窗初始化
   */
  public initSubWindowSelf() {
    TSession.instance.setParams('webview', 'sub');
    this._loadSessionInfo();
    // 初始化日志模块
    const cfg = new TLoggerConfig();
    cfg.schoolId = TSession.instance.getSchoolId();
    cfg.roomId = TSession.instance.getClassId();
    cfg.userId = TSession.instance.getUserId();
    cfg.language = TSession.instance.getLanguage();
    cfg.globalRandom = TSession.instance.getGlobalRandom();
    cfg.sessionId = TSession.instance.getSessionID();
    cfg.webSdkVersion = SDK_MAIN_VERSION;
    TLogger.getInstance('TCIC').setConfig(cfg);
    this.setEnv(TSession.instance.getEnv());
    this.setLng(TSession.instance.getLanguage());
    // 加载定制样式
    const customInfo = TSession.instance.getSchoolInfo().customContent;
    if (TSession.instance.getDebugCSS()) {
      this._loadCSS(TSession.instance.getDebugCSS());
    } else if (customInfo && customInfo.css && customInfo.css.length > 0) {
      this._loadCSS(customInfo.css);
    }
    if (TSession.instance.getDebugJS()) {
      this._loadJS('school', TSession.instance.getDebugJS()).then();
    } else if (customInfo && customInfo.js && customInfo.js.length > 0) {
      this._loadJS('school', customInfo.js).then();
    }

    // 移动端上报onPageFinished
    if (TSession.instance.isMobileNative()) {
      TWebView.instance.call('os', 'onPageFinished');
      TWebView.instance.registerMsgRecv((event: string, message: any) => {
        TEvent.instance.notify(event, message);
      });
      TWebView.instance.registerKeyBoardEvent((event: any) => {
        TEvent.instance.notify(TMainEvent.Soft_Keyboard, {
          kbHeight: event.kbHeight,
        });
      });
    } else {
      (window as any).onWebViewMsg = (event: string, message: any) => {
        TEvent.instance.notify(event, message);
      };
    }
  }

  /**
   * 弹出二级页面
   * @param url 地址
   * @param name 名称
   * @param params 参数
   * @param callback 回调
   */
  public showSubWindow(url: string, name: string, params: any, callback: (code: number, data: any) => void) {
    const platform = TSession.instance.getPlatform();
    let copyParams = `platform=${platform}&env=${TSession.instance.getEnv()}&scene=${TSession.instance.getSessionID()}`;
    this._info('showSubWindow', `url:${url}, copyParams: ${copyParams}, name: ${name}, params: ${params}`);
    if (TSession.instance.isMobileNative()) {
      copyParams += `&device=${TSession.instance.getDevice()}`;
      const loadPath = `${url}?${copyParams}&methodName=initial&componentName=${name}&params=${encodeURI(JSON.stringify(params))}`;
      TWebView.instance.showSubWindow(loadPath, new DOMRect(), callback);
    } else {
      const loadPath = `${url}?${copyParams}&methodName=initial&componentName=${name}&params=${JSON.stringify(params)}`;
      this._loadIFrame(loadPath);
    }
  }

  /**
   * 关闭二级页面自己(由二级页面调用)
   */
  public closeSubWindowSelf() {
    this._info('closeSubWindowSelf', 'enter');
    if (TSession.instance.isMobileNative()) {
      TWebView.instance.closeWebViewSelf();
    } else {
      (window as any).closeCustomIframe();
    }
  }

  /**
   * 发送消息到Native
   * @param message 消息内容
   */
  public sendCustomMessage(message: any) {
    if (TSession.instance.isElectron()) {
      window.Electron.sendCustomMessage(message);
    } else {
      TWebView.instance.sendCustomMessage(message);
    }
  }

  public getPermission(userId: string): TPermissionInfo {
    const arr = this._permissionList.filter(item => item.userId === userId);
    if (arr.length > 0) {
      return arr[0];
    }
    const result = new TPermissionInfo();
    result.userId = userId;
    if (userId === 'mix') {   // 课堂混流虚拟用户
      const classStarted = this.getState(TMainState.Class_Status) === TClassStatus.Already_Start;
      result.mic = classStarted;
      result.camera = classStarted;
    }
    return result;
  }

  /**
   * 设置水印参数
   * @param {TSchoolWaterMark} param  水印参数
   */
  public setWaterMarkParam(param: TSchoolWaterMark) {
    this._waterMark = param;
    this._useCustomWaterMark = true;      // 使用自定义水印(覆盖学校水印信息)
    this._info('setWaterMarkParam', `param: ${JSON.stringify(param)}`);
    this.notify(TMainEvent.WaterMark_Update, this.getWaterMarkParam());
  }

  /**
   * 获取水印参数
   */
  public getWaterMarkParam() {
    return Object.assign({}, this._waterMark);
  }

  /**
   * 设置跑马灯参数
   * @param {TSchoolWaterMark} param  跑马灯参数
   */
  public setMarqueeParam(param: TClassMarquee) {
    this._marquee = param;
    this._info('setMarqueeParam', `param: ${JSON.stringify(param)}`);
    this.notify(TMainEvent.Marquee_Update, this.getMarqueeParam());
  }

  /**
   * 获取跑马灯参数
   */
  public getMarqueeParam() {
    return Object.assign({}, this._marquee);
  }

  /**
   * 获取SDK版本信息
   * @return {string} 版本号
   */
  public getVersion(): string {
    return __TCIC_SDK_VERSION__ || SDK_MAIN_VERSION;
  }

  /**
   * 获取SDK不带构建号的版本 (web未使用)
   * @return {string} 版本
   */
  public getSDKVersion(): string {
    return __TCIC_SDK_VERSION__ || SDK_MAIN_VERSION;
  }

  /**
   * 获取客户端信息
   */
  public getClientInfo() {
    return this._clientInfo;
  }

  /**
   * 设置环境（内部接口）
   * @return {string} 环境标识
   */
  public setEnv(env: string) {
    TSession.instance.setEnv(env);
  }
  /**
   * 设置语言 (内部接口)
   * @param lng  语言 zh-CN或en
   */
  public setLng(lng: string) {
    const oldLng = this.getLanguage();
    TSession.instance.setLng(lng);
    const newLng = this.getLanguage();
    // 初始化快捷回复词条
    let currentLngWordsArr: string[];
    switch (lng) {
      case 'zh':
        currentLngWordsArr = ['YYDS', '求大纲', '听懂了', '没问题', '老师讲的不错', '老师真棒', '666', '1'];
        break;
      case 'en':
        currentLngWordsArr = [];
        break;
      case 'zh-TW':
        currentLngWordsArr = [];
        break;
      case 'ko':
        currentLngWordsArr = [];
        break;
      case 'ja':
        currentLngWordsArr = [];
        break;
      default:
        currentLngWordsArr = [];
    }
    this.setQuickIMWords(currentLngWordsArr);
    TLogger.getInstance('TCIC').setConfig({ language: newLng });
    TLogger.getInstance('TCICUI').setConfig({ language: newLng });
    this._info('setLng', `old: ${oldLng}, param: ${lng}, now: ${newLng}`);
    this.notify(TMainEvent.Language_Update, newLng);
  }

  /**
   * 事件上报
   * @param name  事件名称
   * @param data  事件参数
   * @param code  事件返回错误码
   * @param timestamp 事件开始时间戳
   * @param level 日志级别
   */
  public reportEvent(name: string, data: any, code = 0, timestamp = 0, level?: any) {
    if (timestamp) {
      TLogger.getInstance('TCIC').logStart(name, timestamp);
      TLogger.getInstance('TCIC').logEnded(name, TLevel.INFO, {
        module: 'event_report',
        action: name,
        ext: window.location.href,
        code,
        param: JSON.stringify(data),
      });
    } else {
      const logLevel = level || 'info';
      this._reportEvent(name, data, code, logLevel);
    }
  }

  public objToString(obj: any) {
    try {
      return JSON.parse(obj);
    } catch (error) {
      try {
        return TUtil.getErrorDetailMessage(obj);
      } catch (error) {
        return String(obj);
      }
    }
  }

  /**
   * 日志上报
   * @param action  动作名称
   * @param message 消息内容
   */
  public reportLog(action: string, message: string, opts?: TLoggerParam) {
    if (!opts) {
      opts = {};
    }
    TLogger.getInstance('TCICUI').info({
      module: 'TCICUI',
      action,
      param: message,
      ...opts,
    });
  }

  /**
   * 异常上报
   * @param id 异常id
   * @param data 异常数据
   */
  public reportException(id: string, data: any) {
    this._reportException(id, data);
  }

  /* =========================================｜业务逻辑接口｜===========================================*/

  /* ===========｜成员操作｜==============*/

  /**
   * 清空举手成员列表
   */
  public clearHandUpMemberList() {
    return TStore.instance.clearHandUpMemberList();
  }
  /**
   * 删除举手成员列表
   */
  public removeHandUpMember(userIdList: string[]) {
    return TStore.instance.removeHandUpMember(userIdList);
  }

  /**
   * 更新成员列表过滤参数
   * @param {TMemberListFilter}       filter 过滤参数
   */
  public updateMemberListFilter(filter: TMemberListFilter) {
    TStore.instance.updateMemberListFilter(filter);
  }

  /**
   * 获取成员列表
   * @param {TMemberListFilter}       filter 过滤参数
   */
  public getClassMemberList(filter: TMemberListFilter): Promise<TGetMemberListResult> {
    const token = TSession.instance.getToken();
    const classId = TSession.instance.getClassId();
    return TBusinessMember.instance.getClassMemberList(classId, filter, token);
  }

  /**
   * 获取举手列表
   * @param {TMemberListFilter}       filter 过滤参数
   */
  public getHandUpList(filter: THandUpListFilter): Promise<TGetHandUpListResult> {
    return TStore.instance.getHandUpMemberList(filter);
  }
  /**
   * 点赞接口
   * @param {classId}                 classId    课堂 ID
   * @param {likeNumber}              likeNumber 增加的点赞数
   */
  public memberLike(
    classId: number,
    likeNumber: number,
  ): Promise<null> {
    const token = TSession.instance.getToken();
    return TBusinessMember.instance.memberLike(classId, likeNumber, token);
  }

  /**
   * 加入课堂
   * @param {number}                 classId  课堂 ID
   * @param {number}                 platform 平台
   * @param {number}                 device   设备类型
   * @param {number}                 role     角色
   * @param {string}                 version  版本号
   */
  public memberJoin(
    classId: number,
    platform: number,
    device: number,
    role: number,
    version: string,
  ): Promise<TMemberJoinResult> {
    const token = TSession.instance.getToken();
    return TBusinessMember.instance.memberJoin(classId, token, platform, device, role, version);
  }

  /**
   * 成员操作
   * @param {TMemberActionParam}  action   操作
   */
  public memberAction(action: TMemberActionParam): Promise<void> {
    const token = TSession.instance.getToken();
    action.classId = TSession.instance.getClassId();

    const afterSuccess = () => {
      if (action.actionType === TMemberActionType.Hand_Up) {  // 自己举手后马上发一个举手事件
        const owner = this.getPermission(TSession.instance.getUserId());
        TEvent.instance.notify(TMainEvent.Member_Hand_Up, [{
          userId: owner.userId,
          stage: owner.stage,
        }, {}], true, true);
      } else if (action.actionType === TMemberActionType.Hand_Up_Cancel) {  // 自己取消举手后马上发一个举手事件
        const owner = this.getPermission(TSession.instance.getUserId());
        TEvent.instance.notify(TMainEvent.Member_Hand_Up_Cancel, [{
          userId: owner.userId,
          stage: owner.stage,
        }, {}], true, true);
      } else if (action.actionType === TMemberActionType.Silence) {
        TIM.instance.silenceUserOnMsgList(action.userId, true);
      } else if (action.actionType === TMemberActionType.Silence_Cancel) {
        TIM.instance.silenceUserOnMsgList(action.userId, false);
      }
    };

    const memberActionDone = () => {
      switch (action.actionType) {
        case TMemberActionType.None:
          break;
        case TMemberActionType.Camera_Open:
          this.reportEvent('invite_camera', action);
          break;
        case TMemberActionType.Camera_Close:
          this.reportEvent('remove_camera', action);
          break;
        case TMemberActionType.Mic_Open:
          this.reportEvent('invite_mic', action);
          break;
        case TMemberActionType.Mic_Close:
          this.reportEvent('remove_mic', action);
          break;
        case TMemberActionType.Camera_Mic_Open:
          this.reportEvent('invite_camera', action);
          this.reportEvent('invite_mic', action);
          break;
        case TMemberActionType.Camera_Mic_Close:
          this.reportEvent('remove_camera', action);
          this.reportEvent('remove_mic', action);
          break;
        case TMemberActionType.Hand_Up: {
          this.reportEvent('hand_up', action);
          break;
        }
        case TMemberActionType.Hand_Up_Cancel:
          this.reportEvent('hand_down', action);
          break;
        case TMemberActionType.Kick_Out:
          this.reportEvent('kick_user', action);
          break;
        case TMemberActionType.Board_Enable:
          this.reportEvent('invite_board', action);
          break;
        case TMemberActionType.Board_Disable:
          this.reportEvent('remove_board', action);
          break;
        case TMemberActionType.Silence:
          this.reportEvent('mute_msg', action);
          break;
        case TMemberActionType.Silence_Cancel:
          this.reportEvent('unmute_msg', action);
          break;
        case TMemberActionType.Sub_Camera:
          this.reportEvent('sub_camera', action);
          break;
        case TMemberActionType.Vod_Play:
          this.reportEvent('vod_play', action);
          break;
        case TMemberActionType.Screen_Share_Open:
          this.reportEvent('invite_share', action);
          break;
        case TMemberActionType.Screen_Share_Close:
          this.reportEvent('remove_share', action);
          break;
        case TMemberActionType.Stage_Up:
          this.reportEvent('stage_up', action);
          // 触发一个事件，用于将用户移出举手列表
          this.notify(TMainEvent.Member_Stage_Up, action);
          break;
        case TMemberActionType.Stage_Down:
          // 触发一个事件，用于将用户移出举手列表
          this.notify(TMainEvent.Member_Stage_Down, action);
          this.reportEvent('stage_down', action);
          break;
        case TMemberActionType.Kick_Out_Forever:
          this.reportEvent('kick_user_forever', action);
          break;
      }
      return TBusinessMember.instance.memberAction(action, token)
        .then((res) => {
          afterSuccess();
          return Promise.resolve(res);
        })
        .catch((error) => {
          this.reportEvent('member_action', error);
          console.warn('action error', error);
          if (error.errorCode === 10605  // 摄像头已开启，忽略
            || error.errorCode === 10606  // 麦克风已开启，忽略
            || error.errorCode === 10607  // 屏幕分享已开启，忽略
            || error.errorCode === 10610  // 屏幕分享已关闭，忽略
          ) {
            afterSuccess();
            return Promise.resolve();
          }
          return Promise.reject(error);
        });
    };
    /**
     * 用户下台，关闭用户麦，关闭用户视频，需要二次确认，
     * 避免误操作引起异常
     */
    const forceConfirmAction = [{
      action: TMemberActionType.Stage_Down,
      desc: i18next.t('确认终止与此用户连麦？'),
    }, {
      action: TMemberActionType.Camera_Close,
      desc: i18next.t('确认关闭此用户摄像头？'),
    },
    {
      action: TMemberActionType.Mic_Close,
      desc: i18next.t('确认关闭此用户麦克风？'),
    }];
    const actionItem = forceConfirmAction.find(item => item.action === action.actionType);
    console.log('ActionTmain:');
    if (actionItem) {
      return new Promise((resolve) => {
        TCIC.SDK.instance.showMessageBox(
          '',
          actionItem.desc,
          [i18next.t('确定'), i18next.t('取消')],
          (index) => {
            console.log('ActionTmain:', index);
            if (index === 0) {
              resolve(memberActionDone());
            } else {
              resolve();
            }
          },
        );
      });
    }


    return memberActionDone();
  }

  /**
   * 退出
   * @param {number}                 classId  课堂 ID
   */
  public memberQuit(classId: number): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessMember.instance.memberQuit(classId, token);
  }

  /**
   * 开启循环上台
   * @param interval  间隔
   * @param member_number  上台人数
   */
  public startStageLoop(interval: number, memberNumber: number) {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessMember.instance.startStageLoop(classId, token, interval, memberNumber);
  }

  /**
   * 停止循环上台
   */
  public stopStageLoop() {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessMember.instance.stopStageLoop(classId, token);
  }

  /**
   * 更新循环上台参数
   * @param interval  间隔
   * @param member_number  上台人数
   */
  public updateStageLoop(interval: number, memberNumber: number) {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessMember.instance.updateStageLoop(classId, token, interval, memberNumber);
  }

  /**
   * 锁定/解锁循环上台用户
   * @param userIds  用户id列表
   * @param lock 是否锁定
   */
  public lockStageLoop(userIds: string[], lock: number) {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessMember.instance.lockStageLoop(classId, token, userIds, lock);
  }

  /* ===========｜文档操作｜==============*/
  /**
   * 添加课件
   * @param {TDocumentInfo}           document 文档信息
   */
  public createDocument(document: TDocumentInfo): Promise<TCreateDocumentResult> {
    const token = TSession.instance.getToken();
    return TBusinessDocument.instance.createDocument(document, token);
  }

  /**
   * 删除课件
   * @param {string}                docId    文档 ID
   */
  public deleteDocument(docId: string): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessDocument.instance.deleteDocument(docId, token);
  }

  /**
   * 查询课件
   * @param {string}              docId    文档 ID
   */
  public getDocumentInfo(docId: string): Promise<TDocumentInfo> {
    const token = TSession.instance.getToken();
    return TBusinessDocument.instance.getDocumentInfo(docId, token);
  }

  /**
   * 批量查询课件
   * @param {string[]}              docIds    文档ID列表
   */
  public getDocumentListInfo(docIds: string[]): Promise<TBatchDocumentInfo> {
    const token = TSession.instance.getToken();
    return TBusinessDocument.instance.getDocumentListInfo(docIds, token);
  }

  /**
   * 查询课件列表
   * @param {TGetDocumentListParam}    documentParam    查询参数
   */
  public getDocumentList(documentParam: TGetDocumentListParam): Promise<TGetDocumentListResult> {
    const token = TSession.instance.getToken();
    return TBusinessDocument.instance.getDocumentList(documentParam, token);
  }

  /**
   * 关联课件到课堂
   * @param {string} docId  课件id
   */
  public bindDocumentToClass(docId: string): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessDocument.instance.bindDocumentToClass(docId, classId, token);
  }

  /**
   * 取消关联课件到课堂
   * @param {string} docId  课件id
   */
  public unbindDocumentToClass(docId: string): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessDocument.instance.unbindDocumentToClass(docId, classId, token);
  }

  /**
   * 上传课件
   * @param {File}                    file             上传的文件
   * @param {number}                  schoolId         学校 ID
   * @param {TUploadReadyCallback}    readyCallback    准备上传回调
   * @param {TUploadProgressCallback} progressCallback 进度回调
   * @param {TUploadResponseCallback} responseCallback 结果回调
   */
  public uploadDocument(
    file: File, schoolId: number, readyCallback: TUploadReadyCallback
    , progressCallback: TUploadProgressCallback, responseCallback: TUploadResponseCallback,
  ) {
    const token = TSession.instance.getToken();
    TBusinessDocument.instance.uploadDocument(file, schoolId, token, readyCallback, progressCallback, responseCallback);
  }

  /**
   * 取消上传课件
   * @param {string} taskId 任务 ID
   */
  public cancelUploadDocument(taskId: string) {
    TBusinessDocument.instance.cancelUploadDocument(taskId);
  }

  /* ===========｜课堂操作｜==============*/
  /**
   * 获取公开课历史消息
   */
  public getClassMessageData(): Promise<TClassHistroyMessage> {
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.getClassMessageData(
      TSession.instance.getClassId(),
      token,
      TSession.instance.getUserId(),
    );
  }

  /**
   * 查询课堂详情
   */
  public getClassInfo(): TClassInfo {
    return TSession.instance.getClassInfo();
  }

  /**
   * 发送课后评价
   */
  public submitClassRate(score: number, scoreMsg: string) {
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.submitClassRate({
      classId: TSession.instance.getClassId(),
      userId: this.getUserId(),
      score,
      scoreMsg,
    }, token);
  }

  /**
   * 初始化课堂与角色名称信息，需要在课堂信息ready之前
   */
  public initNameConfig(customJson: { [lng: string]: any }) {
    if (!customJson) {
      return;
    }
    if (TState.instance.getState(TMainState.Class_Info_Ready)) {
      this._warn('initNameConfig', 'can not initNameConfig after Class_Info_Ready');
      return;
    }
    const oldNameConfig = this.getNameConfig();
    TSession.instance.initNameConfig(customJson);
    const newNameConfig = this.getNameConfig();
    this._info('initNameConfig', `old: ${JSON.stringify(oldNameConfig)}\ncustomJson: ${JSON.stringify(customJson)}\nnow: ${JSON.stringify(newNameConfig)}`);
    this.notify(TMainEvent.Name_Config_Update, newNameConfig);
  }

  /**
   * 获取课堂与角色名称信息
   */
  public getNameConfig() {
    return TSession.instance.getNameConfig();
  }

  /**
   * 获取课堂与角色名称信息，命名容易误解，请使用 getNameConfig
   */
  public getRoleInfo(): any {
    return TSession.instance.getRoleInfo();
  }

  /**
   * 查询自定义userId
   */
  public getUid(): string {
    return TSession.instance.getUid();
  }

  /**
   * 查询自定义classId
   */
  public getCid(): string {
    return TSession.instance.getCid();
  }


  /**
   * 开始上课
   */
  public startClass(): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    this.reportEvent('start_class', { classId });
    return TBusinessClass.instance.startClass(classId, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        if (classInfo.realStartTime === 0) {
          classInfo.realStartTime = Math.round(+new Date() / 1000.0);  // 先取本地时间作为课堂开始时间
          TSession.instance.setClassInfo(classInfo);
        }
        TState.instance.setState(
          TMainState.Class_Status,
          TClassStatus.Already_Start,
          this.constructor.name,
        );
        return Promise.resolve();
      })
      .catch(error => Promise.reject(error));
  }

  /**
   * 停止上课
   */
  public async endClass(): Promise<void> {
    const noEndClass = TSession.instance.getParams('noEndClass');
    if (noEndClass) {
      this._warn('endClass', `no endclass but ${noEndClass}`);
      return;
    }
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    this.reportEvent('end_class', { classId });
    const ret = await TBusinessClass.instance.endClass(classId, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        if (classInfo.realEndTime === 0) {
          classInfo.realEndTime = Math.round(+new Date() / 1000.0);  // 先取本地时间作为课堂结束时间
          TSession.instance.setClassInfo(classInfo);
        }
        // this.unInitialize();
        localStorage.removeItem(this.getUserId());
        localStorage.getItem('onlineElIdMap') && localStorage.removeItem('onlineElIdMap'); // 清除 H5 课件标题 map
        // TState.instance.setState(
        //   TMainState.Class_Status,
        //   TClassStatus.Has_Ended,
        //   this.constructor.name,
        // );
        return Promise.resolve();
      });
    await TLogger.flushReport();
    return ret;
  }

  /**
   * 请求退出课堂。该方法向UI层发送一个 Leave_Class 事件，由UI层自行决定如何处理该请求。
   */
  public async leaveClass() {
    TEvent.instance.notify(TMainEvent.Leave_Class, {});
    await TLogger.flushReport();
  }

  /**
   * 检查操作权限
   * @param path 资源路径
   * @param permission 权限标识
   */
  public checkPermission(path: TResourcePath, permission: TPermissionFlag): boolean {
    return TRBAC.instance.checkPermission(path, permission);
  }

  /**
   * 全体禁言
   * @param silence  是否禁言
   */
  public silenceAll(silence: boolean): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    if (silence) {
      this.reportEvent('mute_all_msg', {});
    } else {
      this.reportEvent('unmute_all_msg', {});
    }
    const silenceAll = silence ? 1 : 0;
    return TBusinessClass.instance.silenceAll(classId, silenceAll, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        classInfo.silenceAll = silenceAll;
        TSession.instance.setClassInfo(classInfo);
        TState.instance.setState(TMainState.Silence_All, silence, this.constructor.name);
        return Promise.resolve();
      });
  }

  /**
   * 设置全员发言状态
   * @param {number} silence_mode  0：自由聊天 1：仅允许公开聊天 2：仅允许私聊 3：全员禁言
   */
  public setSilenceMode(silence_mode: number): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    this.reportEvent('setSilenceMode', { silence_mode });
    return TBusinessClass.instance.setSilenceMode(classId, silence_mode, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        classInfo.silenceMode = silence_mode;
        TSession.instance.setClassInfo(classInfo);
        TState.instance.setState(TMainState.Silence_Mode, silence_mode, this.constructor.name);
        return Promise.resolve();
      });
  }
  /**
   * 开/关 请求上麦逻辑
   * @param enable true : 开启； false：关闭
   */
  public setEnableStage(enable: boolean): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    if (enable) {
      this.reportEvent('enable_stage_msg', {});
    } else {
      this.reportEvent('disable_stage_msg', {});
    }
    const stage = enable ? 1 : 0;
    return TBusinessClass.instance.setEnableStage(classId, stage, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        classInfo.enableStage = stage;
        TSession.instance.setClassInfo(classInfo);
        TState.instance.setState(TMainState.Enable_Stage, enable, this.constructor.name);
        return Promise.resolve();
      });
  }
  /**
   * 发送命令
   * @param {TCommandReq} req 请求
   */
  public sendCommand(req: TCommandReq): Promise<TCommandReqResult> {
    const classId = TMain.instance.getClassInfo().classId;
    const token = TSession.instance.getToken();
    TMain.instance.reportEvent('sendCommand', req.serialize);
    return TBusinessCommand.instance.sendCommand(classId, token, req);
  }
  public setCommandStatus(cmdID: TCommandID, name: string, status: TCommandStatus) {
    return TBusinessCommand.instance.setCommandStatus(cmdID, name, status);
  }
  /**
   * 获取用户的命令及其状态
   */
  public getUserCommand(): Promise<TGetUserCommandReqResult> {
    const classId = TMain.instance.getClassInfo().classId;
    const token = TSession.instance.getToken();
    TMain.instance.reportEvent('getUserCommand', { classId });
    return TBusinessCommand.instance.getUserCommand(classId, token);
  }
  /**
   * 获取命令对应的全部数据
   * @param cmd
   */
  public getAllCommand(cmd: TCommandID): Promise<void> {
    const classId = TMain.instance.getClassInfo().classId;
    const token = TSession.instance.getToken();
    TMain.instance.reportEvent('getAllCommand', { classId });
    return TBusinessCommand.instance.getAllCommand(classId, token, cmd);
  }

  /**
   * 进入音视频房间
   */
  public enterRtcRoom(rtcMode = false, retryType?: string): Promise<void> {
    const enterRtcState = this.getState(TMainState.Joined_TRTC, false);
    // 方便查log时用 trtc* 过滤
    const infoAction = 'trtc.enterRtcRoom';
    this._info(infoAction, `enter with rtcMode: ${rtcMode} in state ${enterRtcState}, retryType ${retryType || ''}`);
    const schoolInfo = this.getSchoolInfo();
    const classInfo = this.getClassInfo();
    const userSig = TSession.instance.getUserSig();
    const trtcMode = rtcMode ? TTrtcMode.RTC : TTrtcMode.Live;
    this._isAnchor = trtcMode === TTrtcMode.RTC;

    TState.instance.setState(TMainState.Joined_TRTC, 'entering', this.constructor.name);
    let trtcInited = false;
    const costActionLabel = 'trtc.enterCost';
    this._logStart(costActionLabel);
    return TTrtc.instance.init(schoolInfo.sdkAppId, TSession.instance.getUserId(), userSig, trtcMode)
      .then(() => {
        trtcInited = true;
        this._info(infoAction, 'trtc init success');

        const trtcJoin = () => {
          this._info(infoAction, `trtc join, isAnchor ${this._isAnchor}`);
          if (schoolInfo.schoolId === 3066394) {   // 3066394 TODO 等产品策略确认后去掉。
            if (!TSession.instance.isTeacher()) {  // 老师不启用帧率自适应调整
              TAV.instance.enableVideoParamsAutoAdjust();
            }
          } else {
            // 弱网情况下设置流畅优先
            TTrtc.instance.setNetworkQosParam(TRTCVideoQosPreference.TRTC_VIDEO_QOS_PREFERENCE_SMOOTH);
            TAV.instance.enableVideoParamsAutoAdjust();// 所有用户都帧率自适应
          }
          // for test
          // const mockError = Number(TSession.instance.getParams('mockenterrtcerror'));
          // if (mockError && retryType !== 'userRetry') {
          //   console.error(infoAction, '==== 模拟加入音视频房间失败');
          //   const err = new TCICError(-1, '模拟加入音视频房间失败');
          //   return Promise.reject(err)
          //     .catch((err: TCICError) => {
          //       this._error(infoAction, `trtc join failed: ${err.toString()}`);
          //       TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
          //       throw err;
          //     });
          // }

          if (this.isRtmpMode()) {
            TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
            return Promise.resolve();
          }

          return TTrtc.instance.join(classInfo.classId, this._isAnchor)
            .then(() => {
              this._logEnded(costActionLabel, TLevel.INFO, {
                module: this._getClassName(),
                action: costActionLabel,
                param: 'trtc join success',
              });
              this._info(infoAction, 'trtc join success');
              TState.instance.setState(TMainState.Joined_TRTC, true, this.constructor.name);
            })
            .catch((err: TCICError) => {
              this._logEnded(costActionLabel, TLevel.ERROR, {
                module: this._getClassName(),
                action: costActionLabel,
                param: 'trtc join failed',
              });
              // 异常时增加上报trtc本地日志 begin
              TSession.instance.setReportTRTCLog(true);
              TSession.instance.setLogFlushed(false);
              // 异常时增加上报trtc本地日志 end
              this._error(infoAction, `trtc join failed: ${err.errorCode} | ${err.errorMsg} | ${err.errorDetail}`);
              this._isAnchor = false; // 失败重置trtc角色
              TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
              return Promise.reject(this.getTCICErrorInstance(-1, i18next.t('加入音视频房间失败'), i18next.t('加入音视频房间失败'), 'TRTC'));
            });
        };
        let baseBitrateLevel = 0;
        let highBitrateLevel = 1;
        const  { cameraFPS, cameraBitrate, cameraResolution, classSubType, maxRtcMember  } = classInfo;
        let fps = cameraFPS;
        // 直播课提升分辨率等级
        if (this.isLiveClass()) {
          baseBitrateLevel = 1;
          highBitrateLevel = 2;
          fps = 10;
        }
        if (classInfo.customData.hasOwnProperty('base_bitrate_level')) {
          baseBitrateLevel = parseInt(classInfo.customData.base_bitrate_level, 10);
          baseBitrateLevel = Math.min(Math.max(baseBitrateLevel, 0), 10);
        }
        if (classInfo.customData.hasOwnProperty('high_bitrate_level')) {
          highBitrateLevel = parseInt(classInfo.customData.high_bitrate_level, 10);
          highBitrateLevel = Math.min(Math.max(highBitrateLevel, 0), 10);
        }

        let resolutionMode: 0 | 1 = 0;
        if (this.isPortraitClass() && this.isPortrait()) {
          resolutionMode = 1;
        }

        if (classInfo.customData.hasOwnProperty('disable_bitrate_adjust') || !this.isFeatureAvailable('EnableBitrateAdjust')) {
          const resolution = TTrtc.getVideoResolutionFromCameraResolution(cameraResolution);
          this._info('trtc.setVideoEncoderParam', `disable bitrate adjust, ${JSON.stringify({
            cameraResolution,
            videoResolution: resolution,
            fps,
            bitrate: cameraBitrate,
            expectFps: cameraFPS,
          })}`);
          TTrtc.instance.setVideoEncoderParam(resolution, fps, cameraBitrate, resolutionMode).then();
        } else {
          this._info('trtc.setLocalVideoEncodeParams', `enable bitrate adjust, ${JSON.stringify({
            cameraResolution,
            fps,
            baseBitrateLevel,
            highBitrateLevel,
          })}`);

          const autoReduceResolution = false;

          /**
           * 开启自动降低分辨率
           */
          console.log('classInfos: autoReduceResolution', autoReduceResolution);
          TAV.instance.setLocalVideoEncodeParams(
            // 用于设置视频流的分辨率（长宽比）
            cameraResolution,
            fps,
            baseBitrateLevel,
            highBitrateLevel,
            resolutionMode,
            autoReduceResolution,
          );
        }
        if (TSession.instance.isMobileNative()) {
          // 移动端直接加入TRTC房间
          return trtcJoin();
        }
        // 其它端确保开始上课后再去加入TRTC房间
        TState.instance.setState(TMainState.Joined_TRTC, 'waiting', this.constructor.name);
        this.promiseState(TMainState.Class_Status, TClassStatus.Already_Start).then(() => {
          TState.instance.setState(TMainState.Joined_TRTC, 'entering', this.constructor.name);
          trtcJoin().catch((error) => {
            this._handleEnterRtcRoomFail(error, rtcMode, retryType);
          });
        });
        return Promise.resolve();
      })
      .catch((err) => {
        // init/join 失败都会到这里
        if (!trtcInited) {
          this._error(infoAction, `trtc init failed: ${err.errCode} | ${err.errorMsg} | ${err.errorDetail}`);
          this._isAnchor = false; // 失败重置trtc角色
          TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
        }
        const tcicError = this.getTCICErrorInstance(err.errorCode, err.errorMsg, i18next.t(trtcInited ? '加入音视频房间失败' : '初始化实时音视频失败'), 'TRTC');
        this._handleEnterRtcRoomFail(tcicError, rtcMode, retryType);
        return Promise.reject(tcicError);
      });
  }

  /**
   * 退出音视频房间
   */
  public quitRtcRoom(): Promise<void> {
    const enterRtcState = this.getState(TMainState.Joined_TRTC, false);
    this._info('trtc.quitRtcRoom', `quit in state ${enterRtcState}`);
    if (!enterRtcState) {
      return Promise.resolve();
    }
    return TTrtc.instance.quit().then(() => {
      this._isAnchor = false;     // 重置角色
      TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
    });
  }

  /**
   * 是否已进入音视频房间
   */
  public isInRtcRoom() {
    return this.getState(TMainState.Joined_TRTC) === true;
  }

  /**
   * 学生直播课上台
   * @param {any} param 待传参数
   */
  public approveToStage(param: any): Promise<void> {
    const isStudent = !(this.isTeacher() || this.isSupervisor());
    const isLive = this.isLiveClass();
    if (isLive && isStudent) {
      return this.enterRtcRoom(true);
    }
    return Promise.resolve();
  }
  /**
   * @param {any} param 待传参数
   */
  public hangupToStage(param: any): Promise<void> {
    const onCancelDone = (): Promise<void> => {
      if (this._isJoinedClass) {
        return this.quitRtcRoom();
      }
      return Promise.resolve();
    };

    // 发送取消请求
    const req = new TCommandReq();
    req.cmd = TCommandID.Stage;
    // req.userId = item.userId; // 请求连麦不用填userId
    req.classId = this.getClassInfo().classId;
    req.type = TCommandStatus.Cancel;
    return this.sendCommand(req).then((result) => {
      console.log('===>>> : 主动取消上台');
      onCancelDone();
    })
      .catch((error) => {
        console.log('===>>> : 主动取消上台');
        onCancelDone();
      });
  }
  /**
   * 全员静音
   * @param mute     是否静音
   */
  public muteAll(mute: boolean): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    const muteAll = mute ? 1 : 0;
    if (mute) {
      this.reportEvent('mute_all_mic', {});
    } else {
      this.reportEvent('unmute_all_mic', {});
    }
    return TBusinessClass.instance.muteAll(classId, muteAll, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        classInfo.muteAll = muteAll;
        TSession.instance.setClassInfo(classInfo);
        TState.instance.setState(TMainState.Mute_All, mute, this.constructor.name);
        return Promise.resolve();
      });
  }

  /**
   * 全员视频
   * @param mute     是否关闭视频
   */
  public muteVideoAll(mute: boolean): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    const muteAll = mute ? 1 : 0;
    if (mute) {
      this.reportEvent('mute_video_all', {});
    } else {
      this.reportEvent('unmute_video_all', {});
    }
    return TBusinessClass.instance.muteVideoAll(classId, muteAll, token)
      .then(() => {
        const classInfo = this.getClassInfo();
        classInfo.muteVideoAll = muteAll;
        TSession.instance.setClassInfo(classInfo);
        TState.instance.setState(TMainState.Mute_Video_All, mute, this.constructor.name);
        return Promise.resolve();
      });
  }

  /**
   * 撤回消息，仅老师可以撤回
   * @param {number}             messageSeq 消息 Seq
   */
  public revokeMessage(messageSeq: number): Promise<void> {
    const token = TSession.instance.getToken();
    const classId = this.getClassInfo().classId;
    return TBusinessClass.instance.revokeMessage(classId, messageSeq, token);
  }

  /**
   * @param {TClassLayout}    layout     课堂布局
   * @param {sync} 是否要同步到远端 true : 同步, false 不同步
   */
  public setClassLayout(layout: TClassLayout, sync = true): Promise<void> {
    if (!sync) {
      TState.instance.setState(TMainState.Class_Layout, layout, this.constructor.name);
      return Promise.resolve();
    }
    const token = TSession.instance.getToken();
    const classInfo = this.getClassInfo();
    classInfo.layout = layout;
    return TBusinessClass.instance.setLayout(classInfo.classId, layout, token)
      .then(() => {
        // 保存状态并更新
        TSession.instance.setClassInfo(classInfo);
        this._updateClassLayout();
        return Promise.resolve();
      });
  }

  /**
   * @return {TClassLayout}   layout     课堂布局
   */
  public getClassLayout(): TClassLayout {
    const { fixedLayout, defaultLayout } = this.getClassLayoutConfig();
    if (fixedLayout) {
      // 课堂有固定布局
      return fixedLayout;
    }
    const paramLayout = this.getParams('layout', '');
    const classInfo = this.getClassInfo();
    if (this.isUnitedClass()) {
      return classInfo.layout || (CheckValueInEnum(TClassLayout, paramLayout) ? paramLayout : defaultLayout);
    }
    let layout = defaultLayout;
    const customData = JSON.parse(JSON.stringify(classInfo.customData));
    if (classInfo.customData.hasOwnProperty('layout')) {
      const isStudent = !this.isTeacher() && !this.isSupervisor() && !this.isAssistant();
      layout = classInfo.customData.layout;
      if (layout === TClassLayout.Double && isStudent) {
        // 学生端不支持双排布局
        layout = TClassLayout.Top;
      }
    } else if (customData.hasOwnProperty('class_layout')) {
      layout = classInfo.customData.class_layout;
      if (layout === TClassLayout.Top || layout === TClassLayout.Right) {
        if (this.isLiveClass()) {
          // 支持老版本公开课
          layout = TClassLayout.VideoDoc;
        }
      }
    }
    return layout;
  }

  /**
   * 当前课堂的布局配置
   */
  public getClassLayoutConfig(): {
    fixedLayout: TClassLayout | '';
    defaultLayout: TClassLayout;
  } {
    if (this._classLayoutConfig) {
      return this._classLayoutConfig;
    }
    const classInfo = this.getClassInfo();
    let fixedLayout: TClassLayout | '' = '';
    let defaultLayout: TClassLayout = TClassLayout.Top;
    switch (classInfo.roomType) {
      case TRoomType.Big:
        // 大班课
        if (this.isVideoOnlyClass()) {
          // 纯视频
          fixedLayout = TClassLayout.VideoIM;
        } else {
          // 视频+文档，老师固定三分布局，学生单流用三分布局，混流用纯视频布局
          fixedLayout = TClassLayout.Three;
          if (!this.isTeacherOrAssistant() && classInfo.audienceMixType === TAudienceMixType.Mix) {
            fixedLayout = TClassLayout.Video;
          }
        }
        defaultLayout = fixedLayout;
        break;
      case TRoomType.Small:
      default:
        // 小班课
        if (this.isCoTeachingClass()) {
          // 双师课默认九宫格
          defaultLayout = TClassLayout.CTGrid9;
        } else if (this.isVideoOnlyClass()) {
          // 纯视频
          fixedLayout = TClassLayout.Video;
          defaultLayout = fixedLayout;
        } else {
          // 视频+文档
          if (this.isOneOnOneClass()) {
            // 1v1课
            fixedLayout = TClassLayout.Three;
            defaultLayout = fixedLayout;
          } else if (this.isLiveClass()) {
            // 直播课
            defaultLayout = TClassLayout.VideoDoc;
          } else {
            // 其他课默认顶部布局
            defaultLayout = TClassLayout.Top;
          }
        }
        break;
    }
    this._classLayoutConfig = {
      fixedLayout,
      defaultLayout,
    };
    this._info('classLayoutConfig', `roomType ${classInfo.roomType}, classSubType ${classInfo.classSubType}, maxRtcMember ${classInfo.maxRtcMember}, classLayoutConfig ${JSON.stringify(this._classLayoutConfig)}`);
    return this._classLayoutConfig;
  }

  public isClassLayoutFixed() {
    return !!this.getClassLayoutConfig().fixedLayout;
  }

  public getClassLayoutMainFrame(layout?: TClassLayout) {
    const classLayout = layout || this.getClassLayout();
    return TClassLayoutMainFrameMap[classLayout] || TClassLayoutMainFrameMap.default;
  }

  public isClassLayoutHasDoc(layout?: TClassLayout) {
    return this.getClassLayoutMainFrame(layout).board;
  }

  /* ===========｜学校接口｜==============*/
  /**
   * 查询学校信息
   */
  public getSchoolInfo() {
    return TSession.instance.getSchoolInfo();
  }


  /* ===========｜用户接口｜==============*/
  /**
   * 获取用户信息
   * @param {string}               userId   用户 ID
   * @return Promise<TUserInfo>
   */
  public getUserInfo(userId: string): Promise<TUserInfo> {
    if (this._memberInfoList.has(userId)) {
      return Promise.resolve(this._memberInfoList.get(userId));
    }

    const promise = new TPromise();
    if (this._queryMap.has(userId)) {
      this._queryMap.get(userId).push(promise);       // 添加到用户请求列表
    } else {
      this._queryMap.set(userId, [promise]);
    }
    if (!this._queryTimer) {          // 查询定时器不存在
      this._queryTimer = window.setTimeout(() => {
        const tmpMap = this._queryMap;    // 保存缓存信息
        this._queryTimer = 0;     // 重置缓存信息
        this._queryMap = new Map();
        const token = TSession.instance.getToken();
        /**
         * 尝试规避userIds传入空函数
         */
        const userIds = Array.from(tmpMap.keys()).filter(item => !!item);
        TBusinessUser.instance.getUserList(Array.from(tmpMap.keys()), token)
          .then((result: TGetUserListResult) => {
            result.users.forEach((info) => {
              this._memberInfoList.set(info.userId, info);
              const arr = tmpMap.get(info.userId);
              if (arr) {
                arr.forEach((promise: any) => {
                  promise._resolve(info);
                });
              }
            });
          })
          .catch((error) => {
            Array.from(tmpMap.values()).forEach((proList) => {
              proList.forEach((promise: any) => {
                promise._reject(error);
              });
            });
          });
      }, 500);
    }
    return promise.getPromise();
  }

  /**
   * 获取多个用户信息
   * @param {string[]}             userIds  用户列表
   */
  public getUserList(userIds: string[]): Promise<TGetUserListResult> {
    const users: TUserInfo[] = [];
    const reqArray: string[] = [];
    const mergeResult = new TGetUserListResult();
    mergeResult.users = users;
    for (const userId of userIds) {
      if (this._memberInfoList.has(userId)) {
        users.push(this._memberInfoList.get(userId));
      } else {
        reqArray.push(userId);
      }
    }
    if (reqArray.length > 0) {
      const token = TSession.instance.getToken();
      return TBusinessUser.instance.getUserList(reqArray, token)
        .then((result: TGetUserListResult) => {
          for (const user of result.users) {
            this._memberInfoList.set(user.userId, user);
            users.push(user);
          }
          return Promise.resolve(mergeResult);
        });
    }
    return Promise.resolve(mergeResult);
  }

  public async getUserProfile(userId: string, useCache = true): Promise<TMemberInfo> {
    if (!useCache) {
      return await TBusinessMember.instance.getMemberByUserId(userId);
    }
    let result = await TBusinessMember.instance.getMemberByUserIdFromCache(userId);
    if (!result) {
      result = await TBusinessMember.instance.getMemberByUserId(userId);
    }
    return result;
  }

  /**
   * 修改用户信息
   * @param {number}            userInfo 用户信息
   */
  public modifyUserProfile(userInfo: TUserInfo): Promise<TGetUserListResult> {
    const token = TSession.instance.getToken();
    return TBusinessUser.instance.modifyUserProfile(userInfo, token);
  }

  /**
   * 更新课堂任务
   * @param {string}                  taskId      任务 ID
   * @param {string}                  content     任务内容
   * @param {number}                  duration    任务持续时长，单位秒。-1表示不限时长。默认为-1
   * @param {boolean}                 createOnly  是否仅新建任务，如果任务已存在则失败
   * @param {string}                  bindingUser 生命周期绑定用户，如果用户离线则任务自动失效。默认为空字符串表示不绑定用户
   * @param {boolean}                 enableCallback  是否回调 TaskUpdate 事件到当前应用的回调地址
   * @return {Promise<TTaskInfo>}
   */
  // eslint-disable-next-line max-len
  public updateTask(taskId: string, content = '', duration = -1, createOnly = false, bindingUser = '', enableCallback = false): Promise<TTaskInfo> {
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.updateTask(
      classId,
      taskId, content, duration, createOnly,
      enableCallback, bindingUser, token,
    )
      .then((task: TTaskInfo) => {
        task.isSelfUpdate = true;
        this._processTask(task);  // 自己发起的任务马上处理一下
        return Promise.resolve(task);
      });
  }

  /**
   * 停止课堂任务
   * @param {string}                  taskId      任务 ID
   * @return {Promise<TTaskInfo>}
   */
  public stopTask(taskId: string): Promise<TTaskInfo> {
    if (this.getState(TMainState.Class_Status) === TClassStatus.Has_Ended) {
      // 课堂结束，任务自动失效
      return Promise.resolve(null);
    }
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.stopTask(classId, taskId, token);
  }

  /**
   * 查询课堂任务
   * @param {number}                  seq         开始的seq，返回大于seq的任务列表。最小值为0，默认为0
   * @return {Promise<TGetTaskListResult>}
   */
  public getTasks(seq: number): Promise<TGetTaskListResult> {
    if (seq <= this._lastTaskSeqOfClass) {
      const result = new TGetTaskListResult();
      result.lastSeq = this._lastTaskSeqOfClass;
      result.tasks = this._lastTaskInfos;
      return Promise.resolve(result);
    }
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.getTasks(classId, seq, token);
  }

  /**
   * 设置资源限制
   * @param {string}                  name        资源名称
   * @param {number}                  limit       限制数量
   */
  public setResourceLimit(name: string, limit: number): Promise<null> {
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.setResourceLimit(classId, name, limit, token);
  }

  /**
   * 获取资源
   * @param {string}                  name        资源名称
   */
  public requireResource(name: string): Promise<null> {
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.requireResource(classId, name, token);
  }

  /**
   * 释放资源
   * @param {string}                  name        资源名称
   */
  public releaseResource(name: string): Promise<null> {
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.releaseResource(classId, name, token);
  }

  /**
   * 获取进房RTT
   * @return {number} 服务器毫秒级时间
   */
  public getJoinRtt() {
    return this._joinRtt;
  }

  /**
   * 获取近似的当前服务器时间
   * @return {number} 服务器毫秒级时间
   */
  public getServerTimestamp() {
    return this._serverTimestampAfterJoin + (Date.now() - this._localTimestampAfterJoin);
  }

  /**
   * 获取进房服务器时间
   * @return {number} 服务器毫秒级时间
   */
  public getServerTimestampAfterJoin() {
    return this._serverTimestampAfterJoin;
  }


  /* ===========｜答题接口｜==============*/
  /**
   * 创建提问
   * @param {TCreateQuestionParam}    question 问题参数
   */
  public createQuestion(question: TCreateQuestionParam): Promise<TCreateQuestionResult> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.createQuestion(question, token);
  }

  /**
   * 回答提问
   * @param {TAnswerQuestionParam}    answer   答案
   */
  public answerQuestion(answer: TAnswerQuestionParam): Promise<TAnswerQuestionResult> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.answerQuestion(answer, token);
  }

  /**
   * 停止答题
   * @param {string}            questionId 问题ID
   */
  public stopQuestion(questionId: string): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.stopQuestion(questionId, token);
  }

  /**
   * 取消答题
   * @param {string}            questionId 问题ID
   */
  public abandonQuestion(questionId: string): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.abandonQuestion(questionId, token);
  }

  /**
   * 关闭答题
   * @param {string}            questionId 问题ID
   */
  public closeQuestion(questionId: string): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.closeQuestion(questionId, token);
  }

  /**
   * 获取提问信息
   * @param {string}                   questionId 问题ID
   */
  public getQuestionInfo(questionId: string): Promise<TGetQuestionInfoResult> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.getQuestionInfo(questionId, token);
  }

  /**
   * 获取提问结果
   * @param {string}                     questionId 问题ID
   */
  public getQuestionResult(questionId: string): Promise<TGetQuestionResultResult> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.getQuestionResult(questionId, token);
  }

  /**
   * 获取问题列表
   */
  public getQuestions(): Promise<TGetQuestionsResult> {
    const token = TSession.instance.getToken();
    const classId = TSession.instance.getClassId();
    return TBusinessExam.instance.getQuestions(classId, token);
  }

  /**
   * 获取个人问题列表
   */
  public getMyQuestions(): Promise<TGetQuestionsResult> {
    const token = TSession.instance.getToken();
    const classId = TSession.instance.getClassId();
    return TBusinessExam.instance.getMyQuestions(classId, token);
  }

  /**
   * 获取提问统计信息
   * @param {string}                   questionId 问题ID
   */
  public getQuestionStats(questionId: string): Promise<TGetQuestionStatsResult> {
    const token = TSession.instance.getToken();
    return TBusinessExam.instance.getQuestionStats(questionId, token);
  }

  /**
   * 设置翻译参数
   * @param {string}                  state      开关状态，0关闭，1开启
   * @param {string}                  lang       语言
   * @return {Promise<void>}
   */
  public setTranslate(state: number, lang: string): Promise<void> {
    const classId = this.getClassInfo().classId;
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.setTranslate(classId, state, lang, token);
  }

  /**
   * 获取翻译参数
   * @param {string}                  taskId      任务 ID
   * @return {Promise<TTaskInfo>}
   */
  public getTranslateConfig() {
    return TBusinessClass.instance.getTranslateConfig();
  }

  /**
   * @param {number}                          classId         课堂 ID
   * @param {number}                          seq             消息 seq
   * @param {string}                          from            消息发送方
   * @param {string}                          message         消息内容
   * @param {string}                          lang            语言，en表示英文，zh表示中文.....
   * @param {string}                          token           msg
   * @return {Promise<void>}
   */
  public translate(classId: number, from: string, seq: number, message: string, lang: string): Promise<void> {
    const token = TSession.instance.getToken();
    return TBusinessClass.instance.translate(classId, from, seq, message, lang, token);
  }
  /* =========================================｜音视频接口｜===========================================*/

  /**
   * 检测当前系统环境
   */
  public checkSystemRequirements() {
    return TTrtc.instance.checkSystemRequirements();
  }

  /**
   * 设备是否异常
   */
  public isDeviceAbnormal(deviceStatus: TDeviceStatus) {
    return TTrtc.isDeviceAbnormal(deviceStatus);
  }

  public isWebRTCSupport() {
    return TTrtc.instance.isWebRTCSupport();
  }

  /**
   * 设置本地视频参数
   * @param option      本地视频参数
   */
  public setLocalVideoParams(option: TTrtcLocalVideoParams): Promise<void> {
    return TTrtc.instance.setLocalVideoParams(option);
  }
  /**
   * 设置远端视频参数
   * @param option      本地视频参数
   */
  public setRemoteRenderParams(option: TTrtcLocalVideoParams, userId: string, streamType: TStreamType): Promise<void> {
    return TTrtc.instance.setRemoteVideoParams(option, userId, streamType);
  }
  /**
   * 设置用户的视频编码清晰度
   * @param {string} userId 要设置的用户ID
   * @param {boolean} highDefinition 是否启用高清晰度模式，启用高清晰度模式后，视频推流码率会增加一个级别
   *
   * 针对当前用户，影响视频推流码率
   * 针对远端用户，影响Web端码率估算精准度
   */
  public setVideoEncodeQuality(userId: string, highDefinition: boolean) {
    const classInfo = this.getClassInfo();
    if (classInfo.customData.hasOwnProperty('disable_bitrate_adjust') || !this.isFeatureAvailable('EnableBitrateAdjust')) {
      return;      // 关闭流控时什么也不做
    }
    TAV.instance.setVideoEncodeQuality(userId, highDefinition);
  }

  /**
   * 开启本地视频采集及渲染
   * @param dom         用于渲染视频画面的DOM节点
   */
  public startLocalVideo(dom: HTMLElement): Promise<void> {
    if (this.isRtmpMode()) {
      this.setState(TMainState.Video_Publish, true);
      return Promise.resolve();
    }

    const infoAction = 'trtc.startLocalVideo';
    this._info(infoAction, `enterRtcState ${this.getState(TMainState.Joined_TRTC)}, isAnchor ${this._isAnchor}`);
    if (!this._startLocalVideoPromise) {
      const promise: Promise<void> = new Promise((resolve, reject) => {
        const doStart = () => {
          TTrtc.instance.startLocalVideo(dom)
            .then(() => {
              if (this._startLocalVideoPromise === promise) {
                this._startLocalVideoPromise = null;
              }
              this._info(infoAction, 'start success');
              this.reportEvent('enable_camera', {});
              resolve();
            })
            .catch((err: TCICError) => {
              if (this._startLocalVideoPromise === promise) {
                this._startLocalVideoPromise = null;
              }
              this._error(infoAction, `start failed: ${err.errorCode} | ${err.errorMsg || err.message} | ${err.errorDetail}`);
              this.reportEvent('enable_camera', err, err.errorCode);
              reject(err);
            });
        };
        const isEnterRtcRoom = this.isInRtcRoom();
        if (!isEnterRtcRoom || this._isAnchor) {   // 未进入音视频房间或已是主播直接打开
          this._info(infoAction, 'start directly');
          doStart();
        } else {
          this._info(infoAction, 'wait switchRole');
          this._switchRole()
            .then(() => {
              this._info(infoAction, 'start after switchRole');
              doStart();
            })
            .catch((err) => {
              if (this._startLocalVideoPromise === promise) {
                this._startLocalVideoPromise = null;
              }
              this._error(infoAction, `switchRole failed: ${err.toString()}`);
              this.reportEvent('enable_camera', err, -1);
              reject(err);
            });
        }
      });
      this._startLocalVideoPromise = promise;
    } else {
      this._info(infoAction, 'use existed promise');
    }
    return this._startLocalVideoPromise;
  }

  /**
   * 修改本地视频的resolutionMode
   */
  public changeResolutionMode(resolutionMode: 0 | 1) {
    return TTrtc.instance.setVideoResolutionMode(resolutionMode);
  }

  /**
   * 关闭本地视频采集及渲染
   */
  public stopLocalVideo(): Promise<void> {
    if (this.isRtmpMode()) {
      this.setState(TMainState.Video_Publish, false);
      return Promise.resolve();
    }

    this._info('trtc.stopLocalVideo', `enterRtcState ${this.getState(TMainState.Joined_TRTC)}, isAnchor ${this._isAnchor}`);
    this._startLocalVideoPromise = null;
    this.reportEvent('disable_camera', {});
    return TTrtc.instance.stopLocalVideo();
  }

  /**
   * 控制是否屏蔽自己的视频画面，屏蔽后不推流
   * @param mute        是否屏蔽
   */
  public muteLocalVideo(mute: boolean): Promise<void> {
    return TTrtc.instance.muteLocalVideo(mute);
  }

  /**
   * 绑定远端视频画面DOM节点(有流则自动渲染)
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param fullMode    是否使用全屏模式渲染
   */
  public bindRemoteVideoDom(userId: string, type: TTrtcVideoStreamType, dom: HTMLElement, fullMode: boolean) {
    return TAV.instance.bindRemoteVideoDom(userId, type, dom, fullMode);
  }

  /**
   * 解绑远端视频画面DOM节点
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param sequence
   */
  public unbindRemoteVideoDom(userId: string, type: TTrtcVideoStreamType, sequence: number) {
    return TAV.instance.unbindRemoteVideoDom(userId, type, sequence);
  }

  /**
   * 是否屏蔽所有远端音频
   * @param {boolean} mute 是否屏蔽
   */
  public muteAllRemoteAudio(mute: boolean) {
    TAV.instance.muteAllRemoteAudio(mute);
  }

  /**
   * 禁言白板机器人的音频流
   */
  public muteTicPusherUser() {
    TAV.instance.muteTicPusherUser();
  }

  /**
   * 暂停渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  public pauseVideoRender(userId: string, type: TTrtcVideoStreamType) {
    return TTrtc.instance.pauseVideoRender(userId, type);
  }

  /**
    * 恢复渲染
    * @param userId   要处理的用户id
    * @param streamType  要处理的视频流类型
    */
  public resumeVideoRender(userId: string, type: TTrtcVideoStreamType) {
    return TTrtc.instance.resumeVideoRender(userId, type);
  }


  /**
   * 重置视频渲染
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  public resetVideoRender(userId: string, type: TTrtcVideoStreamType) {
    return TTrtc.instance.resetVideoRender(userId, type);
  }

  /**
   * 视频截图
   * @param userId      要处理的用户ID
   * @param streamType  要处理的视频流类型
   */
  public snapshotVideo(userId: string, streamType: TTrtcVideoStreamType): Promise<TTrtcSnapshot> {
    return TTrtc.instance.snapshotVideo(userId, streamType);
  }

  /**
   * 开始本地音频采集
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public startLocalAudio(dom?: HTMLElement): Promise<void> {
    if (this.isRtmpMode()) {
      this.setState(TMainState.Audio_Publish, true);
      return Promise.resolve();
    }

    const infoAction = 'trtc.startLocalAudio';
    this._info(infoAction, `enterRtcState ${this.getState(TMainState.Joined_TRTC)}, isAnchor ${this._isAnchor}`);
    if (!this._startLocalAudioPromise) {
      const promise: Promise<void> = new Promise((resolve, reject) => {
        const doStart = () => {
          TTrtc.instance.startLocalAudio(this._isHighAudioQuality(), dom)
            .then(() => {
              if (this._startLocalAudioPromise === promise) {
                this._startLocalAudioPromise = null;
              }
              this._info(infoAction, 'start success');
              this.reportEvent('enable_mic', {});
              resolve();
            })
            .catch((err: TCICError) => {
              if (this._startLocalAudioPromise === promise) {
                this._startLocalAudioPromise = null;
              }
              this._error(infoAction, `start failed: ${err.errorCode} | ${err.errorMsg || err.message} | ${err.errorDetail}`);
              this.reportEvent('enable_mic', err, err.errorCode);
              reject(err);
            });
        };
        const isEnterRtcRoom = this.isInRtcRoom();
        if (!isEnterRtcRoom || this._isAnchor) {   // 未进入音视频房间或已是主播直接打开
          this._info(infoAction, 'start directly');
          doStart();
        } else {
          this._info(infoAction, 'wait switchRole');
          this._switchRole()
            .then(() => {
              this._info(infoAction, 'start after switchRole');
              doStart();
            })
            .catch((err) => {
              if (this._startLocalAudioPromise === promise) {
                this._startLocalAudioPromise = null;
              }
              this._error(infoAction, `switchRole failed: ${err.toString()}`);
              this.reportEvent('enable_mic', err, -1);
              reject(err);
            });
        }
      });
      this._startLocalAudioPromise = promise;
    } else {
      this._info(infoAction, 'use existed promise');
    }
    return this._startLocalAudioPromise;
  }

  /**
   * 停止本地音频采集
   */
  public stopLocalAudio(): Promise<void> {
    if (this.isRtmpMode()) {
      this.setState(TMainState.Audio_Publish, false);
      return Promise.resolve();
    }

    this._info('trtc.stopLocalAudio', `enterRtcState ${this.getState(TMainState.Joined_TRTC)}, isAnchor ${this._isAnchor}`);
    this._startLocalAudioPromise = null;
    this.reportEvent('disable_mic', {});
    return TTrtc.instance.stopLocalAudio();
  }

  /**
   * 控制是否屏蔽自己的声音
   * @param mute        是否屏蔽
   */
  public muteLocalAudio(mute: boolean): Promise<void> {
    return TTrtc.instance.muteLocalAudio(mute);
  }

  /**
   * 开启音量大小回调，回调直接通过事件抛出
   * @param interval    回调间隔(最小100ms，0为关闭)
   */
  public enableVolumeEvaluation(interval: number): Promise<void> {
    return TTrtc.instance.enableVolumeEvaluation(interval);
  }

  /**
   * 开始摄像头设备测试
   * @param dom         用于渲染摄像头画面的DOM节点
   */
  public startCameraTest(dom: HTMLElement): Promise<void> {
    return TTrtc.instance.startCameraTest(dom)
      .then(() => {
        this.reportEvent('test_camera', {});
      })
      .catch((err: TCICError) => {
        this.reportEvent('test_camera', err, err.errorCode);
        return Promise.reject(err);
      });
  }

  /**
   * 停止摄像头设备测试
   */
  public stopCameraTest(): Promise<void> {
    return TTrtc.instance.stopCameraTest();
  }

  /**
   * 开始麦克风设备测试
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public startMicTest(dom?: HTMLElement): Promise<void> {
    return TTrtc.instance.startMicTest(dom)
      .then(() => {
        this.reportEvent('test_mic', {});
      })
      .catch((err: TCICError) => {
        this.reportEvent('test_mic', err, err.errorCode);
        return Promise.reject(err);
      });
  }

  /**
   * 停止麦克风设备测试
   */
  public stopMicTest(): Promise<void> {
    return TTrtc.instance.stopMicTest();
  }

  /**
   * 开启扬声器设备测试
   * @param path        要播放的声音文件路径
   */
  public startSpeakerTest(path: string): Promise<void> {
    return TTrtc.instance.startSpeakerTest(path)
      .then(() => {
        this.reportEvent('test_speaker', {});
      })
      .catch((err: TCICError) => {
        this.reportEvent('test_speaker', err, err.errorCode);
        return Promise.reject(err);
      });
  }

  /**
   * 停止扬声器设备测试
   */
  public stopSpeakerTest(): Promise<void> {
    return TTrtc.instance.stopSpeakerTest();
  }

  /**
   * 获取摄像头设备列表
   */
  public getCameras(): Promise<TTrtcDeviceInfo[]> {
    return TTrtc.instance.getCameras();
  }

  /**
   * 切换使用的摄像头设备
   * @param deviceId      要切换的设备ID
   */
  public switchCamera(deviceId: string): Promise<void> {
    TTrtc.setLocalStorageDeviceId(TTrtcDeviceType.Camera, deviceId);
    return TTrtc.instance.switchCamera(deviceId);
  }

  /**
   * 获取正在使用的摄像头设备ID
   */
  public getCameraDeviceId(): Promise<string> {
    return TTrtc.instance.getCameraDeviceId();
  }

  /**
   * 获取麦克风设备列表
   */
  public getMics(): Promise<TTrtcDeviceInfo[]> {
    return TTrtc.instance.getMics();
  }

  /**
   * 切换使用的麦克风设备
   * @param deviceId      要切换的设备ID
   */
  public switchMic(deviceId: string): Promise<void> {
    TTrtc.setLocalStorageDeviceId(TTrtcDeviceType.Mic, deviceId);
    return TTrtc.instance.switchMic(deviceId);
  }

  /**
   * 获取正在使用的麦克风设备ID
   */
  public getMicDeviceId(): Promise<string> {
    return TTrtc.instance.getMicDeviceId();
  }

  /**
   * 设置正在使用的麦克风设备音量
   * @param volume        要设置的音量大小
   */
  public setMicVolume(volume: number): Promise<void> {
    return TTrtc.instance.setMicVolume(volume);
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  public getMicVolume(): Promise<number> {
    return TTrtc.instance.getMicVolume();
  }

  /**
   * 获取正在使用的麦克风设备音量
   */
  public getRealMicVolume(): Promise<number> {
    return TTrtc.instance.getRealMicVolume();
  }

  /**
   * 获取扬声器设备列表
   */
  public getSpeakers(): Promise<TTrtcDeviceInfo[]> {
    return TTrtc.instance.getSpeakers();
  }

  /**
   * 切换使用的扬声器设备
   * @param deviceId      要切换的设备ID
   */
  public switchSpeaker(deviceId: string): Promise<void> {
    TTrtc.setLocalStorageDeviceId(TTrtcDeviceType.Speaker, deviceId);
    return TTrtc.instance.switchSpeaker(deviceId);
  }

  /**
   * 获取正在使用的扬声器设备ID
   */
  public getSpeakerDeviceId(): Promise<string> {
    return TTrtc.instance.getSpeakerDeviceId();
  }

  /**
   * 设置正在使用的扬声器设备音量
   * @param volume        要设置的音量大小
   */
  public setSpeakerVolume(volume: number): Promise<void> {
    return TTrtc.instance.setSpeakerVolume(volume);
  }

  /**
   * 获取正在使用的扬声器设备音量
   */
  public getSpeakerVolume(): Promise<number> {
    return TTrtc.instance.getSpeakerVolume();
  }

  /**
   * 检查当前平台是否支持屏幕分享
   */
  public isScreenShareSupported(): Promise<boolean> {
    return TTrtc.instance.isScreenShareSupported();
  }

  /**
   * 检查屏幕分享权限
   */
  public hasScreenCapturePermission(): Promise<boolean> {
    return TTrtc.instance.hasScreenCapturePermission();
  }

  /**
   * 检查设备权限状态
   */
  public getMacAuthStatus(type: 'camera' | 'microphone' | 'screen'): Promise<'authorized' | 'denied'> {
    return (TTrtc.instance as any).getMacAuthStatus(type);
  }

  /**
   * 请求麦克风权限
   */
  public askForMicrophoneAccess(): Promise<'authorized' | 'denied'> {
    return (TTrtc.instance as any).askForMicrophoneAccess();
  }

  /**
   * 请求摄像头权限
   */
  public askForCameraAccess(): Promise<'authorized' | 'denied'> {
    return (TTrtc.instance as any).askForCameraAccess();
  }

  /**
   * 请求屏幕分享权限
   */
  public askForScreenCaptureAccess(): Promise<'authorized' | 'denied'> {
    return (TTrtc.instance as any).askForScreenCaptureAccess();
  }

  /**
   * 获取屏幕分享屏幕采集源列表
   */
  public getScreenCaptureSources(): Promise<TScreenCaptureSourceInfo[]> {
    return TTrtc.instance.getScreenCaptureSources();
  }

  /**
   * 选择要进行屏幕分享的目标采集源
   * @param sources        要分享的采集源
   * @param captureMouse        是否捕获鼠标
   * @param highlightWindow        是否高亮选择区域
   */
  public selectScreenCaptureTarget(
    sources: TScreenCaptureSourceInfo[],
    captureMouse = true,
    highlightWindow = true,
  ): Promise<void> {
    if (TSession.instance.isElectron()) {
      window.Electron.selectScreenCaptureTarget(sources[0]);
    }
    return new Promise<void>((resolve, reject) => {
      TTrtc.instance.selectScreenCaptureTarget(sources[0], captureMouse, highlightWindow).then(() => {
        const otherSources = sources.splice(1).map(item => item.sourceId);
        if (otherSources.length > 0) {
          TTrtc.instance.addIncludedShareWindows(otherSources)
            .then(() => resolve())
            .catch(error => reject(error));
        } else {
          resolve();
        }
      })
        .catch(error => reject(error));
    });
  }

  /**
   * 获取屏幕分享的目标采集源
   */
  public getScreenCaptureTarget(): TScreenCaptureSourceInfo {
    return TTrtc.instance.getScreenCaptureTarget();
  }

  /**
   * 将指定窗口加入屏幕分享的排除列表中
   */
  public addExcludedShareWindows(sourceIds: string[]): Promise<void> {
    return TTrtc.instance.addExcludedShareWindows(sourceIds);
  }

  public screenShareClickAreaIn(event: any) {
    // console.log('%c [ event ]-158', 'font-size:13px; background:pink; color:#bf2c9f;', event?.type);
    window?.Electron?.screenShareClickAreaMouseIn?.();
  };

  public screenShareClickAreaOut(event: any) {
    // console.log('%c [ event ]-163', 'font-size:13px; background:pink; color:#bf2c9f;', event?.type);
    window?.Electron?.screenShareClickAreaMouseOut?.();
  };

  /**
   * addScreenShareClickArea
   */
  public addScreenShareClickArea(dom: Element) {
    // console.log('%c [ dom ]-3712', 'font-size:13px; background:pink; color:#bf2c9f;', dom);
    if (!dom) {
      this._error('addScreenShare', 'dom is empty');
      return;
    }
    if (!(dom as any).$clickArea) {
      dom.addEventListener('mouseenter', this.screenShareClickAreaIn);
      dom.addEventListener('mouseover', this.screenShareClickAreaIn);
      dom.addEventListener('mouseleave', this.screenShareClickAreaOut);
      dom.addEventListener('mouseout', this.screenShareClickAreaOut);
      (dom as any).$clickArea = true;
    }
  }

  /**
   * 开始屏幕分享
   * @param dom           用于渲染分享画面的DOM节点，不需要渲染可以忽略
   */
  public startScreenShare(dom?: HTMLElement, opts?: any): Promise<void> {
    this._screenSharing = true;  // 标记接口调用状态
    let retPromise: Promise<void> = null;
    let startPromise: Promise<void> = null;
    if (TSession.instance.isElectron()) {
      if (this.isCollegeClass()) {
        console.log('[ScreenComponent][SDK]', dom, opts);
        startPromise = TTrtc.instance.startScreenShare(dom, opts);
      } else {
        if (this.isFeatureAvailable('ScreenShareAdvanceMode')) {
          startPromise = new Promise((resolve) => {
            const schoolInfo = TSession.instance.getSchoolInfo();
            const classId = TSession.instance.getClassId();
            const userId = TSession.instance.getUserId();
            const userSig = TSession.instance.getUserSig();
            // 共享窗口重建时, 需要把新创建的窗口重新包含进trtc分享
            window.Electron.registerRPCMethod && window.Electron.registerRPCMethod('addIncludedShareWindows', (includeShareWindowSourceIds: string[]) => {
              TTrtc.instance.addIncludedShareWindows(includeShareWindowSourceIds);
            });
            window.Electron.startScreenShare({
              sdkAppId: schoolInfo.sdkAppId,
              roomId: classId,
              userId,
              userSig,
              followTarget: this.isFeatureAvailable('ScreenShareFollowTarget'),
            } as any).then(async (sourceIds: any) => {
              if (sourceIds.excludeShareWindowSourceIds.length !== 0) {
                await TTrtc.instance.addExcludedShareWindows(sourceIds.excludeShareWindowSourceIds);
              }
              if (sourceIds.includeShareWindowSourceIds.length !== 0) {
                await TTrtc.instance.addIncludedShareWindows(sourceIds.includeShareWindowSourceIds);
              }
              TTrtc.instance.startScreenShare(dom)
                .then(() => {
                  resolve();
                })
                .catch(() => {
                  resolve();  // 由于TRTC Bug，这里可能产生Promise pending超时的错误，直接忽略
                });
            });
          });
        } else {
          startPromise = TTrtc.instance.startScreenShare(dom)
            .catch();  // 由于TRTC Bug，这里可能产生Promise pending超时的错误，直接忽略
        }
      }
    } else {
      startPromise = TTrtc.instance.startScreenShare(dom, opts);
    }
    if (this._isAnchor) {
      retPromise = startPromise;
    } else {
      retPromise = new Promise((resolve, reject) => {
        TTrtc.instance.switchRole(true)
          .then(() => {
            this._isAnchor = true;
            startPromise
              .then(() => {
                resolve();
              })
              .catch((err) => {
                reject(err);
              });
          })
          .catch((err) => {
            reject(err);
          });
      });
    }
    if (this.isTeacher() && !TSession.instance.isWeb()) {  // 老师先邀请自己进行屏幕分享，以便后台更新状态(Web端不需要)
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Screen_Share_Open,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }
  /**
   * 暂停屏幕分享
   */
  public pauseScreenShare(): Promise<void> {
    return TTrtc.instance.pauseScreenShare();
  }

  public setSubStreamEncoderParam(params: Partial<{
    videoResolution: TTrtcVideoResolution,
    resMode: 0 | 1,
    videoFps: number,
    videoBitrate: number,
    enableAdjustRes: boolean,
    screenCaptureMode: number,
  }>): Promise<void> {
    return TTrtc.instance.setSubStreamEncoderParam(params);
  }

  /**
   * 恢复屏幕分享
   */
  public resumeScreenShare(): Promise<void> {
    return TTrtc.instance.resumeScreenShare();
  }

  /**
   *
   * 获取屏幕共享流
   */
  public getScreenShareStream() {
    return TTrtc.instance.getScreenShareStream();
  }

  /**
   * 停止屏幕分享
   */
  public stopScreenShare(): Promise<void> {
    this._screenSharing = false;  // 标记接口调用状态
    let retPromise: Promise<void>;
    if (TSession.instance.isElectron()) {
      if (this.isFeatureAvailable('ScreenShareAdvanceMode')) {
        retPromise = new Promise((resolve) => {
          window.Electron.stopScreenShare().then(() => {
            TTrtc.instance.stopScreenShare().then(() => {
              resolve();
            });
          });
        });
      } else {
        retPromise = TTrtc.instance.stopScreenShare();
      }
    } else {
      retPromise = TTrtc.instance.stopScreenShare();
    }
    if (this.isTeacher()) {  // 老师先关闭自己的屏幕分享，以便后台更新状态
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Screen_Share_Close,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }

  /**
   * 控制是否分享系统声音
   * @param enable        是否分享
   */
  public enableSystemAudioLoopback(enable: boolean): Promise<void> {
    return TTrtc.instance.enableSystemAudioLoopback(enable);
  }

  /**
   * 播放背景音乐
   */
  public startMusic(documentId: string, url: string): Promise<boolean> {
    return TTrtc.instance.startMusic(documentId, url);
  }

  /**
   * 停止背景音乐
   */
  public stopMusic(): void {
    TTrtc.instance.stopMusic();
  }

  /**
   * 暂停背景音乐
   */
  public pauseMusic(): void {
    TTrtc.instance.pauseMusic();
  }

  /**
   * 恢复背景音乐
   */
  public resumeMusic(): void {
    TTrtc.instance.resumeMusic();
  }

  /**
   * 获取音乐时长，单位毫秒
   */
  public getMusicDuration(): number {
    return TTrtc.instance.getMusicDuration();
  }

  /**
   * 设置背景音乐进度
   */
  public seekMusic(pts: number): void {
    TTrtc.instance.seekMusic(pts);
  }

  /**
   * 设置背景音乐的音量大小
   */
  public setMusicVolume(volume: number): void {
    TTrtc.instance.setMusicVolume(volume);
  }

  /**
   * 课件音频是否在播放
   */
  public isDocumentAudioPlaying(): boolean {
    return this._isDocumentAudioPlaying;
  }

  public setDocumentAudioPlaying(playing: boolean): void {
    this._isDocumentAudioPlaying = playing;
  }
  /**
   * 课件视频是否在播放
   */
  public isDocumentVideoPlaying(): boolean {
    return this._isDocumentVideoPlaying;
  }

  public setDocumentVideoPlaying(playing: boolean): void {
    this._isDocumentVideoPlaying = playing;
  }

  /**
   * 加载视频
   */
  public loadVideo(dom: HTMLElement, url: string): Promise<void> {
    const retPromise = TTrtc.instance.loadVideo(dom, url);
    if ((this.isTeacher() || this.isAssistant()) && !TSession.instance.isWeb()) {  // 老师先邀请自己进行屏幕分享，以便后台更新状态(Web端不需要)
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Vod_Play,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }

  /**
   * 开始播放视频
   */
  public playVideo(): Promise<void> {
    return TTrtc.instance.playVideo();
  }

  /**
   * 暂停播放视频
   */
  public pauseVideo(): Promise<void> {
    return TTrtc.instance.pauseVideo();
  }

  /**
   * 视频进度跳转
   */
  public seekVideo(time: number): Promise<void> {
    return TTrtc.instance.seekVideo(time);
  }

  /**
   * 设置音量大小
   */
  public setVideoVolume(volume: number): Promise<void> {
    return TTrtc.instance.setVideoVolume(volume);
  }

  /**
   * 结束播放视频
   */
  public stopVideo(): Promise<void> {
    const retPromise = TTrtc.instance.stopVideo();
    if ((this.isTeacher() || this.isAssistant()) && !TSession.instance.isWeb()) {  // 老师先关闭自己的屏幕分享，以便后台更新状态
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Screen_Share_Open,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }

  /**
   * 打开辅助摄像头
   */
  public startSubCamera(dom: HTMLElement, deviceIndex: number, resolution: TTrtcVideoResolution): Promise<void> {
    const retPromise = TTrtc.instance.startSubCamera(dom, deviceIndex, resolution);
    if ((this.isTeacher() || this.isAssistant()) && !TSession.instance.isWeb()) {  // 老师先邀请自己进行屏幕分享，以便后台更新状态(Web端不需要)
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Sub_Camera,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }

  /**
   * 关闭辅助摄像头
   */
  public stopSubCamera(): Promise<void> {
    const retPromise = TTrtc.instance.stopSubCamera();
    if ((this.isTeacher() || this.isAssistant()) && !TSession.instance.isWeb()) {  // 老师先关闭自己的屏幕分享，以便后台更新状态
      const param = {
        classId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
        actionType: TMemberActionType.Screen_Share_Open,
      };
      return this.memberAction(param).then(() => retPromise);
    }
    return retPromise;
  }

  // *******************************************美颜相关*******************************************
  /**
   * 设置虚拟背景
   * @param enable  开关
   * @param url   虚拟背景(为空时为背景虚化)
   */
  public setVirtualImg(enable: boolean, url: string = null, sceneKey: string): Promise<void> {
    return TTrtc.instance.setVirtualImg(enable, url, sceneKey);
  }

  public setAvatar(effectId: string = null, url: string = null): Promise<void> {
    return TTrtc.instance.setAvatar(effectId, url);
  }
  /**
   * 设置美颜
   * @param beauty  美颜度( 0 - 1 ，推荐为 0.5 )
   * @param brightness 	明亮度( 0 - 1 ，，推荐为 0.5 )
   * @param ruddy 红润度( 0 - 1 ，，推荐为 0.5 )
   */
  public setBeautyParam(beauty: number, brightness: number, ruddy: number): Promise<void> {
    return TTrtc.instance.setBeautyParam(beauty, brightness, ruddy);
  }

  // *******************************************降噪相关*******************************************
  /**
   * 是否支持AI降噪
   * @return {boolean}
   */
  public isAIDenoiseSupported(): boolean {
    return TTrtc.instance.isAIDenoiseSupported();
  }

  /**
   * 开启AI降噪
   * @param enable    是否开启
   */
  public enableAIDenoise(enable: boolean): Promise<void> {
    return TTrtc.instance.enableAIDenoise(enable);
  }

  /* =========================================｜IM 接口｜===========================================*/
  /**
   * 发送群组自定义消息
   * @param  {string} ext      扩展标识
   * @param  {string} data     内容
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendGroupCustomMessage(ext: string, data: string, priority?: TencentCloudChat.TYPES): Promise<TIMMsg> {
    return this.promiseState(TMainState.IM_Cmd_Ready, true)
      .then(() => TIM.instance.sendGroupCustomMessage(ext, data, priority));
  }

  /**
   * 发送群组文本消息
   * @param  {string} text     文本消息
   * @param  {object} dataExt  扩展数据，透传
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendGroupTextMessage(text: string, dataExt?: any, priority?: TencentCloudChat.TYPES): Promise<TIMMsg> {
    return TIM.instance.sendGroupTextMessage(text, dataExt, priority);
  }

  public getCurrnetImgId(): number {
    return TIM.instance.getCurrnetImgId();
  }

  /**
   * 发送群组图片消息
   * @param  {File} fileObj 图片 File 对象
   * @param  {string} fileUrl 图片 ObjectURL
   * @param  {object} reSendImgMsg 重传的msg
   * @param  {Function} progressCallback  进度回调
   * @param  {object} dataExt  扩展数据，透传
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendGroupImgMessage(
    fileObj: any,
    fileUrl: string,
    reSendImgMsg: any,
    progressCallback: TSendProgressCallback,
    dataExt?: any,
    priority?: TencentCloudChat.TYPES,
  ): Promise<TIMMsg> {
    return TIM.instance.sendGroupImgMessage(fileObj, fileUrl, reSendImgMsg, progressCallback, dataExt, priority);
  }

  /**
   * 发送群组文件消息
   * @param  {File} fileObj 图片 File 对象
   * @param  {string} fileUrl 图片 ObjectURL
   * @param  {object} reSendImgMsg 重传的msg
   * @param  {Function} progressCallback  进度回调
   * @param  {object} dataExt  扩展数据，透传
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendGroupFileMessage(
    fileObj: any,
    fileUrl: string,
    progressCallback: TSendProgressCallback,
    dataExt?: any,
  ): Promise<TIMMsg> {
    return TIM.instance.sendGroupFileMessage(fileObj, fileUrl, progressCallback, dataExt);
  }

  /**
   * 发送C2C文本消息
   * @param  {string} userId   用户 ID
   * @param  {string} text     文本内容
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendC2CTextMessage(userId: string, text: string): Promise<TIMMsg> {
    return TIM.instance.sendC2CTextMessage(userId, text);
  }

  /**
   * 发送C2C自定义消息
   * @param  {string} userId   用户ID
   * @param  {string} ext      扩展标识
   * @param  {string} data     内容
   * @return {Promise<TIMMsg>} 返回结果
   */
  public sendC2CCustomMessage(userId: string, ext: string, data: string): Promise<TIMMsg> {
    return TIM.instance.sendC2CCustomMessage(userId, ext, data);
  }

  /**
   * 拉取消息列表
   * @param count 消息数量
   * @param nextMsgId 起始消息id
   */
  getMessageList(count: number, nextMsgId: string = null) {
    const groupId = this.getClassInfo().chatGroupId;
    return TIM.instance.getMessageList(groupId, count, nextMsgId);
  }

  /**
   * 标记指定消息为已读消息
   * @param msgSeq 消息序列号
   */
  public markMessageAsRead(msgSeq: number) {
    TIM.instance.markMessageAsRead(msgSeq);
  }

  /**
   * 标记所有消息为已读消息
   */
  public markAllMessagesAsRead() {
    TIM.instance.markAllMessagesAsRead();
  }

  /**
   * 获取课堂默认群组的消息列表
   */
  public getIMMsgList(): TIMMsg[] {
    return TIM.instance.getIMMsgList();
  }

  /**
   * 设置快捷回复词条
   * @param wordsArr 快捷回复词条数组
   */
  public setQuickIMWords(wordsArr: string[]) {
    return TIM.instance.setQuickIMWords(wordsArr);
  }

  /**
   * 获取快捷回复词条
   */
  public getQuickIMWords() {
    return TIM.instance.getQuickIMWords();
  }
  /* =========================================｜白板 接口｜=============================================*/
  /**
   * 获取白板实例
   */
  public getBoard() {
    return TBoard.instance.getBoard();
  }


  /* =========================================｜事件监听和分发｜===========================================*/
  /**
   * 消息广播(仅当前窗口收到)
   * @param {string} event 事件名称
   * @param {*} message
   * @param report 是否上报事件
   */
  public notify(event: string, message: any, report = true) {
    TEvent.instance.notify(event, message, report);
  }

  /**
   * 广播消息(主窗口和弹窗均可收到)
   * @param {string} event 事件名称
   * @param {*} message 消息内容
   */
  public emit(event: string, message: any) {
    if (TSession.instance.isSubWindow()) { // 当前是弹窗
      if (TSession.instance.isMobileNative()) {
        TWebView.instance.sendMsgToWebView(event, message, '', 'subWindow');
      } else {
        (window as any).onWebViewMsg(event, message);
      }
    } else {
      if (TSession.instance.isMobileNative()) {
        TWebView.instance.sendMsgToWebView(event, message, 'subWindow', '');
      } else {
        const iframe = document.getElementById('custom-iframe') as HTMLIFrameElement;
        if (iframe) {
          const subWin: any = iframe.contentWindow;
          if (subWin.onWebViewMsg) {
            subWin.onWebViewMsg(event, message);
          }
        }
      }
    }
    // 补发当前窗口
    TEvent.instance.notify(event, message);
  }

  /**
   * 事件监听
   * @param event 事件
   * @param listener 回调
   * @param options 监听选项
   */
  public on(event: string, listener: TEventListener, options?: TEventOptions): boolean {
    return TEvent.instance.on(event, listener, options);
  }

  /**
   * 移除事件回调
   * @param event 事件名称
   * @param listener 回调
   * @param options 监听选项
   */
  public off(event: string, listener: TEventListener, options?: TEventOptions) {
    TEvent.instance.off(event, listener, options);
  }

  /**
   * 一次性事件监听
   * @param event 事件名称
   * @param listener 回调
   * @param options 监听选项
   */
  public one(event: string, listener: TEventListener, options?: TEventOptions): boolean {
    return TEvent.instance.one(event, listener, options);
  }

  /* =========================================｜状态方法｜===========================================*/
  /**
   * 注册状态
   * @param name          状态名称
   * @param desc          状态说明，必须传入长度>=4的字符串
   * @param value         状态初始值
   * @param validCallers  状态可写者数组
   */
  public registerState(name: string, desc: string, value: any = null, validCallers: string[] = []) {
    return TState.instance.registerState(name, desc, value, validCallers);
  }

  /**
   * 更新状态
   * @param name    状态名称
   * @param value   状态内容
   * @param caller  状态更新者
   * @param log     是否打印日志
   */
  public setState(name: string, value: any, caller = '', log = true) {
    return TState.instance.setState(name, value, caller, log);
  }

  /**
   * 读取状态
   * @param name          状态名称
   * @param defaultValue  默认返回值（组件未注册时返回）
   */
  public getState(name: string, defaultValue?: any) {
    return TState.instance.getState(name, defaultValue);
  }

  /**
   * 订阅状态变更
   * @param name      状态名称
   * @param listener  状态更新回调
   * @param options   状态监听选项
   */
  public subscribeState(name: string, listener: TStateUpdateListener, options?: TStateOptions) {
    return TState.instance.subscribeState(name, listener, options);
  }

  /**
   * 取消状态订阅
   * @param name      状态名称
   * @param listener  状态更新回调
   * @param options   状态监听选项
   */
  public unsubscribeState(name: string, listener: TStateUpdateListener, options?: TStateOptions) {
    return TState.instance.unsubscribeState(name, listener, options);
  }

  /**
   * 确认状态值符合预期
   * @param name        状态名称
   * @param exceptValue 预期的状态值
   * @param equalFunc   状态值比较函数，默认使用 === 进行比较
   */
  public promiseState(name: string, exceptValue: any, equalFunc?: (v: any, ev: any) => boolean): Promise<void> {
    return TState.instance.promiseState(name, exceptValue, equalFunc);
  }


  /* =========================================｜工具方法｜===========================================*/
  /**
   * 是否支持触摸事件
   * @return {boolean}
   */
  public supportTouch(): boolean {
    return TSession.instance.supportTouch();
  }

  /**
   * 是否Electron端
   * @return {boolean}
   */
  public isElectron(): boolean {
    return TSession.instance.isElectron();
  }

  /**
   * 是否Win32客户端
   * @return {boolean}
   */
  public isWin32Electron(): boolean {
    return TSession.instance.isWin32Electron();
  }

  /**
   * 是否Web端，含所有设备上使用浏览器载入的场景
   */
  public isWeb(): boolean {
    return TSession.instance.isWeb();
  }

  /**
   * 是否Android端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isAndroid(): boolean {
    return TSession.instance.isAndroid();
  }

  public isHarmonyOs(): boolean {
    return this.isAndroid() && this._clientInfo && this._clientInfo.harmonyOs;
  }

  // 是否ipad native sdk
  public isiPadNative(): boolean {
    // this._clientInfo.os === "iOS"
    return TSession.instance.isIOSNative() && this._clientInfo && this._clientInfo.osDevType === 'iPad';
  }

  /**
   * 是否iOS端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isIOS(): boolean {
    return TSession.instance.isIOS();
  }

  /**
   * 是否iOS端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isIOSNative(): boolean {
    return TSession.instance.isIOSNative();
  }

  /**
   * 获取IOS版本号
   * @return {string[]}
   */
  public getIOSVersion(): string[] {
    return TSession.instance.getIOSVersion();
  }

  /**
   * 是否大屏(平板或电视)，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isPad(): boolean {
    return TSession.instance.isPad();
  }

  /**
   * 是否Mac端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isMac(): boolean {
    return TSession.instance.isMac();
  }

  /**
   * 是否Windows端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isWindows(): boolean {
    return TSession.instance.isWindows();
  }

  /**
   * 是否移动端，含Native和Web加载两种场景
   * @return {boolean}
   */
  public isMobile(): boolean {
    return TSession.instance.isMobile();
  }

  /**
   * 是否Native移动端
   * @return {boolean}
   */
  public isMobileNative(): boolean {
    return TSession.instance.isMobileNative();
  }

  /**
   * 是否为竖屏课
   * @return {boolean}
   */
  public isPortraitClass(): boolean {
    return (this.isVideoOnlyClass() || this.isBigRoom())
    && TSession.instance.getClassInfo().videoOrientation === VideoOrientation.Portrait;
  }

  /**
   * 是否为竖屏
   * @return {boolean}
   */
  public isPortrait(): boolean {
    return this.getState(TMainState.Device_Orientation) === TDeviceOrientation.Portrait;
  }

  /**
   * 是否小程序
   * @return {boolean}
   */
  public isMiniProgram(): boolean {
    return TSession.instance.isMiniProgram();
  }

  /**
   * 是否小程序WebView
   * @return {boolean}
   */
  public isMiniProgramWebview(): boolean {
    return TSession.instance.isMiniProgramWebview();
  }

  /**
   * 初始化缓存参数
   * @param {string} query &符号分割的参数列表（URL内的query串格式），若未传该参数，取当前页面URL的query串
   */
  public initParams(query?: string) {
    TSession.instance.initParams(query);
  }

  /**
   * 获取参数
   * @param key 参数名称
   * @param defValue 默认值
   */
  public getParams(key: string, defValue?: any) {
    return TSession.instance.getParams(key, defValue);
  }

  /**
   * 设置参数
   * @param key 参数名称
   * @param value 参数值
   */
  public setParams(key: string, value: any) {
    return TSession.instance.setParams(key, value);
  }

  /**
   * 是否是子窗口
   * @return {boolean}
   */
  public isSubWindow(): boolean {
    return TSession.instance.isSubWindow();
  }

  /**
   * 窗口最小化或还原（只在桌面端有效）
   */
  public minimizeWindow() {
    if (window.Electron && window.Electron.minimizeWindow) {
      window.Electron.minimizeWindow();
    }
  }

  /**
   * 窗口最大化或还原（只在桌面端有效）
   */
  public maximizeWindow() {
    if (window.Electron && window.Electron.maximizeWindow) {
      window.Electron.maximizeWindow();
    }
  }

  /**
   * 获取当前UI语言类型
   * @return {string} 语言类型
   */
  public getLanguage(): string {
    return TSession.instance.getLanguage();
  }

  /**
   * 获取支持的UI语言类型
   */
  public getLanguageOptions() {
    return TSession.instance.getLanguageOptions();
  }

  /**
   * 获取当前用户 ID
   * @return {string} 用户 ID
   */
  public getUserId(): string {
    return TSession.instance.getUserId();
  }

  /**
   * 是否为假1v1模式
   * @return {boolean}
   */
  public isFake1v1(): boolean {
    return TSession.instance.isFake1v1();
  }

  /**
   * 获取当前token
   * @return {string} token
   */
  public getToken(): string {
    return TSession.instance.getToken();
  }

  /**
   * 获取 TRTC UserSig
   * @return {string} userSig
   */
  public getTrtcUserSig(): string {
    return TSession.instance.getUserSig();
  }

  /**
   * 获取 webview 宿主小程序 appId（URL 中 fromMpAppId 参数）
   * @return {string} 宿主小程序 appId
   */
  public getFromMpAppId(): string {
    return TSession.instance.getFromMpAppId();
  }

  /**
   * 判断是否巡课
   * @return {boolean}
   */
  public isSupervisor(): boolean {
    return TSession.instance.isSupervisor();
  }

  /**
   * 判断是否是老师
   * @return {boolean}
   */
  public isTeacher(userId?: string): boolean {
    return TSession.instance.isTeacher(userId);
  }

  /**
   * 判断是否助教
   * @return {boolean}
   */
  public isAssistant(userId?: string): boolean {
    return TSession.instance.isAssistant(userId);
  }

  /**
   * 判断是否老师或助教
   * @return {boolean}
   */
  public isTeacherOrAssistant(userId?: string): boolean {
    return this.isTeacher(userId) || this.isAssistant(userId);
  }

  /**
   * 判断是否是学生
   * @return {boolean}
   */
  public isStudent(userId?: string): boolean {
    return TSession.instance.isStudent(userId);
  }

  /**
   * 判断是否是访客
   * @return {boolean}
   */
  public isVisitor(): boolean {
    return TSession.instance.isVisitor();
  }

  /**
   * 获取自己角色
   */
  public getUserRole() {
    return TSession.instance.getUserRole();
  }

  /**
   * 判断是否录制模式
   */
  public isRecordMode() {
    return TSession.instance.isRecordMode();
  }

  /**
   * 判断是否是 RTMP 推流模式
   */
  public isRtmpMode() {
    return TSession.instance.isRtmpMode();
  }

  /**
   * 判断是否直播课
   */
  public isLiveClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classType === TClassType.Live;
    }
    return TSession.instance.getIntParams('classtype') === TClassType.Live
      || TSession.instance.getParams('classsubtype') === TClassSubType.Live;
  }

  /**
   * 判断是互动班课
   */
  public isInteractClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classType === TClassType.Interactive;
    }
    return TSession.instance.getIntParams('classtype') === TClassType.Interactive
      || TSession.instance.getParams('classsubtype') === TClassSubType.Interactive;
  };

  /**
   * 判断是大教学课堂
   * 针对联奕的老版本兼容，优先获取url 参数中的课堂类型
   */
  public isCollegeClass(): boolean {
    const classInfo = this.getClassInfo();
    const queryCollege = TSession.instance.getParams('classsubtype') === TClassSubType.College;
    if (classInfo) {
      return classInfo.classSubType === TClassSubType.College || queryCollege;
    }
    return queryCollege;
  };

  /**
   * 判断是纯视频课堂
   */
  public isVideoOnlyClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classSubType === TClassSubType.Video;
    }
    return TSession.instance.getParams('classsubtype') === TClassSubType.Video;
  }

  /**
   * 判断是视频加文档课堂
   */
  public isVideoDocClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classSubType === TClassSubType.VideoDoc;
    }
    return TSession.instance.getParams('classsubtype') === TClassSubType.VideoDoc;
  }

  /**
   * 判断是否是双师课堂
   */
  public isCoTeachingClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classSubType === TClassSubType.CoTeaching;
    }
    return TSession.instance.getParams('classsubtype') === TClassSubType.CoTeaching;
  };

  /**
   * 判断是否是1v1课堂
   */
  public isOneOnOneClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      const isUnitedOneOnOneClass = this.isUnitedClass() && classInfo.maxRtcMember === 1;
      return classInfo.classSubType === TClassSubType.OneOnOne || isUnitedOneOnOneClass;
    }
    return TSession.instance.getParams('classsubtype') === TClassSubType.OneOnOne;
  };

  /**
   * 判断是否是1v0课堂
   */
  public isOneOnZeroBigClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      const isUnitedOneOnOneClass = this.isUnitedClass() && classInfo.maxRtcMember === 0;
      return isUnitedOneOnOneClass;
    }
    return false;
  };

  /**
   * 判断是否为统一课堂
   */
  public isUnitedClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classType === TClassType.United;
    }
    return TSession.instance.getIntParams('classtype') === TClassType.United;
  }
  /**
   * 判断是否为统一课堂RTC观看类型
   */
  public isUnitedRTCClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo && classInfo.classType === TClassType.United) {
      return classInfo.audienceType === TAudienceType.RTC;
    }
    return false;
  }
  /**
   * 判断是否为统一课堂CDN观看类型
   */
  public isUnitedLiveClass(): boolean {
    const classInfo = this.getClassInfo();
    if (classInfo && classInfo.classType === TClassType.United) {
      return classInfo.audienceType === TAudienceType.CDN;
    }
    return false;
  }

  /**
   * 判断是否为大班课
   */
  public isBigRoom(): boolean {
    const classInfo = this.getClassInfo();
    return classInfo?.roomType === TRoomType.Big;
  }

  public getInstances(): object {
    return {
      im: TIM.instance,
      trtc: TTrtc.instance,
      session: TSession.instance,
      board: TBoard.instance,
      main: TMain.instance,
      state: TState.instance,
      event: TEvent.instance,
      live: TLive.instance,
      tav: TAV.instance,
      member: TBusinessMember.instance,
    };
  }

  public getDeviceType(): TDevice {
    let device = TDevice.Unknown;
    // 获取当前设备类型
    if (TSession.instance.getDevice() === 'tv') {   // 优先判断是否电视
      device = TDevice.TV;   // TV
    } else if (this.isMiniProgram()) { // 小程序
      device = TDevice.Miniprogram;
    } else if (this.isPad()) {
      device = TDevice.Pad;   // pad
    } else if (this.isMobile()) {
      device = TDevice.Phone;   // phone
    } else if (this.isWindows()) {
      device = TDevice.Windows;   // windows
    } else if (this.isMac()) {
      device = TDevice.Mac;   // mac
    }
    return device;
  }

  public getPlatformType(): TPlatform {
    let platform = TPlatform.Unknown;
    // 获取当前平台类型
    if (TSession.instance.getDevice() === 'tv') {   // 优先判断是否电视
      platform = TPlatform.TV;   // TV
    } else if (this.isMiniProgram()) { // 小程序
      platform = TPlatform.Miniprogram;
    } if (this.isWeb()) {   // 优化判断是否使用浏览器(区分是否使用浏览器还是客户端)
      platform = TPlatform.Web;   // Web
    } else if (this.isElectron()) { // 是使用桌面端
      if (this.isWindows()) {
        platform = TPlatform.Windows; // windows
      } else if (this.isMac()) {
        platform = TPlatform.Mac; // mac
      }
    } else if (this.isMobile()) {   // 是移动端
      if (this.isAndroid()) {
        platform = TPlatform.Android;   // android
      } else if (this.isIOS()) {
        platform = TPlatform.IOS;   // iOS
      }
    }
    return platform;
  }

  /**
   * 获取当前平台类型和设备类型
   */
  public getPlatformInfo() {
    if (this._platformInfo) {
      return this._platformInfo;
    }

    const device = this.getDeviceType();
    const platform = this.getPlatformType();
    this._platformInfo = {
      device,
      deviceName: TDeviceNameMap[device],
      platform,
      platformName: TPlatformNameMap[platform],
      platformCategory: TPlatformToPlatformCategoryMap[platform],
    };
    // console.log('platformInfo', this._platformInfo);
    return this._platformInfo;
  }

  /**
   * 检查当前平台是否可用
   */
  public isPlatformAvailable(): boolean {
    if (Number(TSession.instance.getParams('platformforbidden'))) {
      return false;
    }
    const schoolInfo = TSession.instance.getSchoolInfo();
    if (!schoolInfo) {
      return false;
    }
    if (!schoolInfo.availablePlatforms?.length) {
      // 没指定就不限制
      return true;
    }
    return schoolInfo.availablePlatforms.includes(this._platformInfo.platform);
  }

  public isFreeEditionTo25Minutes(): boolean {
    const schoolInfo = TSession.instance.getSchoolInfo();
    if (!schoolInfo) {
      return false;
    }
    return schoolInfo.packageType === TPackageType.Trial && schoolInfo.schoolId !== 3601045;
  }

  public updateClassInfo() {
    this._info('updateClassInfo', '');
    return TBusinessClass.instance.getClassInfo(
      TSession.instance.getClassId(),
      false,
      TSession.instance.getToken(),
    )
      .then((classInfo: TClassInfo) => {
        TSession.instance.setClassInfo(classInfo);
        this._saveSessionInfo();
        // 更新布局信息
        this._updateClassLayout();
        TEvent.instance.notify(TMainEvent.Modify_Class, classInfo);
        TState.instance.setState(
          TMainState.Class_Status,
          classInfo.status,
          this.constructor.name,
        );
        // 更新全员静音状态
        TState.instance.setState(TMainState.Mute_All, classInfo.muteAll === 1, this.constructor.name);
        // 更新全员视频状态
        TState.instance.setState(TMainState.Mute_Video_All, classInfo.muteVideoAll === 1, this.constructor.name);
        // 处理全员禁言
        TState.instance.setState(TMainState.Silence_All, classInfo.silenceAll === 1, this.constructor.name);
        TState.instance.setState(TMainState.Silence_Mode, classInfo.silenceMode, this.constructor.name);
        // 处理允许申请上麦
        TState.instance.setState(TMainState.Enable_Stage, classInfo.enableStage === 1, this.constructor.name);
        TBusinessCommand.instance.onEnableStageChanged(classInfo.enableStage === 1);
        // console.log(`===>>> : updateClassInfo 2: Enable_Stage : ${classInfo.enableStage}`);
        this._info('updateClassInfo success', `updateAt: ${classInfo.updateAt}`);
      })
      .catch((error) => {
        this._error('updateClassInfo error', `failed: ${error.toString()}`);
      });
  }

  public getAvailablePlatformsDesc(): string {
    const schoolInfo = TSession.instance.getSchoolInfo();
    if (!schoolInfo || !schoolInfo.availablePlatforms) {
      return '';
    }
    const categoryNameMap: { [key: string]: string } = {
      [TPlatformCategory.Web]: i18next.t('浏览器'),
      [TPlatformCategory.Miniprogram]: i18next.t('小程序'),
      [TPlatformCategory.Mobile]: i18next.t('移动端'),
      [TPlatformCategory.PC]: i18next.t('PC端'),
    };
    const availableCategoryMap: any = {};
    const availableNameList: string[] = [];
    schoolInfo.availablePlatforms.forEach((platform) => {
      const category = TPlatformToPlatformCategoryMap[platform];
      availableCategoryMap[category] = true;
    });
    // 遍历 categoryNameMap 而不是 availableCategoryMap 是为了保证提示的顺序
    Object.keys(categoryNameMap).forEach((key) => {
      if (availableCategoryMap[key]) {
        availableNameList.push(categoryNameMap[key]);
      }
    });

    if (availableNameList.length === 0) {
      return '';
    }

    const lastName = availableNameList[availableNameList.length - 1];
    if (availableNameList.length === 1) {
      return lastName;
    }

    availableNameList.pop();
    const desc = availableNameList.join(i18next.t('、'));
    return i18next.t('{{arg_0}}或{{arg_1}}', { arg_0: desc, arg_1: lastName });
  }

  public saveSnapshot(
    snapId: string,
    base64Data: string,
    snapName: string,
    sliceUnit: number,
    dialog = false,
  ): Promise<any> {
    return TBoard.instance.saveSnapshot(snapId, base64Data, snapName, sliceUnit, dialog);
  }

  /**
   * 打开外部浏览器
   * @param url {string} 网页地址
   */
  public openBrowser(url: string) {
    if (TSession.instance.isMobile()) {
      TWebView.instance.call('os', 'openOSBrowser', { url });
    } else if (TSession.instance.isElectron()) {
      window.Electron.openUrl(url);
    } else {
      window.open(url, '_blank');
    }
  }

  /**
   * 设置设备方向
   * @param orientation {TDeviceOrientation} 设备方向
   */
  public async setDeviceOrientation(orientation: TDeviceOrientation): Promise<void> {
    // 锁定横竖屏不能更改.
    const locked = TSession.instance.getLockedOrientation();
    const current = TState.instance.getState(TMainState.Device_Orientation) as TDeviceOrientation;
    const map = {
      landscape: TDeviceOrientation.Landscape,
      portrait: TDeviceOrientation.Portrait,
    };
    if (locked) {
      // 锁定状态时,保持api的调用值不变, 但不触发重力旋转
      if (locked === 'locked') {
        TState.instance.setState(
          TMainState.Device_Orientation,
          orientation,
        );
        return;
      }
      if (map[locked] !== current) {
        TState.instance.setState(
          TMainState.Device_Orientation,
          map[locked],
        );
      }
      return;
    }
    // 记录调用堆栈.
    this._info('setDeviceOrientation', `${current}=>${orientation} `, {
      stack: new Error().stack,
    });
    return new Promise<void>((resolve) => {
      const isStudent = TSession.instance.isStudent();
      if (this.isMobileNative()) {
        // 非竖屏课时老师全部横屏
        if (!this.isPortraitClass() && !this.isStudent()) {
          orientation = TDeviceOrientation.Landscape;
        }
        TWebView.instance.call('os', 'rotateDevice', {
          home: orientation === TDeviceOrientation.Portrait ? 1 : 3,
        }, () => {
          setTimeout(() => {
            // 之所以延迟100ms
            // 1. 旋转未完成，外部获取的bound为旋转前的bound
            // 2. 如果旋转完成后回调，H5 UI会出现变化的过程，影响体验
            TState.instance.setState(
              TMainState.Device_Orientation,
              orientation,
            );
            resolve();
          }, 100);
        });
      } else {
        // h5老师、1v1课堂、纯视频(非竖屏)课堂时弹窗提示
        if (orientation === TDeviceOrientation.Portrait && !this.isPad() && !this.isPortraitClass()) {
          // if (this.isTeacher()) {
          //   this._tipDialogId = this._curDialogId + 1;
          //   this.showMessageBox(
          //     i18next.t('提示'),
          //     [
          //       i18next.t('为了保证最佳体验，{{arg_0}}请使用横屏。', { arg_0: TSession.instance.getRoleInfo().roleInfo.teacher }),
          //       i18next.t('注意：请先解除手机方向锁定'),
          //     ].join('<br>'),
          //     [i18next.t('确定')],
          //     () => {},
          //   );
          // } else if (this.isOneOnOneClass() || this.isVideoOnlyClass()) {
          //   this._tipDialogId = this._curDialogId + 1;
          //   this.showMessageBox(
          //     i18next.t('提示'),
          //     [
          //       i18next.t('为了保证最佳体验，请使用横屏。'),
          //       i18next.t('注意：请先解除手机方向锁定'),
          //     ].join('<br>'),
          //     [i18next.t('确定')],
          //     () => {},
          //   );
          // }
          if (window.showToast) {
            window.showToast(i18next.t('为最佳体验，请使用横屏。'));
          } else {
            this._tipDialogId = this._curDialogId + 1;
            this.showMessageBox(
              i18next.t('提示'),
              [
                i18next.t('为了保证最佳体验，请使用横屏。'),
                i18next.t('注意：请先解除手机方向锁定'),
              ].join('<br>'),
              [i18next.t('确定')],
              () => { },
            );
          }
        } else {
          this.closeMessageBox(this._tipDialogId);
        }
        TState.instance.setState(TMainState.Device_Orientation, orientation);
      }
    });
  }

  /**
   * 设置进房退房成员信息过滤器
   * @param filter 过滤器
   */
  public setMemberJoinExitRoomInfoFilter(filter: Function) {
    this._member_join_exit_filter = filter;
  }


  /**
   * 获取进房退房成员信息过滤器
   */
  public getMemberJoinExitRoomInfoFilter() {
    return this._member_join_exit_filter;
  }

  /* ===========｜统计接口｜==============*/
  /**
   * 检查网络链接情况
   * @return {boolean}
   */
  public checkNetworkConnection(): boolean {
    return TStatistics.instance.checkNetworkConnection();
  }

  /**
   * 检查 aPaaS 服务器链接情况
   * @return {Promise<number>} < 0 表示链接错误，> 0 表示链接成功的耗时
   */
  public checkBusinessServerConnection(): Promise<number> {
    return TStatistics.instance.checkBusinessServerConnection();
  }
  public setCookie() {
    TStatistics.instance.setCookie();
  }

  /**
   * 检查网络权限
   * @return {Promise<boolean>}
   */
  public checkNetworkPermission(): Promise<boolean> {
    return TStatistics.instance.checkNetworkPermission();
  }

  /**
   * 检查 TRTC 服务器链接情况
   * @return {Promise<number>} < 0 表示链接错误，> 0 表示链接成功的耗时
   */
  public checkMediaServerConnection(): Promise<TNetworkStatistics> {
    return TStatistics.instance.checkMediaServerConnection();
  }

  /**
   * 网络质量的判断方法
   * @return {Promise<TNetworkQuality>}
   */
  public getNetworkQuality(rtt: number, loss: number) {
    return TStatistics.instance.getNetworkQuality(rtt, loss);
  }

  /**
   * API网络接口质量
   * @return {Promise<TNetworkQuality>}
   */
  public getApiNetworkQuality(rtt: number) {
    return TStatistics.instance.getApiNetworkQuality(rtt);
  }

  /**
   * 检查DNS解析情况
   * @return {Promise<number>} < 0 表示解析错误，> 0 表示解析成功的耗时
   */
  public checkDNSParsing(): Promise<number> {
    return TStatistics.instance.checkDNSParsing(TBusiness.instance.getHost());
  }

  public startPlay(dom: HTMLElement, url: string, fullMode = true, muteMode = false, userId = '', isSub = false): Promise<void> {
    return TLive.instance.startPlay(dom, url, fullMode, muteMode, userId, isSub);
  }

  public stopPlay(url: string): Promise<void> {
    return TLive.instance.stopPlay(url);
  }

  public resetLocalVideoEncodeParams() {
    const classInfo = this.getClassInfo();
    if (classInfo.customData.hasOwnProperty('disable_bitrate_adjust') || !this.isFeatureAvailable('EnableBitrateAdjust')) {
      // 公开课没有fps/bitrate调整功能
      if (classInfo.classType !== TClassType.Live) {
        const resolution = TTrtc.getVideoResolutionFromCameraResolution(classInfo.cameraResolution);
        const resolutionMode = (this.isPortraitClass() && this.isTeacher()) ? 1 : 0;
        TTrtc.instance.setVideoEncoderParam(
          resolution,
          classInfo.cameraFPS,
          classInfo.cameraBitrate,
          resolutionMode,
        ).then();
      }
      return false;
    }
    return TAV.instance.resetLocalVideoEncodeParams();
  }

  // 清除已有的定时器
  public clearDurationInterval() {
    if (this._classDurationInterval) {
      clearInterval(this._classDurationInterval);
      this._classDurationInterval = null;
    }
  }

  // 获取白板背景色
  public getBoardBackgroundColor() {
    return this._boardInitParams.styleConfig.globalBackgroundColor;
  }

  public getTCICErrorInstance(code: number, msg: string, detail: string, module: string) {
    module = module || 'TCIC';
    if (module === 'TRTC') {
      if (this.isElectron()) {
        return new TRTCElectronError(code, msg, detail);
      }
      if (this.isMobileNative()) {
        return new TRTCMobileError(code, msg, detail);
      }
      if (this.isWeb()) {
        return new TRTCWebError(code, msg, detail);
      }
    } else if (module === 'Board') {
      return new BoardError(code, msg, detail);
    } else if (module === 'IM') {
      return new IMError(code, msg, detail);
    } else {
      return new TCICError(code, msg, detail);
    }
  }

  // 是否是AI课堂
  public isAiRoom() {
    const classInfo = this.getClassInfo();
    if (classInfo) {
      return classInfo.classSubType === TClassSubType.AudioOnly && classInfo.liveType === 10;
    }
  }

  public callExperimentalAPI(key: string, val: any) {
    return TIM.instance.callExperimentalAPI(key, val);
  }

  /**
   * 获取类名
   * @private
   */
  protected _getClassName() {
    return 'TMain';
  }
  protected _initMergeLogs() {
    const eventModule = this._getClassName();
    TLogger.getInstance('TCIC').mergeInfoLog(eventModule, '_processCTRLMsg@v1/sync');
  }

  protected _infoWithLog(action: string, message = '') {
    TSession.instance.addLog(`${action} ${message}`);
    super._info(action, message);
  }

  protected _warnWithLog(action: string, message = '') {
    TSession.instance.addLog(`${action} ${message}`);
    super._warn(action, message);
  }

  protected _errorWithLog(action: string, message = '') {
    TSession.instance.addLog(`${action} ${message}`);
    super._error(action, message);
  }

  protected _flushLog() {
    this._info('innerLog', TSession.instance.flushLog());
  }

  private _updateAppClientRect() {
    const newRect = TMain.makeDomRect(this._getRootDom().getBoundingClientRect());
    if (!this._compareDomRect(newRect, this._appClientRect)) {
      this._appClientRect = newRect;
      // 当窗口大小变化时，更新可拖动区域并重新布局组件，避免组件在窗口大小变化后超出可拖动区域限制
      this._componentsMap.forEach((comInfo) => {
        comInfo.updateDraggableLayout();
      });
      requestAnimationFrame(() => {
        TEvent.instance.notify(TMainEvent.App_Resized, newRect);
      });
    }
  }

  private _getComponentInfo(name: string, label: string): ComponentInfo {
    const fullName = `${name}-${label}`;
    // if (label === '1st-') {
    //   for (const key of this._componentsMap.keys()) {
    //     if (key.startsWith(`${name}-`) && key !== `${name}-1st-`) {
    //       fullName = key;
    //       break;
    //     }
    //   }
    // }
    const layoutComp = `${name}-layout--`;
    if (!this._componentsMap.has(fullName)) {
      const comp = new ComponentInfo(name, label);
      if (this._componentsMap.has(layoutComp)) {
        comp.layout = this._componentsMap.get(layoutComp).layout;
      }
      this._componentsMap.set(fullName, comp);
    }
    const comp = this._componentsMap.get(fullName);
    // if (this._componentsMap.has(layoutComp)) {
    //   comp.commonLayout = this._componentsMap.get(layoutComp).layout;
    // }

    return comp;
  }

  /**
   * 更新所有组件状态
   * @private
   */
  private _updateAllComponents() {
    // if (this._componentsUpdateTask) {
    //   cancelAnimationFrame(this._componentsUpdateTask);
    // }
    const renderFn = () => {
      this._componentsUpdateTask = 0;
      const rootDom = this._getRootDom();
      if (!rootDom) {
        this._reportEvent('updateAllComponents@error', 'root dom not found');
        this._error('_updateAllComponents@error', 'root dom not found');
        return;
      }
      let hasNewComponent = false;
      let hasSomethingChanged = false;
      const keys = Array.from(this._componentsMap.keys());
      const startTime = window.performance.now();
      for (let i = 0; i < keys.length; ++i) {
        const comInfo = this._componentsMap.get(keys[i]);
        do {
          // 记录状态
          const remove = comInfo.remove;
          const create = comInfo.create;
          const update = comInfo.update;
          if (remove) {  // 明确指定需要删除
            comInfo.remove = false;
            if (comInfo.dom) {
              comInfo.destroy();
              this._info('_updateAllComponents@remove', `${comInfo.fullName()} done`);
              hasSomethingChanged = true;
            } else {
              this._reportEvent('updateAllComponents@removeError', comInfo.fullName());
              this._info('_updateAllComponents@remove', `${comInfo.fullName()} not found`);
            }
          }
          if (create) {  // 明确指定需要创建
            if (!comInfo.dom) {
              comInfo.dom = document.createElement(comInfo.name);
              if (!(comInfo.dom instanceof TBase) && !(TVueComponent.instance.find(comInfo.name))) {
                this._reportEvent('updateAllComponents@createError', comInfo.fullName());
                comInfo.dom = null;
                comInfo.resolve(false);
                break;
              }

              comInfo.dom.classList.add('tcic-component-container');
              // 通过这个 class可以找到devtools里面对应的 app 实例...
              comInfo.dom.classList.add(`vue-dev-${i + 1}`);
              comInfo.dom.classList.add(comInfo.fullName());
              comInfo.dom.setAttribute('label', comInfo.label);
              comInfo.dom.id = comInfo.fullName();
              let parentDom = rootDom;
              if (comInfo.parentDomId) {
                parentDom = document.getElementById(comInfo.parentDomId);
                if (!parentDom) {
                  this._reportEvent('updateAllComponents@createError', comInfo.fullName());
                  this._error('_updateAllComponents@create', `${comInfo.fullName()} parent dom ${comInfo.parentDomId} not found`);
                  comInfo.dom = null;
                  comInfo.resolve(false);
                  break;
                }
              }
              parentDom.appendChild(comInfo.dom);
              this._info('_updateAllComponents@create', `${comInfo.fullName()} done`);
              hasNewComponent = true;
              hasSomethingChanged = true;
            }
            comInfo.create = false;
          }
          if (update || create) {  // 明确指定需要更新，或者刚创建完成
            if (comInfo.dom) {
              // 更新属性
              const strStyle = `position: ${comInfo.layout.position || 'absolute'};${comInfo.layout.right ? `right: ${comInfo.layout.right || 'auto'};` : `left: ${comInfo.layout.left || '0'};`
              }top: ${comInfo.layout.top || 0};`
                + `width: ${comInfo.layout.width || '100%'};`
                + `height: ${comInfo.layout.height || '100%'};`
                + `opacity: ${comInfo.layout.opacity || 1};`
                + `display: ${comInfo.layout.display || 'none'};`
                + `z-index: ${comInfo.layout.zIndex || 1};`
                + `transform: ${comInfo.layout.transform || 'none'};`
                + `padding: ${comInfo.layout.padding || 'inherit'};`
                + `transform-origin: ${comInfo.layout.transformOrigin || 'center center'};`
                + 'vertical-align: middle;'
                + `${comInfo.layout.style || ''}`;
              comInfo.dom.setAttribute('style', strStyle);
              // 延迟更新组件状态，因为updateStatus内部会去获取DOM节点状态，需要留出时间等待DOM节点更新.
              requestAnimationFrame(() => {
                comInfo.updateStatus();
              });
              this._info('_updateAllComponents@update', `${comInfo.fullName()} -> ${strStyle} done`);
              hasSomethingChanged = true;
            }
            comInfo.update = false;
          }
          if (remove || create || update) {
            if (TSession.instance.isIOS8910()) {
              setTimeout(() => {
                // ios 9，添加dom到文档不回同步触发connectedcallback，导致getVueInstance函数注入延后无法调用
                comInfo.resolve(true);
              }, 10);
            } else {
              comInfo.resolve(true);
            }
          }
        } while (false);
        if (window.performance.now() - startTime >= 10) {  // 确保10ms内执行完，避免阻塞UI更新
          if (i < keys.length - 1) {
            setTimeout(this._updateAllComponents.bind(this), 0);
          }
          break;
        }
      }
      if (hasNewComponent) {
        TMain._touchScreenAdjust();
      }
      if (hasSomethingChanged) {
        // 当组件发生布局更新时，更新可拖动区域并重新布局组件，避免组件在其它组件布局变化后超出可拖动区域限制
        this._componentsMap.forEach((comInfo) => {
          comInfo.updateDraggableLayout();
        });
      }
    };
    renderFn();
    // this._componentsUpdateTask = requestAnimationFrame(renderFn);
  }

  /**
   * 加载 JS
   * @param name 组件名称后缀，格式为 custom-script-${name}
   * @param url  JS 链接
   * @return {Promise<void>} 加载回调
   * @private
   */
  private _loadJS(name: string, url: string): Promise<void> {
    const costActionLabel = `tmain.loadJSCost.${name}`;
    return new Promise<void>((resolve, reject) => {
      this._info('loadJS', `${name} start, url->${url}`);
      const scriptTag = document.getElementById(`custom-script-${name}`);
      const head = document.getElementsByTagName('head')[0];
      if (scriptTag) {
        head.removeChild(scriptTag);
      }
      this._logStart(costActionLabel);
      const script: HTMLScriptElement | any = document.createElement('script');
      script.type = 'text/javascript';
      // 自定义js脚本加上crossOrigin方便搜集报错.
      if (url.includes('.qcloudclass.com/customcontent/')) {
        script.crossOrigin = 'anonymous';
      }
      script.onerror = (error: any) => {
        this._logEnded(costActionLabel, TLevel.ERROR, {
          module: this._getClassName(),
          action: costActionLabel,
          param: `loadJS failed:${url}`,
        });
        this._error('loadJS', `${name} onerror, url->${url}, error->${JSON.stringify(error)}`);
        reject(error);
      };
      // IE
      if (script.readyState) {
        script.onreadystatechange = () => {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null;
            this._logEnded(costActionLabel, TLevel.INFO, {
              module: this._getClassName(),
              action: costActionLabel,
              param: `loadJS sucess:${url}`,
            });
            this._info('loadJS', `${name} onloaded/complete, url->${url}`);
            resolve();
          }
        };
      } else {  // 其他浏览器
        script.onload = () => {
          this._logEnded(costActionLabel, TLevel.INFO, {
            module: this._getClassName(),
            action: costActionLabel,
            param: `loadJS sucess:${url}`,
          });
          this._info('loadJS', `${name} onload, url->${url}`);
          resolve();
        };
      }
      script.src = url;
      script.id = `custom-script-${name}`;
      head.appendChild(script);
    });
  }

  /**
   * 加载 CSS
   * @param url  地址
   * @private
   */
  private _loadCSS(url: string) {
    this._info('loadCSS', `url->${url}`);
    const cssTag = document.getElementById('custom-css');
    const head = document.getElementsByTagName('head')[0];
    if (cssTag) {
      head.removeChild(cssTag);
    }
    const css = document.createElement('link');
    css.href = url;
    css.rel = 'stylesheet';
    css.type = 'text/css';
    css.id = 'custom-css';
    head.appendChild(css);
  }

  /**
   * 关闭页面
   * @private
   */
  private _closeWindow() {
    window.removeunloadListener();
    let backUrl = decodeURIComponent(TSession.instance.getParams('back_url') || '');
    let cond = '';
    try {
      /**
     * 如果backUrl地址开头不合规不做处理
     */
      backUrl = backUrl.replace(/^\s+|\s+$/g, '');
      if (backUrl !== '' && !/^https?:\/\/[\w]+/.exec(backUrl)) {
        cond = '1';
        return;
      }
      if (backUrl.includes('login.html')) {
        // 登录页，把lng带上
        const [backPage, backSearch] = backUrl.split('?');
        const backUrlParams = new URLSearchParams(backSearch || '');
        backUrlParams.set('lng', TSession.instance.getLanguage());
        backUrl = `${backPage}?${backUrlParams.toString()}`;
      }

      if (TSession.instance.isMobileNative()) {
        cond = 'isMobileNative';
        TWebView.instance.closeWebView();
      } else if (TSession.instance.isElectron()) {
        window.Electron.closeWindow();
      } else if (TSession.instance.isMiniProgramWebview()) {
        if (backUrl) {
          window.location.replace(backUrl);
        } else {
          // @ts-ignore
          const wxMiniprogram: any = wx;
          (window as any).open('', '_self', '').close(); // 优化关闭
          wxMiniprogram.miniProgram.redirectTo({
            url: '/pages/index/webview',
          });
        }
      } else {
        const customInfo = TSession.instance.getSchoolInfo().customContent;
        if (backUrl) {
          window.location.href = backUrl;
          cond = '3';
        } else if (customInfo && customInfo.home && customInfo.home.length > 0) {
          backUrl = customInfo.home;
          cond = '4';
          window.location.href = backUrl;
        } else if (window.self !== window.top) {
          cond = '6';
          window.top.postMessage({ name: 'tcic_window_close' }, '*');
        } else {
          cond = '5';
          (window as any).open('', '_self', '').close(); // 优化关闭
          history.back();
        }
      }
    } finally {
      this._info('_closeWindow', `backUrl: ${backUrl} cond:${cond}`);
      TLogger.flushReport();
    }
  }

  /**
   * 加载trtc相关js
   * @private
   */
  private async _loadTrtcLibs() {
    const trtcSDKJS = TTrtc.getSDKJS() || ''; // trtc sdk js
    if (trtcSDKJS) {
      try {
        await this._loadJS('trtc', trtcSDKJS);
      } catch (err) {
        const errorIns = this.getTCICErrorInstance(-1, i18next.t('加载音视频库失败'), err.toString(), 'TRTC');
        throw errorIns;
      }
    }

    const trtcPluginList = TTrtc.getPluginList() || []; // trtc插件list
    if (trtcPluginList.length > 0) {
      try {
        await Promise.all(trtcPluginList.map(jsItem => this._loadJS(jsItem.name, jsItem.url)));
      } catch (err) {
        const errorIns = this.getTCICErrorInstance(-1, i18next.t('加载音视频插件失败'), err.toString(), 'TRTC');
        throw errorIns;
      }
    }
  }
  /**
   * 获取信息并进入课堂
   * @private
   */
  private async _getInfoAndJoinClass() {
    this._enterTimeStamp = new Date().getTime();
    const arg_0 = TSession.instance.getRoleInfo().roomInfo.name;

    let step = '';
    let schoolInfo: TSchoolInfo;
    let classInfo: TClassInfo;
    // 获取各种信息
    try {
      step = 'getSchoolInfo';
      TSession.instance.addLog(`_getInfo, step ${step}`);
      schoolInfo = await this._getSchoolInfo();

      step = 'loadCustomCssAndJs';
      TSession.instance.addLog(`_getInfo, step ${step}`);
      await this._loadCustomCssAndJs(schoolInfo).catch((err) => {
        // 不阻塞进房了.
        console.error('%c [ err ]-5562', 'font-size:13px; background:pink; color:#bf2c9f;', err);
      });

      // App信息ready
      TState.instance.setState(TMainState.School_Info_Ready, true, this.constructor.name);

      step = 'getClassInfo';
      TSession.instance.addLog(`_getInfo, step ${step}`);
      classInfo = await this._getClassInfo();

      step = 'checkSupervisor';
      TSession.instance.addLog(`_getInfo, step ${step}`);
      await this._checkSupervisor();

      // 更新自己的角色
      TSession.instance.initUserRole();
      this._infoWithLog('_getInfo', `success, ${JSON.stringify({
        roomType: classInfo.roomType,
        classSubType: classInfo.classSubType,
        maxRtcMember: classInfo.maxRtcMember,
        autoOpenMic: classInfo.autoOpenMic,
        userRole: TSession.instance.getUserRole(),
        isRecordMode: TSession.instance.isRecordMode(),
      }, null, 2)}`);

      // 课堂信息ready
      TState.instance.setState(TMainState.Class_Info_Ready, true, this.constructor.name);
    } catch (err) {
      // console.error('_getInfo error:', err);
      let error = err;
      if (!err.errorCode || !err.errorMsg) {
        // 有些不是 TCICError，比如loadJS出错，转成统一格式方便处理
        error = new TCICError(
          err.errorCode || -1,
          err.errorMsg || i18next.t('获取{{arg_0}}信息失败', { arg_0 }),
          `${step} error`,
        );
      }
      this._errorWithLog('_getInfo', `${step} failed: ${JSON.stringify(error)}`);
      this.reportEvent('enter_class', error, error.errorCode, this._enterTimeStamp);
      throw new TCICError(
        error.errorCode,
        error.errorMsg,
        i18next.t('获取{{arg_0}}信息失败 {{arg_1}}', { arg_0, arg_1: `${step} error` }),
      );
    }

    // 进房，这个不用 try，出错了透传
    step = 'joinClass';
    await this._joinClass(classInfo);
  }

  /**
   * 获取学校信息
   * @private
   */
  private async _getSchoolInfo() {
    const result = await TBusinessSchool.instance.getSchoolInfo(
      TSession.instance.getScene(),
      TSession.instance.getParams('callid'),
      TSession.instance.getToken(),
    );
    this._info('_getSchoolInfo', JSON.stringify(result));
    TSession.instance.setSchoolInfo(result);

    const loggerConfig = {
      sdkAppId: result.sdkAppId,
      schoolId: result.schoolId,
    };
    TLogger.getInstance('TCIC').setConfig(loggerConfig);
    TLogger.getInstance('TCICUI').setConfig(loggerConfig);

    // 根据套餐包控制一些特性
    this._packageFeatureConfig = TPackageFeatureConfigMap[result.packageType] || packageFeatureEmptyConfig;
    this._info('_packageFeatureConfig', `packageType ${result.packageType}, ${JSON.stringify(this._packageFeatureConfig)}`);

    Object.assign(TFeatureDefaultConfig, this._packageFeatureConfig);

    return result;
  }

  /**
   * 加载自定义css和js
   * @private
   */
  private async _loadCustomCssAndJs(schoolInfo: TSchoolInfo) {
    // 解析学校定制样式
    const customInfo = schoolInfo.customContent;
    if (TSession.instance.getDebugCSS()) {
      this._loadCSS(TSession.instance.getDebugCSS());
    } else if (customInfo && customInfo.css && customInfo.css.length > 0) {
      this._loadCSS(customInfo.css);
    }

    let jsUrl = '';
    if (TSession.instance.getDebugJS()) {
      jsUrl = TSession.instance.getDebugJS();
    } else if (customInfo && customInfo.js && customInfo.js.length > 0) {
      jsUrl = customInfo.js;
    }
    // 调试
    if (jsUrl) {
      await this._loadJS('school', jsUrl);
    }
  }

  /**
   * 初始化特性
   * @private
   */
  private _initFeature() {
    this._localFeatureMap.set('WhiteBoardTOOL', true);
    this._localFeatureMap.set('WhiteBoardList', true);
    this._localFeatureMap.set('WhiteBoardPPT', true);
    this._localFeatureMap.set('WhiteBoardPPT.WheelPaging', false);
    // 是否生成板书截图，同时依赖后端创建课堂参数
    this._localFeatureMap.set('WhiteBoardSnapShot', false);
    // 自定义域名默认不展示美颜设置
    const isCustomDomain = !(location.hostname.match(/qcloudclass\.com$/) || location.hostname === 'localhost');
    this._localFeatureMap.set('Beautify.Enable', !(isCustomDomain && this.isWeb()));
  }


  /**
   * 获取课堂信息
   * @private
   */
  private async _getClassInfo() {
    if (TSession.instance.isMobileNative()) {
      TWebView.instance.call('os', 'onGetClassInfo', {
        webSdkVersion: this.getVersion(),
        sdkAppId: TSession.instance.getSchoolInfo().sdkAppId,
        schoolId: TSession.instance.getSchoolInfo().schoolId,
        roomId: TSession.instance.getClassId(),
        userId: TSession.instance.getUserId(),
      });
    }
    const result = await TBusinessClass.instance.getClassInfo(
      TSession.instance.getClassId(),
      false,
      TSession.instance.getToken(),
    );
    this._info('_getClassInfo', JSON.stringify(result));
    TSession.instance.setClassInfo(result);

    try {
      const marquee = new TClassMarquee();
      marquee.deserialize(JSON.parse(result.marquee ?? '{}'));
      this.setMarqueeParam(marquee);
    } catch (e) {
      this._info('setMarqueeParam', `setMarqueeParam failed ${result.marquee ?? '{}'}`);
    }

    this._saveSessionInfo();

    return result;
  }

  /**
   * 查询是否巡课
   * @private
   */
  private async _checkSupervisor() {
    // web 录制账号识别并判断
    const hasSupervisorParam = TSession.instance.getParams('role') === 'supervisor';
    if (hasSupervisorParam && !this.isRecordMode()) {
      const isSupervisor = await this._checkSupervisorFromPage(1);
      this._info('_checkSupervisor', `isSupervisor ${isSupervisor}`);
      TSession.instance.setIsSuperVisor(isSupervisor);
      return isSupervisor;
    }
    return false;
  }
  private async _checkSupervisorFromPage(page: number): Promise<boolean> {
    // console.log('checkSupervisor get page', page);
    const pageLimit = 100;
    const supervisors = await TBusinessSchool.instance.getSupervisor(
      TSession.instance.getSchoolId(),
      pageLimit,
      page,
      TSession.instance.getToken(),
    );
    // console.log('checkSupervisor get page success', page, supervisors);
    if (!supervisors.userIds?.length) {
      return false;
    }
    if (supervisors.userIds.includes(this.getUserId())) {
      return true;
    }
    const length = pageLimit * (page - 1) + supervisors.userIds.length;
    // console.log('checkSupervisor now length', length);
    if (length < supervisors.total) {
      return await this._checkSupervisorFromPage(page + 1);
    }
    return false;
  }

  /**
   * 加入课堂
   * @param classInfo 课堂信息
   * @private
   */
  private async _joinClass(classInfo: TClassInfo): Promise<void> {
    const arg_0 = TSession.instance.getRoleInfo().roomInfo.name;

    const { platform } = this._platformInfo;
    const availablePlatforms = TSession.instance.getSchoolInfo()?.availablePlatforms;
    console.log(`_joinClass in platform ${platform}, availablePlatforms`, availablePlatforms);
    if (!this.isPlatformAvailable()) {
      // 当前平台不可用
      this._warnWithLog('_joinClass', `failed: platform ${platform} not available in ${JSON.stringify(TSession.instance.getSchoolInfo()?.availablePlatforms)}`);
      const platformsDesc = this.getAvailablePlatformsDesc();
      // TODO errorCode 有统一定义吗？
      throw new TCICError(
        -1,
        i18next.t('请使用{{arg_1}}进入{{arg_0}}', { arg_0, arg_1: platformsDesc }),
        i18next.t('进入{{arg_0}}失败', { arg_0 }),
      );
    }

    const device = this.getDeviceType();
    const timestampBeforeJoin = Date.now();
    // web 录制账号识别并判断
    const urlRole = ~~getQuery('dynamicRole');
    const joinRole = urlRole || ((this.isSupervisor() || this.isRecordMode()) ? 1 : 0);
    let result: TMemberJoinResult;
    try {
      let recordVersion = RECORD_PAGE_VERSION;
      const host = window.location.host;
      let isTest = false;
      if (host.indexOf('test-') > -1) {
        isTest = true;
      }
      const extractContent = (url: string) => {
        const regex = /\/([^/]+)\//;
        const match = url.match(regex);
        return match ? match[1] : null;
      };
      if (isTest) {
        const partUrl = location.href.split('://')[1];
        recordVersion = extractContent(partUrl);
      }

      result = await TBusinessMember.instance.memberJoin(
        classInfo.classId,
        TSession.instance.getToken(),
        platform,
        device,
        joinRole,
        recordVersion, // 后台用来拼录制页面url的版本号
        this._position ? JSON.stringify(this._position) : '',
      );

      // 如果 URL 上指定了角色身份，reload 一下页面
      if (joinRole >= 10) {
        const newURL = location.href.replace(/&dynamicRole=\d+/, '');
        location.href = newURL;
      }
    } catch (error) {
      this._errorWithLog('_joinClass', `failed: ${JSON.stringify(error)}`);
      this.reportEvent('enter_class', error, error.errorCode, this._enterTimeStamp);
      throw new TCICError(
        error.errorCode,
        error.errorMsg,
        i18next.t('进入{{arg_0}}失败', { arg_0 }),
      );
    }
    this._info('member/join::result', JSON.stringify(result));
    const timestampAfterJoin = Date.now();
    const halfRtt = (timestampAfterJoin - timestampBeforeJoin) / 2;
    // 记录下当前的服务器时间以及本地时间，方便后续随时在本地计算近似的服务器时间
    // TODO: 这里本地时间直接使用系统时钟，存在一个潜在问题：
    // 如果上课过程中系统时钟发生调整，比如用户主动更改系统时间，或者系统自动对时，则会导致计算出来的近似服务器时间不准确
    // 不使用window.performance.now()方法获取本地时间的原因是在移动端上，退后台后该时间可能会变慢
    /**
     * 后端更新了timestamp,精度提升为纳秒'1706512093328504407',且为字符串
     * 线上服务返回为秒级，补充兼容方案
     * 说明返回的新数据nano_timestamp
     */
    if (result.nano_timestamp) {
      this.nano_timestamp = Number(result.nano_timestamp);
    }
    this._serverTimestampAfterJoin = result.timestamp * 1000 + halfRtt;
    this._localTimestampAfterJoin = timestampAfterJoin;
    TSession.instance.setUserSig(result.userSig);  // 保存UserSig
    const schoolInfo = TSession.instance.getSchoolInfo();
    const userId = TSession.instance.getUserId();
    const userRole = TSession.instance.getUserRole();
    // 权限初始化
    await TRBAC.instance.initialize();
    // IM初始化
    TIM.instance.init(
      schoolInfo.sdkAppId,
      userId,
      result.userSig,
      classInfo.chatGroupId,
      classInfo.cmdGroupId,
    );
    TState.instance.subscribeState(TMainState.IM_Logined, async (state) => {
      if (state) {
        try {
          const userInfo = await this.getUserInfo(userId);
          TIM.instance.setProfile({
            nick: userInfo.nickname,
            avatar: userInfo.avatar,
          });
        } catch (e) {
          console.error(e);
        }
      }
    });
    if (!(this.isCollegeClass() || this.isCoTeachingClass()) && !this.isVideoOnlyClass()) { // 非大教学或双师模式才初始化白板
      // 白板初始化
      const initParam = Object.assign({
        classId: classInfo.classId,
        sdkAppId: schoolInfo.sdkAppId,
        userId: TSession.instance.getUserId(),
        userSig: result.userSig,
        newEnterId: schoolInfo.schoolId,
        ratio: classInfo.boardRatio || '16:9',
        drawEnable: false,
        boardContentFitMode: TBoard.TEduBoard.TEduBoardContentFitMode.TEDU_BOARD_FILE_FIT_MODE_CENTER_INSIDE,
        enableScaleTool: false,
        // 只在Mac下使用系统光标，Windows下不使用，因为开启了Window Ink功能会导致光标闪烁
        systemCursorEnable: TSession.instance.isMac(),
      }, this._boardInitParams);
      TBoard.instance.init(initParam).then();
    }
    const isBigRoom = this.isBigRoom();
    const isTeacherOrAssistant = this.isTeacherOrAssistant();
    let selfRole = userRole;
    if (this.isRecordMode()) {
      selfRole = `${selfRole}-record`;
    }
    const isSelfOnStage = !!result.members.find(item => item.userId === this.getUserId() && item.stage);
    this._joinType = result.joinType;
    const unitedRTCMode = this._joinType === TJoinType.RTC;    // 低延迟观看
    const isOneOnZeroClass = this.isOneOnZeroBigClass(); // 1v0课程
    // 互动课或直播课且上台的人都用TRTC进房
    let needJoinTrtc = (this.isLiveClass() && isSelfOnStage)
      || this.isInteractClass()
      || (isBigRoom && (isTeacherOrAssistant || isSelfOnStage))
      || unitedRTCMode;
    // 1v0 课程如果学生不支持webrtc 则不进房
    if (isOneOnZeroClass && this.isStudent() && !this.isWebRTCSupport()) {
      needJoinTrtc = false;
    }
    this._infoWithLog('_joinClass', `success, ${JSON.stringify({
      joinRole,
      'joinRes.joinType': result.joinType,
      isBigRoom,
      selfRole,
      isSelfOnStage,
      needJoinTrtc,
    }, null, 2)}`);
    // 加入音视频房间（互动班课或者老师角色）
    const rtcMode = !!classInfo.autoOpenMic || this.isTeacher();
    let enterRtcRoomErr = null;
    if (needJoinTrtc) {
      try {
        await this.enterRtcRoom(rtcMode);
        // 加入音视频房间成功
        this._infoWithLog('_joinClass', 'join class success and enterRtcRoom success');
        TState.instance.setState(TMainState.RTC_Mode, true);
      } catch (err) {
        // 进房成功但是加入音视频房间失败，算进房成功，然后在 _handleEnterRtcRoomFail 处理
        this._warnWithLog('_joinClass', `join class success but enterRtcRoom failed: ${err.toString()}`);
        TState.instance.setState(TMainState.RTC_Mode, false);
        enterRtcRoomErr = err;
      }
    } else {
      // 无需加入 trtc 房间
      TState.instance.setState(TMainState.RTC_Mode, false);
    }
    this.reportEvent('enter_class', classInfo, 0, this._enterTimeStamp);

    this._infoWithLog('_joinClass', '_afterJoinClass');
    this._afterJoinClass(result);

    this._infoWithLog('_joinClass', '_getClassMessageList');
    TIM.instance.getClassMessageList();

    // 前面有加入音视频房间失败
    if (enterRtcRoomErr) {
      this._handleEnterRtcRoomFail(enterRtcRoomErr, rtcMode);
    }
  }

  /**
   * 退房
   * @private
   * @param {boolean} needMemberQuit 当自己踢自己的时候，不需要调用退出课堂接口
   */
  private async _quitClass(needMemberQuit = true): Promise<void> {
    TEvent.instance.notify(TMainEvent.Before_Leave, {});
    try {
      this._info('_quitClass', `_isJoinedClass: ${this._isJoinedClass}, imKicked: ${TState.instance.getState(TMainState.IM_Kicked)}, trtcJoinState: ${TState.instance.getState(TMainState.Joined_TRTC)}`);
      if (this._isJoinedClass) {
        // 更新进房状态
        this._isJoinedClass = false;
        TState.instance.setState(TMainState.Joined_Class, false, this.constructor.name);
        TSession.instance.addLog('Joined_Class false');

        this._updateClassHeartbeat(0);  // 停掉心跳
        let exitPromise = Promise.resolve();
        if (!TState.instance.getState(TMainState.IM_Kicked)) {    // IM被踢时，不需要调用退出课堂接口，否则会导致新设备上账号掉线
          TIM.instance.logout();
          if (needMemberQuit) {
            exitPromise = TBusinessMember.instance.memberQuit(this.getClassInfo().classId, TSession.instance.getToken())
              .catch((error) => {
                this._warn('_quitClass', `failed: ${error.toString()}`);
                return Promise.resolve();
              });
          }
        }
        let trtcUnInitPromise = Promise.resolve();
        // 正在进RTC房间的也要quit
        if (TState.instance.getState(TMainState.Joined_TRTC)) {
          trtcUnInitPromise = TTrtc.instance.quit()
            .then(() => {
              this._isAnchor = false; // 重置trtc角色
              TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);
              return TTrtc.instance.unInit();
            })
            .catch((err) => {
              this._warn('_quitClass', `quitRtcRoom failed: ${err.toString()}`);
              return Promise.resolve();
            });
        }
        return Promise.all([exitPromise, trtcUnInitPromise]).then(() => Promise.resolve());
      }
      return Promise.resolve();
    } finally {
      TEvent.instance.notify(TMainEvent.After_Leave, {});
    }
  }

  /**
   * 更新学生推流状态
   * @param publishStatus
   * @private
   */
  private async _updateStudentPublishStatus(publishStatus: boolean) {
    this._info('_updateStudentPublishStatus', `value=>${publishStatus}`);
    if (this.isStudent()) {
      try {
        if (publishStatus) { // 推流则不mute
          await this.muteLocalAudio(false);
          await this.muteLocalVideo(false);
        } else { // 不推流则mute
          await this.muteLocalAudio(true);
          await this.muteLocalVideo(true);
        }
      } catch (e) {
        this._error('_updateStudentPublishStatus', `value=>${e.message}`);
      }
    }
  }

  /**
   * 加入课堂后处理
   * @param result
   * @private
   */
  private async _afterJoinClass(result: TMemberJoinResult) {
    this._info('_afterJoinClass', `enter=>${window.location.href}`);
    this._reportEvent('join_class', result);

    TAV.instance.init();

    // 添加音视频状态上报
    const _stateReport = (type: TStateType, status: number): Promise<void> => {
      const params: TMemberStateReportParam = {
        classId: TSession.instance.getClassId(),
        type,
        state: 0,
      };
      const { typeName, stateNameMap }: {
        typeName: string;
        stateNameMap?: any;
      } = TMemberStateConfig[type] || { typeName: `${type}-other` };
      const stateName = stateNameMap ? (stateNameMap[status] || `${status}-failed`) : status;
      console.log('=========stateReport=====', typeName, stateName);
      // 屏幕分享及页面可见状态强转为数字去报，音视频状态视情况取枚举值
      if (type === TStateType.Screen || type === TStateType.Visible) {
        params.state = +status;
      } else {
        params.state = status;
      }
      return TBusinessMember.instance.stateReport(params, TSession.instance.getToken());
    };

    // 视频
    const stateNamesMap = {
      camera: {
        deviceStatus: TMainState.Video_Device_Status,
        publish: TMainState.Video_Publish,
        reporting: TMainState.Reporting_Camera_State,
      },
      mic: {
        deviceStatus: TMainState.Audio_Device_Status,
        publish: TMainState.Audio_Publish,
        reporting: TMainState.Reporting_Mic_State,
      },
    };
    const updateReportDeviceState = (deviceType: 'camera' | 'mic') => {
      const stateNames = stateNamesMap[deviceType];
      if (!stateNames) {
        return;
      }
      const deviceStatus = TState.instance.getState(stateNames.deviceStatus);
      const publishState = TState.instance.getState(stateNames.publish);
      const reportingState = publishState ? TDeviceStatus.Open : (
        TTrtc.isDeviceAbnormal(deviceStatus) ? deviceStatus : TDeviceStatus.Closed
      );
      console.log('updateReportDeviceState', deviceType, {
        deviceStatus,
        publishState,
        reportingState,
      });
      TState.instance.setState(stateNames.reporting, reportingState);
    };
    TState.instance.subscribeState(TMainState.Video_Device_Status, _ => updateReportDeviceState('camera'));
    TState.instance.subscribeState(TMainState.Video_Publish, _ => updateReportDeviceState('camera'));
    TState.instance.setReport(
      TMainState.Reporting_Camera_State,
      stateValue => _stateReport(TStateType.Camera, stateValue),
    );

    // 音频
    TState.instance.subscribeState(TMainState.Audio_Device_Status, _ => updateReportDeviceState('mic'));
    TState.instance.subscribeState(TMainState.Audio_Publish, _ => updateReportDeviceState('mic'));
    TState.instance.setReport(
      TMainState.Reporting_Mic_State,
      stateValue => _stateReport(TStateType.Mic, stateValue),
    );

    // 屏幕共享
    const screenShare$ = new Subject<number>();
    const vodPlay$ = new Subject<number>();
    const subCamera$ = new Subject<number>();
    const musicPlay$ = new Subject<number>();
    combineLatest([
      screenShare$,
      vodPlay$,
      subCamera$,
      musicPlay$,
    ])
      .pipe(
        map((states: any) => {
          if (states.includes(0)) return 0;
          if (states.includes(3)) return 3;
          if (states.includes(1)) return 1;
          return 2;
        }),
        distinctUntilChanged(),
        throttleTime(0),
      )
      .subscribe((stateValue: any) => {
        TState.instance.setState(TMainState.Reporting_Screen_State, stateValue);
      });

    TState.instance.subscribeState(TMainState.Screen_Share, stateValue => screenShare$.next(stateValue));
    TState.instance.subscribeState(TMainState.Vod_Play, stateValue => vodPlay$.next(stateValue));
    TState.instance.subscribeState(TMainState.Sub_Camera, stateValue => subCamera$.next(stateValue));
    TState.instance.subscribeState(TMainState.Music_Play, stateValue => musicPlay$.next(stateValue));
    TState.instance.setReport(
      TMainState.Reporting_Screen_State,
      stateValue => _stateReport(TStateType.Screen, stateValue),
    );

    TState.instance.subscribeState(TMainState.Stage_Status, (onStage) => {
      if (onStage) {  // 只有在台上时才启用可见性上报
        TState.instance.setReport(TMainState.Class_Visible, stateValue => _stateReport(TStateType.Visible, stateValue));
        // 如果在台上， 则学生可推流
        this._updateStudentPublishStatus(true);
      } else {
        TState.instance.setReport(TMainState.Class_Visible, null);
        // 如果在台下， 则学生不推流
        this._updateStudentPublishStatus(false);
      }
    });

    TState.instance.subscribeState(TMainState.Class_Status, (status) => {
      if (status === TClassStatus.Has_Ended || status === TClassStatus.Has_Expired) {
        // 课堂结束或过期，不再上报音视频状态和可见性
        TState.instance.setReport(TMainState.Reporting_Camera_State, null);
        TState.instance.setReport(TMainState.Reporting_Mic_State, null);
        TState.instance.setReport(TMainState.Reporting_Screen_State, null);
        TState.instance.setReport(TMainState.Class_Visible, null);
      }
      // 没开始上课， 学生不推流
      if (status === TClassStatus.Not_Start) {
        this._updateStudentPublishStatus(false);
      }
    });

    TState.instance.subscribeState(TMainState.IM_Kicked, (kicked) => {
      if (kicked) {
        this._quitClass()
          .then(() => {  // 先退房再发通知
            TEvent.instance.notify(TMainEvent.Kick_Out_By_Another, {});
          })
          .catch((err) => {
            TEvent.instance.notify(TMainEvent.Kick_Out_By_Another, {});
          });
      }
    });
    TState.instance.subscribeState(TMainState.IM_SIG_EXPIRED, (kicked) => {
      if (kicked) {
        this._quitClass()
          .then(() => {  // 先退房再发通知
            TEvent.instance.notify(TMainEvent.Kick_Out_By_Expire, {});
          })
          .catch((err) => {
            TEvent.instance.notify(TMainEvent.Kick_Out_By_Expire, {});
          });
      }
    });

    TStore.instance.init();  // 初始化TStore模块
    // 更新禁言状态
    TStore.instance.notifyChatPermission(result.members);

    // 进入房间后再拉取最新的状态，避免进入过程中状态变化导致的不一致，例如进入过程中老师开课
    try {
      await this.updateClassInfo();
    } catch (err) {
      this._info('_afterJoinClass updateClassInfo error', JSON.stringify(err));
    }

    const classInfo = this.getClassInfo();

    // !!! 这里处理进房后的一些状态更新及事件触发，一般情况下，遵循以下原则
    // 1. 状态尽可能在进房回调触发前配置好，这样方便使用者在进房回调中能正常读取到状态
    // 2. 其它事件回调尽可能进房回调后再触发，这样如果使用者在进房回调里配置事件监听，也能正常收到回调

    // 更新板书生成开关
    this._localFeatureMap.set('WhiteBoardSnapShot', !!classInfo.whiteBoardSnapshotMode);


    // 更新设备方向
    let deviceOrientation = TDeviceOrientation.Landscape;
    const angle = this.getOrientationAngle();
    if (this.isMobile()) {
      if (!this.isMobileNative() && !this.isMiniProgramWebview()) {
        if (this.isOneOnOneClass() || (!this.isPortraitClass())) {
          // 老师竖屏加入课堂给出切换横屏的提示
          if (angle === 180 || angle === 0) {
            if (window.showToast) {
              window.showToast(i18next.t('为最佳体验，请使用横屏。'));
            } else {
              this.showMessageBox(
                i18next.t('提示'),
                [
                  i18next.t('为了保证最佳体验，请使用横屏。'),
                  i18next.t('注意：请先解除手机方向锁定'),
                ].join('<br>'),
                [i18next.t('确定')],
                () => { },
              );
            }
          }
          deviceOrientation = (angle === 180 || angle === 0)
            ? TDeviceOrientation.Portrait : TDeviceOrientation.Landscape;
        }
      }
      if (this.isStudent() && !this.isOneOnOneClass() && !this.isPad()) {
        deviceOrientation = (angle === 180 || angle === 0)
          ? TDeviceOrientation.Portrait : TDeviceOrientation.Landscape;
        if (this.isLiveClass()) {
          // 公开课主题背景为白色
          document.body.style.backgroundColor = '#fff';
        };
      }
      // 如果是竖屏直播课，老师设备方向是竖屏
      if (this.isPortraitClass() && !this.isStudent()) {
        deviceOrientation = TDeviceOrientation.Portrait;
      }
    }
    this._info('deviceOrientation', `${deviceOrientation}|${this.getParams('defaultDeviceOrientation', deviceOrientation)}`);
    /**
       * 注释掉没有默认方向会引起布局异常，带上默认值会自动修改方向
       */
    this.setDeviceOrientation(Number(this.getParams('defaultDeviceOrientation', deviceOrientation)));

    // 更新课堂状态
    TState.instance.setState(TMainState.Class_Status, classInfo.status, this.constructor.name);

    // 更新布局信息
    this._updateClassLayout();

    // 更新全员静音状态
    TState.instance.setState(TMainState.Mute_All, classInfo.muteAll === 1, this.constructor.name);

    // 更新全员视频状态
    TState.instance.setState(TMainState.Mute_Video_All, classInfo.muteVideoAll === 1, this.constructor.name);

    // 处理全员禁言
    TState.instance.setState(TMainState.Silence_All, classInfo.silenceAll === 1, this.constructor.name);
    TState.instance.setState(TMainState.Silence_Mode, classInfo.silenceMode, this.constructor.name);

    // 处理上麦
    TState.instance.setState(TMainState.Enable_Stage, classInfo.enableStage === 1, this.constructor.name);

    console.log('rtcInfos: --> updagte tim---->');
    // 更新课堂心跳时间
    this._updateClassHeartbeat(1000);

    // 更新进房状态
    this._isJoinedClass = true;
    TState.instance.setState(TMainState.Joined_Class, true, this.constructor.name);
    TSession.instance.addLog('Joined_Class true');

    // 进房成功事件上报
    TEvent.instance.notify(TMainEvent.After_Enter, classInfo);
    this._reportEvent('after_enter_class', classInfo);
    this._flushLog();

    // 页面关闭前退房
    window.addEventListener('beforeunload', async (event) => {
      this._warn('beforeunload', event.type);
      await TLogger.flushReport();
    });

    window.addEventListener('pagehide', async (event) => {
      this._warn('pagehide', event.type);
      TTrtc.instance.removeLogCallback();
      await TLogger.flushReport();
      // 更新进房状态
      if (this._isJoinedClass) {  // 被踢时不应退出业务课堂
        this._isJoinedClass = false;
        TState.instance.setState(TMainState.Joined_Class, false, this.constructor.name);
        await TBusinessMember.instance.syncMemberQuit(TSession.instance.getClassId(), TSession.instance.getToken());
      }
      await TLogger.flushReport();
    });

    // 更新权限列表
    const permissionList: TPermissionInfo[] = [];
    // todo 接口问题 从member/join里来的数据
    result.members.forEach((member: TMemberInfo) => {
      permissionList.push(member);
    });
    this._updatePermissionList(permissionList, TPermissionUpdateReason.Init, result.permissionListSeq, { dataFrom: 'member/join' });

    const reason = `afterJoinClass, joinRes.timestamp ${result.timestamp}, classInfo.updateAt ${classInfo.updateAt}`;

    // 更新点赞数量
    this._updateLikeCount(result.likeNumber, reason);

    // 更新成员数量
    this._updateMemberCount(result.studentsOnlineMember, reason);

    // 更新直播流
    this._updateStreams(result.streams, result.streamListSeq);

    // 先更新课堂最新seq字段，避免第一个心跳就会去拉全量任务数据
    this._lastTaskSeqOfClass = classInfo.lastTaskSeq;
    // 记录最新的任务信息
    this._lastTaskInfos = classInfo.tasks;
    // 处理获取到的任务列表
    classInfo.tasks.forEach((taskInfo) => {
      this._processTask(taskInfo);
    });

    // 检测课中答题
    this.getQuestions()
      .then((ret) => {
        ret.questionInfos.forEach((question) => {
          if (question.state === 0 || question.state === 1) {
            this.emit(TMainEvent.Question_Valid, {
              ...question,
              answer: ret.answerInfos.find(item => item.questionId === question.questionId),
            });
          }
        });
      })
      .catch((err) => {
        this._warn('_afterJoinClass', `check question fail: ${JSON.stringify(err)}`);
      });

    if (window.Notification && Notification.permission === 'default') { // 进课堂后请求通知权限
      Notification.requestPermission();
    }

    // 获取学校水印信息
    TBusinessSchool.instance.getWaterMark(
      TSession.instance.getSchoolId(),
      TSession.instance.getToken(),
    ).then((info: TSchoolWaterMark) => {
      this._info('_afterJoinClass', `custom: ${this._useCustomWaterMark}, waterMark: ${JSON.stringify(info)}`);
      if (!this._useCustomWaterMark) {
        this._waterMark = info;
        this.notify(TMainEvent.WaterMark_Update, this.getWaterMarkParam());
      }
    });

    // 强制拉取下状态信息
    this.getUserCommand();
  }

  /**
   * 进入音视频房间失败后的处理
   */
  private _handleEnterRtcRoomFail(err: any, rtcMode: boolean, retryType?: string) {
    const infoAction = 'trtc._handleEnterRtcRoomFail';
    this._warn(infoAction, `enterRtcRoom failed: ${err.toString()}, isJoinedClass ${this._isJoinedClass}, retryType ${retryType || ''}`);

    // 清理
    this._isAnchor = false; // 失败重置trtc角色
    this._switchRolePromise = null;

    TState.instance.setState(TMainState.Joined_TRTC, false, this.constructor.name);

    if (!this._isJoinedClass) {
      // 还没完成进房
      return;
    }

    // 不是重试的，自动重试一次
    if (!retryType) {
      this._info(infoAction, 'autoRetry');
      this.enterRtcRoom(rtcMode, 'autoRetry');
      return;
    }
    // 记录真正的进房失败，即经过重试后仍失败，则认为是一次真正的进房失败
    this._error(infoAction, 'enterRtcRoom failed');
    this.showErrorMsgBox({
      message: err.errorMsg,
      module: err.module || 'TCIC',
      code: -1,
      buttons: [
        i18next.t('重试'),
      ],
      callback: (index) => {
        this._info(infoAction, `msgBox callback, index ${index}`);
        if (index === 0) {
          // 重试
          this._info(infoAction, 'userRetry');
          this.enterRtcRoom(rtcMode, 'userRetry');
        } else {
          // 退出课堂
          this._info(infoAction, 'quitClass and unInitialize');
          this._quitClass();
          this.unInitialize();
        }
      },
    });

    // this.showMessageBox('', err.errorMsg, [
    //   i18next.t('重试'),
    //   // this.getNameConfig().roomInfo.leaveRoom, // 不显示这个入口
    // ], (index) => {
    //   this._info(infoAction, `msgBox callback, index ${index}`);
    //   if (index === 0) {
    //     // 重试
    //     this._info(infoAction, 'userRetry');
    //     this.enterRtcRoom(rtcMode, 'userRetry');
    //   } else {
    //     // 退出课堂
    //     this._info(infoAction, 'quitClass and unInitialize');
    //     this._quitClass();
    //     this.unInitialize();
    //   }
    // });
  }

  /**
   * 动态更新课堂时长
   * @param adjustDuration 要校正的课堂时长
   * @private
   */
  private _updateClassDuration(adjustDuration?: number) {
    const setDurationInterval = (duration: number) => {
      this.clearDurationInterval();
      TState.instance.setState(TMainState.Class_Duration, adjustDuration, this.constructor.name, false);
      if (duration === 0) {
        return;
      }
      const start = Date.now();
      const initDura = duration;
      let dura = duration;
      // 设置新的定时器
      this._classDurationInterval = window.setInterval(() => {
        if (initDura === 0) {  // 倒数计时归0，清除已有的定时器
          this.clearDurationInterval();
        }
        // 每次都计算绝对间隔
        duration = Math.ceil((Date.now() - start) / 1000) + initDura;
        dura += 1;
        if (Math.abs(dura - duration) > 2) {
          this._warn('setDurationInterval', `${dura}, ${duration}`);
        }
        TState.instance.setState(TMainState.Class_Duration, duration, this.constructor.name, false);
      }, 1000);
    };
    const classInfo = this.getClassInfo();
    if (typeof adjustDuration !== 'number') {  // 未传入参数，初始化分支
      if (classInfo.status === TClassStatus.Not_Start) {  // 课堂未开始上课
        adjustDuration = Math.round(+new Date() / 1000.0) - classInfo.startTime;  // 计算距离约课开课时间的时长
        if (adjustDuration > 0) {  // 已到上课时间，但是未开始上课，直接置零
          adjustDuration = 0;
        }
      } else if (classInfo.status === TClassStatus.Already_Start) {  // 已开始上课
        const realStartTime = classInfo.realStartTime || classInfo.startTime || (Date.now() / 1000);
        if (!classInfo.realStartTime) {
          this._warn('_updateClassDuration', `${realStartTime}, ${JSON.stringify(classInfo)}`);
        }
        adjustDuration = Math.round(+new Date() / 1000.0) - realStartTime;  // 计算距离实际上课时间的时长
        if (adjustDuration <= 0) {
          this._warn('_updateClassDuration', `adjustDuration:${adjustDuration}, ${realStartTime},${JSON.stringify(classInfo)}`);
          adjustDuration = 1;
        }
      } else {
        // 剩余情况为已结束或已过期，保持为0
        this._warn('_updateClassDuration', `else: ${JSON.stringify(classInfo)}`);
        adjustDuration = 0;
      }
    } else {  // 校正参数
      if (classInfo.status !== TClassStatus.Already_Start) {  // 只有课堂状态为已开始上课才处理
        return;
      }
      // 只有误差超过2秒时才校正，避免网络抖动影响
      if (Math.abs(adjustDuration - TState.instance.getState(TMainState.Class_Duration, 0)) <= 2) {
        return;  // 传入校正参数，但是和当前时间误差较小，不做任何处理
      }
    }
    setDurationInterval(adjustDuration || 0);
  }

  /**
   * 更新课堂布局信息
   * @private
   */
  private _updateClassLayout() {
    const layout = this.getClassLayout();
    TState.instance.setState(TMainState.Class_Layout, layout, this.constructor.name);
  }

  /**
   * 更新心跳
   * 注： 这个方法里面抛错误会被吞掉，不知道在哪里被catch了
   * @param interval
   * @private
   */
  private _updateClassHeartbeat(interval: number) {
    // if (this._heartbeatInterval !== interval) {
    this._info('_updateClassHeartbeat', `${interval}`);
    if (this._heartBeatTimer) {
      clearTimeout(this._heartBeatTimer);
      this._heartBeatTimer = 0;
    }
    this._heartbeatInterval = interval;
    if (this._heartbeatInterval > 0) {
      // 改为setTimeout, 防止事件队列阻塞堆积.
      const looper = () => {
        clearTimeout(this._heartBeatTimer);
        this._heartBeatTimer = window.setTimeout(async () => {
          try {
            await this._heartBeat();
          } finally {
            // if (this._heartBeatTimer) {
            //   setTimeout(() => {
            //     looper();
            //   }, 0);
            // }
          }
        }, interval);
      };
      looper();
    }
    // }
  }

  private async _heartBeat() {
    // () => {
    if (!this._isJoinedClass) {    // 退出课堂后不再上报
      clearTimeout(this._heartBeatTimer);
      this._heartBeatTimer = 0;
      return;
    }
    let infos: any = {};
    const role = (TSession.instance.isTeacher() && 'teacher')
      || (TSession.instance.isSupervisor() && 'supervisor')
      || (TSession.instance.isAssistant() && 'assistant')
      || 'student';
    // infos = TTrtc.instance.getRtcInfo();
    try {
      infos = TTrtc.instance.getRtcInfo();
      const tid = this.getClassInfo().teacherId;
      const calculateResulutionFromStr = function (str: string) {
        const splitedResulution = str.split('x');
        return Number(splitedResulution[0]) * Number(splitedResulution[1]);
      };
      /**
     * 如果是学生只上报老师的音视频信息
     */
      if (this.getUserRole() === 'student' && tid) {
        // {Resolution: string ;UserId: string; StreamType: 'main'}
        /**
         * 暂时只观测老师的摄像头区域
         */
        const tvideoInfo = infos.video.find((item: any) => item.UserId === tid && item.StreamType === 'main');
        let tearcherVideoSize = 0;
        let teacherAudioVolumes = 0;
        if (tvideoInfo) {
          tearcherVideoSize = calculateResulutionFromStr(tvideoInfo.Resolution);
        }
        const taudioInfo = infos.audio.get(tid);
        if (taudioInfo) {
          let calculate = taudioInfo;
          if (taudioInfo.length > 50) {
            calculate = taudioInfo.slice(50);
          }
          teacherAudioVolumes = calculate.reduce((itemA: number, itemB: number) => itemA + itemB);
        }
        console.log('willReport teacherMedia', tearcherVideoSize, teacherAudioVolumes);
        this.reportLog('teacherMedia', `${tearcherVideoSize}`, {
          ext: `${teacherAudioVolumes}`,
        });
        TEvent.instance.notify('tevent@teacherMedia', {
          video: tearcherVideoSize,
          audio: teacherAudioVolumes,
        });
      }

      /**
       * 如果是老师，上报老师能看到几个学生
       */
      if (tid === this.getUserId()) {
        // {Resolution: string ;UserId: string; StreamType: 'main'}
        const svideoInfos = infos.video.filter((item: any) => {
          const memberItem = TBusinessMember.instance.getMemberByUserIdFromCache(item.UserId);
          if (memberItem && (memberItem.role === TMemberRole.Student)) {
            return true;
          }
          return false;
        });
        let canSeeStudentCounter = 0;
        let seeLen = svideoInfos.map((vinfo: any) => calculateResulutionFromStr(vinfo.Resolution));
        seeLen = seeLen.filter((relutionVal: number) => relutionVal > 0);
        canSeeStudentCounter = seeLen.length;
        let canhearStudentCounter = 0;
        /**
         * 把老师自己的数据从里面删掉
         */
        infos.audio.delete(tid);
        let totalvolumes: number[] = [];
        infos.audio.forEach((volumeInfo: any) => {
          totalvolumes.push(volumeInfo.reduce((itemA: number, itemB: number) => itemA + itemB));
        });
        totalvolumes = totalvolumes.filter(item => item > 0);
        canhearStudentCounter = totalvolumes.length;
        this.reportLog('studentMedia', `${canSeeStudentCounter}`, {
          ext: `${canhearStudentCounter}`,
        });
        TEvent.instance.notify('tevent@studentMedia', {
          canSeeStudentCounter,
          canhearStudentCounter,
        });
        console.log('willReport studentMedia', canSeeStudentCounter, canhearStudentCounter);
      }

      // 发送网络检测信息
      this._checkAndSendNetworkStatus();
    } catch (err) {
      console.error(err);
      this.reportLog('mediaError', `${err}`);
    }

    // this.reportLog('rtcInfo',)

    await TBusinessMember.instance.heartbeat(
      TSession.instance.getClassId(),
      role,
      {
      } as any, // 这里不再上报，前面已经更新上报方案
      TSession.instance.getToken(),
    )
      .then((result: TMemberHeartbeat) => {
        // if (result.interval >= 500) { // 更新心跳间隔
        this._updateClassHeartbeat(TMain._getClientHeartbeatInterval(result.interval || this._heartbeatInterval));
        // }
        TState.instance.setState(
          TMainState.Network_Broken,
          false,
          this.constructor.name,
          true,
          'heartbeat',
        );
      })
      .catch((error) => {
        if (error.errorCode === 10622) {
          // / 停止心跳，并且提示。防止心跳停止了，但是用户仍然在房间中，导致统计异常
          this._updateClassHeartbeat(0);
          this.showMessageBox(
            i18next.t('提示'),
            i18next.t('您不在课堂，请重新加入'),
            [i18next.t('确定')],
            () => {
              const reload = window.customReload || window.location.reload;
              reload();
            },
          );
        } else if (error.errorCode === 10624) {  // 被踢通知
          TEvent.instance.notify(TMainEvent.Kick_Out_By_Teacher, false);
        } else if (error.errorCode === 10625) {  // 被踢通知，不可再进房
          TEvent.instance.notify(TMainEvent.Kick_Out_By_Teacher, true);
        } else {  // 请求失败或者请求成功但未收到响应，或后台返回异常错误码（可能是后台出问题了）
          TState.instance.setState(
            TMainState.Network_Broken,
            true,
            this.constructor.name,
            true,
            'heartbeat',
          );
          this._updateClassHeartbeat(TMain._getClientHeartbeatInterval(this._heartbeatInterval));
        }
        this._warn('_updateClassHeartbeat', `failed: ${error.toString()}`);
      });
    // }
  }

  // 1v1课堂内向对方发送自己网络信息
  private async _checkAndSendNetworkStatus() {
    const isOneOnOne = this.isOneOnOneClass();
    const plist = this.getPermissionList() || [];
    const myselfIsTeacher = this.isTeacher();
    const currClassStatus = TState.instance.getState(TMainState.Class_Status);
    if (isOneOnOne) {
      let targetUserId = '';
      try {
        const quality = TState.instance.getState(TMainState.Network_Quality_Status, 1);
        // 非ready this.sendC2CCustomMessage 报错.
        await TState.instance.promiseState(TMainState.IM_Logined, true);
        // 取出targetUserId
        for (let i = 0; i < plist.length; i++) {
          const p = plist[i];
          if (p.userId !== this.getUserId()) {
            targetUserId = p.userId;
            // 只给台上的发消息
            if (p.stage) {
              await this.sendC2CCustomMessage(targetUserId, 'networkStats', `${quality}`);
            }
            // 老师给学生发送课堂状态信息
            if (myselfIsTeacher && this.isStudent(targetUserId)) {
              await this.sendC2CCustomMessage(targetUserId, 'classStatus', `${currClassStatus}`);
            }
            break;
          }
        }
      } catch (e) {
        console.error('checkAndSendNetworkStatus error', e);
      }
    }
  }

  /**
   * 更新成员数量
   * @param {number} onlineNumber 当前在线人数（包含老师等非学生角色）
   * @private
   */
  private _updateMemberCount(onlineNumber: number, reason = '') {
    if (!Number.isInteger(onlineNumber)) return;
    // const teacherId = this.getClassInfo().teacherId;
    // const assistants = this.getClassInfo().assistants;
    // const permissionList = this.getPermissionList();
    // const ignorePermission = permissionList.filter(permission => permission.userId === teacherId
    //   || assistants.includes(permission.userId));
    // const onlineStudent = Math.max(onlineNumber - ignorePermission.length, 0);
    // console.log(
    //   `_updateMemberCount ${onlineNumber}, student ${onlineStudent}, reason ${reason},`,
    //   `${Date.now() - this._localTimestampAfterJoin}ms after join`,
    // );
    TState.instance.setState(TMainState.Member_Count, onlineNumber, this.constructor.name);
  }

  /**
   * 更新点赞数量
   * @param {number} likeNumber 当前总点赞数量
   * @private
   */
  private _updateLikeCount(likeNumber: number, reason = '') {
    if (!Number.isInteger(likeNumber)) return;
    TState.instance.setState(TMainState.Like_Count, likeNumber, this.constructor.name);
  }
  /**
   * 监听IM消息
   * @private
   */
  private _processIMMsg() {
    this.on(TIMEvent.Recv_Custom_Msg, (msg: TIMMsg) => {
      try {
        if (msg.convType === TIMConvType.Group) {
          // 信令群组消息
          this._info('_processIMMsg', `cmd msg: ${JSON.stringify(msg)}`);
          if (msg.ext === TBoard.EXT_WHITEBOARD) {
            TBoard.instance.addSyncData(msg.data);
            console.log('%c [ addSyncData ]-6153', 'font-size:13px; background:pink; color:#bf2c9f;', msg.data);
          } else if (msg.ext === EXT_CTRL) {
            let data = msg.data;
            if (msg.desc) {
              console.time('gzip');
              const desc = JSON.parse(msg.desc);
              if (desc.gzip) {
                // 先base64解码
                const decodedData = window.atob(msg.data);
                // 字符串转数组 在循环返回一个 Unicode表所在位置的新数组
                const charData = decodedData.split('').map(x => x.charCodeAt(0));
                // Uint8Array 数组类型表示一个8位无符号整型数组，创建时内容被初始化为0。创建完后，可以以对象的方式或使用数组下标索引的方式引用数组中的元素。
                const binData = new Uint8Array(charData);
                // 解压缩
                data = pako.inflate(binData, { to: 'string' });
              }
              console.timeEnd('gzip');
            }
            this._processCTRLMsg(data, msg);
          } else {
            TEvent.instance.notify(TMainEvent.Recv_Custom_IM_Msg, msg);
          }
        } else if (msg.convType === TIMConvType.C2C && msg.from === 'saasadmin') {   // 处理管理员信令
          this._processAdminMsg(msg.data, msg);
        } else if (msg.convType === TIMConvType.C2C && msg.ext === 'networkStats') {
          this._processC2CNetworkMsg(msg.data, msg);
        } else if (msg.convType === TIMConvType.C2C && msg.ext === 'classStatus') {
          this._processC2CClassStatusMsg(msg.data, msg);
        }
      } catch (err) {
        this._warn('_processIMMsg', `parse msg fail: ${msg}`);
      }
    });
  }

  /**
   * 发送控制信令
   * @param action
   * @private
   */
  private _sendCtlMsg(action: string) {
    const request = {
      user_id: TSession.instance.getUserId(),
      time: new Date(),
      type: 'control',
      seq: new Date().getMilliseconds(),
      data: {
        action,
      },
    };
    const classInfo = this.getClassInfo();
    if (classInfo && classInfo.cmdGroupId) {
      return this.sendGroupCustomMessage(EXT_CTRL, JSON.stringify(request));
    }
    return Promise.reject('no class info');
  }
  /**
   * 处理下发的C2C网络消息
   * @param data
   * @param msg
   * @private
   */
  private _processC2CNetworkMsg(data: string, msg: TIMMsg) {
    try {
      const targetUserId = msg.from;
      const networkQuality = parseInt(data, 10) || 0;
      const isOneOnOne = this.isOneOnOneClass();
      if (isOneOnOne) {
        TEvent.instance.notify(TStatisticsEvent.Remote_Network_Statistics, {
          userId: targetUserId,
          networkQuality,
        });
      }
    } catch (e) {
      console.error('_processC2CNetworkMsg error ', e);
    }
  }

  private _processC2CClassStatusMsg(data: string, msg: TIMMsg) {
    try {
      const targetUserId = msg.from;
      const classStatus = parseInt(data);
      const isOneOnOne = this.isOneOnOneClass();
      const myselfIsStudent = this.isStudent();
      const currStatus = this.getState(TMainState.Class_Status);
      // 如果当前是学生且未上课， 但老师端消息证明已开课则变更状态
      // 覆盖极小概率老师端开课后学生端未收到开课消息的情况
      if (isOneOnOne && myselfIsStudent && this.isTeacher(targetUserId) && classStatus !== currStatus) {
        if (currStatus === TClassStatus.Not_Start && classStatus === TClassStatus.Already_Start) {
          this.setState(TMainState.Class_Status, TClassStatus.Already_Start);
        }
      }
    } catch (e) {
      console.error('_processC2CClassStatusMsg error ', e);
    }
  }

  /**
   * 处理管理员下发的C2C消息
   * @param data
   * @param msg
   * @private
   */
  private _processAdminMsg(data: string, msg: TIMMsg) {
    const req = JSON.parse(data);
    const classInfo = this.getClassInfo();
    if (req.class_id === classInfo.classId && req.type === 'v1/command/status') {
      // 当前教室的C2 信令状态消息
      TBusinessCommand.instance.handleCommandStatusMsg(req);
    }
    if (`${req.class_id}` === classInfo.cmdGroupId) {
      switch (req.type) {
        case 0:
          // 老师执行memberAction修改学生权限，但是学生之前权限已经是该状态，说明学生在获取权限后自己修改了设备状态
          // 忽略事件，上抛权限变更列表
          TEvent.instance.notify(TMainEvent.Recv_Member_Action, { action: req.action, operatorId: req.operator_id });
          break;
        case 1:
          // 通知老师有人回答了问题
          this.emit(TMainEvent.Question_Been_Answered, {
            questionId: req.data.question_id,
          });
          break;
        default:
          console.warn('unknown c2c msg', req);
          break;
      }
    }
  }

  /**
   * 处理内部消息
   * @param data
   * @param msg
   * @private
   */
  private _processCTRLMsg(data: string, msg: TIMMsg) {
    const req = JSON.parse(data);
    if (!req.data) {
      this._error('_processCTRLMsg', `received control msg without data: ${data}`);
      return;
    }
    if (req.data.action !== undefined) {
      this._info('_processCTRLMsg', `type: ${req.type}, action: ${req.data.action}, timestamp: ${req.timestamp}, req: ${JSON.stringify(req)}`);
    } else {
      // 无req.data.action的消息日志
      this._info(`_processCTRLMsg@${req.type}`, `req: ${JSON.stringify(req)}`);
    }
    if (req.type === 'event') {  // 事件通知
      const reason = `event:${req.data.action}`;
      switch (req.data.action) {
        case 'member_join':
          req.data.data.forEach((data: any) => {
            const userInfo = new TUserInfo();
            userInfo.deserialize(data);
            const { role, userId } = userInfo;
            // 如果进房角色是助教并且，classInfo 的助教列表里面不存在，需要手动更新下助教列表，主要应对dynamicRole 助教进房场景
            if (role === TMemberRole.Assistant) {
              const classInfo = this.getClassInfo();
              const assistants = classInfo.assistants;
              const isExist = assistants.includes(userId);
              if (!isExist) {
                classInfo.assistants.push(userId);
                TSession.instance.setClassInfo(classInfo);
              }
            }
            this._memberInfoList.set(userInfo.userId, userInfo);
            TEvent.instance.notify(TMainEvent.Member_Join, [userInfo.userId, msg], true, true);
          });
          this._updateMemberCount(req.data.student_online_number, reason);  // 更新成员数量
          // this._getPermissionList(reason);  // 主动更新权限列表, 兜底v1/permissions未更新的情况
          break;
        case 'change_member_info':
          req.data.data.forEach((data: any) => {
            const memberInfo = new TMemberInfo();
            memberInfo.deserialize(data);
            TEvent.instance.notify(TMainEvent.Member_Info_Update, memberInfo);
          });
          break;
        case 'member_quit':
          this._updateMemberCount(req.data.student_online_number, reason);  // 更新成员数量
          req.data.data.forEach((data: any) => {
            const userInfo = new TUserInfo();
            userInfo.deserialize(data);
            this._memberInfoList.set(userInfo.userId, userInfo);
            TEvent.instance.notify(TMainEvent.Member_Exit, [userInfo.userId, msg], true, true);
          });
          // this._getPermissionList(reason);  // 主动更新权限列表, 兜底v1/permissions未更新的情况
          break;
        case 'member_online':
          this._updateMemberCount(req.data.student_online_number, reason);  // 更新成员数量
          req.data.data.forEach((data: any) => {
            const userInfo = new TUserInfo();
            userInfo.deserialize(data);
            this._memberInfoList.set(userInfo.userId, userInfo);
            TEvent.instance.notify(TMainEvent.Member_Join, [userInfo.userId, msg], true, true);
          });
          // this._getPermissionList(reason);  // 主动更新权限列表, 兜底v1/permissions未更新的情况
          break;
        case 'member_offline':
          this._updateMemberCount(req.data.student_online_number, reason);  // 更新成员数量
          req.data.data.forEach((data: any) => {
            const userInfo = new TUserInfo();
            userInfo.deserialize(data);
            this._memberInfoList.set(userInfo.userId, userInfo);
            TEvent.instance.notify(TMainEvent.Member_Exit, [userInfo.userId, msg], true, true);
          });
          // this._getPermissionList(reason);  // 主动更新权限列表, 兜底v1/permissions未更新的情况
          break;
        // 1.6.2 member_hand_up由v1/hand_up替换
        // case 'member_hand_up':
        //   if (req.data.data.user_id !== TSession.instance.getUserId()) {  // 不处理自己的举手事件，因为调举手接口时已触发
        //     TEvent.instance.notify(TMainEvent.Member_Hand_Up, [{
        //       userId: req.data.data.user_id,
        //       stage: !!req.data.data.stage,
        //       handUpTimes: req.data.data.hand_up_times,
        //     }, msg], true, true);
        //   }
        //   break;
        // // 1.6.2 member_hand_up_cancel由v1/hand_up替换
        // case 'member_hand_up_cancel':
        //   if (req.data.data.user_id !== TSession.instance.getUserId()) {  // 不处理自己的取消举手事件，因为调举手接口时已触发
        //     TEvent.instance.notify(TMainEvent.Member_Hand_Up_Cancel, [{
        //       userId: req.data.data.user_id,
        //       stage: !!req.data.data.stage,
        //     }, msg], true, true);
        //   }
        //   break;
        case 'change_member_stream':
          if (req.data.stream_list_seq && req.data.data) {
            const streamList: TStreamInfo[] = [];
            req.data.data.forEach((streamJson: any) => {
              const stream = new TStreamInfo();
              stream.deserialize(streamJson);
              streamList.push(stream);
            });
            this._updateStreams(streamList, req.data.stream_list_seq);
          }
          break;
        case 'member_hand_up':          // 避免日志重复上报
          break;
        case 'change_member_permission':  // (旧协议)避免日志重复上报
          break;
        default:
          this._warn('_processCTRLMsg', `ignore unexpect event action: ${req.data.action}`);
          break;
      }
    } else if (req.type === 'control') {  // 控制信令
      const myId = TSession.instance.getUserId();
      /**
       * @todo [异常路径][2] 老师加入房间触发
       */
      switch (req.data.action) {
        // 1.6.2版本以后change_member_permission指令由v1/permissions替换
        // 但是 case 分支要保留，避免日志重复上报
        case 'change_member_permission':
          // if (req.data.permission_list_seq && req.data.objectId) {
          //   const permissionList: TPermissionInfo[] = [];
          //   req.data.objectId.forEach((memberJson: any) => {
          //     const member = new TMemberInfo();
          //     member.deserialize(memberJson);
          //     permissionList.push(member);
          //   });
          //   this._updatePermissionList(
          //     permissionList,
          //     req.data.reason || TPermissionUpdateReason.Unknown,
          //     req.data.permission_list_seq,
          //     { dataFrom: 'control:change_member_permission' },
          //   );
          // }
          break;
        case 'show_question':
          this.emit(TMainEvent.Question_Begin, {
            questionId: req.data.question_id,
            optionNumber: req.data.option_number,
            startTime: req.data.start_time,
            duration: req.data.duration,
            questionName: req.data.question_name,
            questionContent: req.data.question_content,
            correctAnswer: req.data.correct_answer,
            state: req.data.state,
            ttl: req.data.ttl,
            type: req.data.type,
          });
          break;
        case 'abandon_question':
          this.emit(TMainEvent.Question_Abandon, {
            questionId: req.data.question_id,
          });
          break;
        case 'ai_class_ready':
          this.emit(TMainEvent.AI_CLASS_READY, {
            taskId: req.data.data,
          });
          break;
        case 'close_question':
          this.emit(TMainEvent.Question_Close, {
            questionId: req.data.question_id,
          });
          break;
        case 'cancel_question':
          this.emit(TMainEvent.Question_End, {
            questionId: req.data.question_id,
          });
          break;
        // case 'mutemsg_all':
        //   TState.instance.setState(TMainState.Silence_All, true, this.constructor.name);
        //   break;
        // case 'unmutemsg_all':
        //   TState.instance.setState(TMainState.Silence_All, false, this.constructor.name);
        //   break;
        case 'kickoff':  // TODO: 原有协议，由于拼写错误原因废弃，保留的原因是为了向下兼容一段时间，等客户端版本覆盖率上去了以后再更新后台
        case 'kick_out':
          /**
           * 后台依赖kick_out来剔除重复登录的用户，但是无法区分前后登陆的终端
           * 如果踢出时间小于加入时间，说明加入是踢出之后的，此时为后一个登陆终端不做退房处理
           * 如果 this.nano_timestamp 值为 -1表示后台没有更新字段，不需要处理这个逻辑
           */
          if (this.nano_timestamp !== -1) {
            if (req.timestamp < this.nano_timestamp) {
              return;
            }
          }
          this._warn('kicked', `${myId},${this.objToString(req)}`);
          TLogger.flushReport();
          if (req.data.objectId.indexOf(myId) !== -1) {
            if (req.user_id === myId) {
              /**
               * 区分是否自己踢出, 自己踢出不调用 quit
               */
              this._quitClass(false).then(() => {
                TEvent.instance.notify(TMainEvent.Kick_Out_By_Self, false);
              });
            } else {
              this._quitClass().then(() => {
                TEvent.instance.notify(TMainEvent.Kick_Out_By_Teacher, false);
              });
            }
          }
          break;
        case 'kick_out_forever':
          if (req.data.objectId.indexOf(TSession.instance.getUserId()) !== -1) {
            this._quitClass().then(() => {
              TEvent.instance.notify(TMainEvent.Kick_Out_By_Teacher, true);
            });  // 直接退房
          }
          break;
        case 'class_start': {  // 课堂相关的几个通知，课堂信息中的状态字端都统一在Class_Status变化后去更新
          const classInfo = this.getClassInfo();
          classInfo.realStartTime = req.data.real_start_time;
          TSession.instance.setClassInfo(classInfo);
          TState.instance.setState(
            TMainState.Class_Status,
            TClassStatus.Already_Start,
            this.constructor.name,
          );
        }
          break;
        case 'class_end': {
          const classInfo = this.getClassInfo();
          classInfo.realEndTime = req.data.real_end_time;
          TSession.instance.setClassInfo(classInfo);
          TState.instance.setState(
            TMainState.Class_Status,
            TClassStatus.Has_Ended,
            this.constructor.name,
          );
        }
          break;
        case 'class_expire': {
          TState.instance.setState(
            TMainState.Class_Status,
            TClassStatus.Has_Expired,
            this.constructor.name,
          );
        }
          break;
        case 'class_info_change':
          // 更新课堂信息
          this._updateClassInfo(req.data);
          break;
        // case 'mute_mic_all':
        //   // 收到全员静音信令
        //   {
        //     const classInfo = this.getClassInfo();
        //     classInfo.muteAll = 1;
        //     TSession.instance.setClassInfo(classInfo);
        //     TState.instance.setState(TMainState.Mute_All, true, this.constructor.name);
        //   }
        //   break;
        // case 'unmute_mic_all':
        //   // 收到取消全员静音信令
        //   {
        //     const classInfo = this.getClassInfo();
        //     classInfo.muteAll = 0;
        //     TSession.instance.setClassInfo(classInfo);
        //     TState.instance.setState(TMainState.Mute_All, false, this.constructor.name);
        //   }
        //   break;
        case 'enable_stage': {
          // TODO : 学生收到允许上台开关通知
          this._updateClassInfo(req.data);
          break;
        }
        case 'msg_free': {
          this._updateClassInfo(req.data);
          break;
        }
        case 'msg_public_only': {
          this._updateClassInfo(req.data);
          break;
        }
        case 'msg_private_only': {
          this._updateClassInfo(req.data);
          break;
        }
        case 'msg_unallow': {
          this._updateClassInfo(req.data);
          break;
        }
        case 'class_task_change': {
          // 兼容单个任务和多个任务的消息格式
          let tasks = [];
          if (typeof req.data.data === 'object') {
            if (Array.isArray(req.data.data)) {
              tasks = req.data.data;
            } else {
              tasks.push(req.data.data);
            }
          } else {
            this._warn('_processCTRLMsg', `class_task_change with wrong content: ${JSON.stringify(req.data.data)}`);
          }
          tasks.forEach((task: any) => {  // 反序列化并处理任务
            const taskInfo = new TTaskInfo();
            taskInfo.deserialize({ task });
            this._processTask(taskInfo);
          });
        }
          break;
        default:
          this._warn('_processCTRLMsg', `ignore unexpect control action: ${req.data.action}`);
          break;
      }
    } else if (req.type === 'v1/stage') {
      const newList = new TCommandList(TCommandID.Stage);
      newList.deserialize(req.data);
      TBusinessCommand.instance.handleCommandMsg(TCommandID.Stage, newList);
    } else if (req.type === 'v1/stage_loop') {
      if (req.data && req.data.action) {
        this.notify(TMainEvent.Stage_Loop, req.data);
      }
    } else if (req.type === 'v1/sync') {
      const syncMsg = new TSyncInfo();
      syncMsg.deserialize(req.data);
      const reason = `v1/sync, classDuration ${syncMsg.classDuration}, classUpdateTimeStamp ${syncMsg.classUpdateTimeStamp}`;
      this._updateLikeCount(syncMsg.likeNumber, reason); // 更新点赞数量
      this._updateMemberCount(syncMsg.studentOnlineMember, reason);  // 更新成员数量
      this._updateClassDuration(syncMsg.classDuration);  // 校正上课时长
      this._updateTasks(syncMsg.classTaskSeq);  // 尝试更新任务列表
      // 检测课堂信息是否变更
      const classInfo = this.getClassInfo();
      if (syncMsg.classUpdateTimeStamp !== classInfo.updateAt) {
        this._updateClassInfo(null);
      }
      // 检测权限信息是否变更
      if (syncMsg.permissionSeq > this._permissionListSequence) {
        this._getPermissionList();  // 主动更新权限列表
      }
    } else if (req.type === 'v1/permissions') {
      /**
       * @todo [异常路径][3] 触发权限更新
       * 日志70 ：12-06 18:09:04.141
       */
      if (req.data.seq && req.data.permissions) {
        const permissionList: TPermissionInfo[] = [];
        req.data.permissions.forEach((memberJson: any) => {
          const permission = new TPermissionInfo();
          permission.deserialize(memberJson);
          permissionList.push(permission);
        });
        this._updatePermissionList(
          permissionList,
          req.data.reason || TPermissionUpdateReason.Unknown,
          req.data.seq,
          { dataFrom: 'v1/permissions' },
        );
      }
    } else if (req.type === 'v1/hand_up') {
      if (req.data) {
        TEvent.instance.notify(TMainEvent.Member_Hand_Up_Update, { ...req.data }, true, false);
      }
    } else if (req.type === 'v1/translate') {
      if (req.data) {
        TEvent.instance.notify(TMainEvent.Translate_Msg_Notify, { ...req.data }, true, false);
      }
    }
  }

  /**
   * 处理任务
   * @param taskInfo
   * @private
   */
  private _processTask(taskInfo: TTaskInfo) {
    this._missTaskSeqSet.delete(taskInfo.seq);  // 收到任务后，尝试从集合删除任务序列号
    // 只有本地之前未处理过该任务，或者收到的任务序列号比之前处理过的大，才进行处理，否则就是乱序消息，忽略
    if (!this._lastTaskSeq.hasOwnProperty(taskInfo.taskId) || this._lastTaskSeq[taskInfo.taskId] < taskInfo.seq) {
      this._info('_processTask', `task: ${JSON.stringify(taskInfo)}, lastSeq: ${this._lastTaskSeq[taskInfo.taskId]}`);
      // 更新本地记录的最新序列号
      if (taskInfo.seq > this._lastTaskSeqOfClass) {
        this._updateTasks(taskInfo.seq - 1);  // 每次收到新任务后，都尝试更新任务，确保收齐当前任务之前的所有任务
        this._lastTaskSeqOfClass = taskInfo.seq;
      }
      this._lastTaskSeq[taskInfo.taskId] = taskInfo.seq;
      // 更新本地缓存的任务列表，避免组件中getTasks获取到过期信息
      const idx = this._lastTaskInfos.findIndex((item: TTaskInfo) => item.taskId === taskInfo.taskId);
      if (idx >= 0) {
        this._lastTaskInfos[idx] = taskInfo;
      } else {
        this._lastTaskInfos.push(taskInfo);
      }
      // 触发事件回调
      TEvent.instance.notify(TMainEvent.Task_Updated, taskInfo);
    }
  }

  /**
   * 更新任务列表
   * @param lastSeq
   * @private
   */
  private _updateTasks(lastSeq: number) {
    for (let seq = this._lastTaskSeqOfClass + 1; seq <= lastSeq; seq++) {  // 将缺失的任务序列号都加入集合
      this._missTaskSeqSet.add(seq);
    }
    if (this._missTaskSeqSet.size === 0) return;  // 没有缺失任务，直接返回
    if (this._updateTasksTimer) return;  // 已存在待执行的更新任务
    // 考虑到任务更新的IM消息可能延迟到达，这里延迟500毫秒做对比更新操作，保证只在真正必要时做更新
    this._updateTasksTimer = window.setTimeout(() => {
      this._updateTasksTimer = 0;
      if (this._missTaskSeqSet.size > 0) {  // 只有存在缺失任务时，才需要更新
        const missSeqList = Array.from(this._missTaskSeqSet.values());
        this._info('_updateTasks', `${missSeqList}`);
        const classId = this.getClassInfo().classId;
        const token = TSession.instance.getToken();
        TBusinessClass.instance.getTasks(classId, Math.min(...missSeqList) - 1, token)
          .then((result) => {
            let lastSeq = 0;
            if (this._lastTaskSeqOfClass < result.lastSeq) {
              this._lastTaskSeqOfClass = result.lastSeq;
              this._lastTaskInfos = result.tasks;
            }
            result.tasks.forEach((taskInfo) => {
              this._processTask(taskInfo);
              lastSeq = Math.max(lastSeq, taskInfo.seq);
            });
            // 清除小于收到的最大seq的缺失任务记录（后台只会返回任务最新状态对应的seq，因此不是所有seq都会返回）
            this._missTaskSeqSet.forEach((seq) => {
              if (seq <= lastSeq) {
                this._missTaskSeqSet.delete(seq);
              }
            });
            this._updateTasks(0);  // 尝试再次触发更新，以便拉取到所有缺失任务
          })
          .catch((err) => {  // 理论上不应该发生，是可以忽略的错误
            this._warn('_updateTasks', `get tasks fail: ${err.toString()}`);
            this._updateTasks(0);  // 尝试再次触发更新，以便拉取到所有缺失任务
          });
      }
    }, 500);
  }

  /**
   * 更新白板/聊天权限
   * @param stageFlag
   * @param boardFlag
   * @param chatFlag
   * @param screenFlag
   * @param handUp
   * @param reason
   * @private
   */
  private _updateMyPermission(
    stageFlag: boolean,
    boardFlag: boolean,
    screenFlag: number,
    reason: TPermissionUpdateReason,
    operatorId?: string,
  ) {
    // 更新上台状态
    TState.instance.setState(TMainState.Stage_Status, stageFlag, this.constructor.name, true, reason, operatorId);
    if (this.isLiveClass()) { // 公开课上下台会更新RTC模式
      TState.instance.setState(TMainState.RTC_Mode, stageFlag, this.constructor.name, true, reason, operatorId);
    }
    // 更新白板权限
    if (TState.instance.getState(TMainState.Board_Permission) !== boardFlag) {
      this.reportEvent(boardFlag ? 'grant_board' : 'revoke_board', {});
    }
    TState.instance.setState(TMainState.Board_Permission, boardFlag, this.constructor.name, true, reason, operatorId);

    // 更新屏幕分享状态
    if (TState.instance.getState(TMainState.Screen_Share_Permission) !== screenFlag) {
      this.reportEvent(screenFlag ? 'grant_screen' : 'revoke_screen', { screen: screenFlag });
    }
    TState.instance.setState(
      TMainState.Screen_Share_Permission,
      screenFlag, this.constructor.name, true, reason, operatorId,
    );
  }

  /**
   * 更新权限列表
   * @param permissionList
   * @param reason
   * @param sequence
   * @param options
   * @private
   */
  private _updatePermissionList(
    permissionList: TPermissionInfo[],
    reason: TPermissionUpdateReason,
    sequence: number,
    { dataFrom = '' } = {},
  ) {
    if (!permissionList) {
      return;
    }
    if (sequence <= this._permissionListSequence) {
      this._warn('_updatePermissionList', `ignore list ${sequence}/${this._permissionListSequence}, reason ${reason}, dataFrom: ${dataFrom}, count: ${permissionList.length}`);
      return;
    }
    if (reason === TPermissionUpdateReason.Init) {
      this._info('_updatePermissionList', `seq: ${sequence}, reason ${reason}, dataFrom: ${dataFrom}, count: ${permissionList.length}, ${JSON.stringify(permissionList)}`);
    } else {
      this._info('_updatePermissionList', `seq: ${sequence}, reason ${reason}, dataFrom: ${dataFrom}, count: ${permissionList.length}`);
    }
    // 按进房时间排序，先进房在前面
    permissionList.sort((l: TMemberInfo, r: TMemberInfo) => l.lastEnterTime - r.lastEnterTime);
    this._permissionListSequence = sequence;

    const teacherId = this.getClassInfo().teacherId;
    const userArr: string[] = [];
    let currentUserInfo: TPermissionInfo = null;
    permissionList.forEach((info) => {
      if (info.userId === TSession.instance.getUserId()) {  // 记录当前用户信息
        currentUserInfo = info;
      }
      // 保证老师端权限永存(不会因断线重连而消失) begin
      if (info.userId === teacherId && !info.screen) {
        info.screen = 1;    // 校正老师的屏幕共享权限
      }
      if (info.userId === teacherId && !info.stage) {
        info.stage = true;    // 校正老师的上台权限
      }
      if (info.userId === teacherId && !info.board) {
        info.board = true;    // 校正老师的白板权限
      }
      // 保证老师端权限永存(不会因断线重连而消失) end
      userArr.push(info.userId);
      if (this._userAvMap.get(info.userId)) {  // 已存在
        const oldInfo = this._userAvMap.get(info.userId);
        if (info.stage) {   // 仍有权限
          if (oldInfo.camera !== info.camera || oldInfo.mic !== info.mic) {
            TEvent.instance.notify(TMainEvent.AV_Update, [info, reason], true, true);
            this._userAvMap.set(info.userId, info);
            if (info.userId === TSession.instance.getUserId()) {
              oldInfo.camera !== info.camera && this.reportEvent(info.camera ? 'grant_camera' : 'revoke_camera', info);
              oldInfo.mic !== info.mic && this.reportEvent(info.mic ? 'grant_mic' : 'revoke_mic', info);
            }
          }
        } else {
          this._userAvMap.delete(info.userId);
          TEvent.instance.notify(TMainEvent.Member_Stage_Down, info);
          // 如果不在台上就不显示视频列表中
          TEvent.instance.notify(TMainEvent.AV_Remove, [info, reason], true, true);
          // if (info.userId === TSession.instance.getUserId()) {
          //   // this._processMyDownStage(info, reason);
          //   console.log('被下台了');
          // } else {
          //   TEvent.instance.notify(TMainEvent.AV_Remove, [info, reason], true, true);
          // }
        }
      } else {
        if (info.stage) {
          this._userAvMap.set(info.userId, info);
          TEvent.instance.notify(TMainEvent.Member_Stage_Up, info);
          if (info.userId === TSession.instance.getUserId()) {
            this._processMyUpStage(info, reason);
          } else {
            TEvent.instance.notify(TMainEvent.AV_Add, [info, reason], true, true);
          }
          TState.instance.setState(TMainState.Wait_Stage_UserId, info.userId, this.constructor.name);
        }
      }
    });
    // 更新连麦状态
    if (this.isLiveClass() || this.isMobile()) {
      const stageMembers = permissionList.filter(item => item.stage === true && item.userId !== teacherId);
      TState.instance.setState(TMainState.Stage_Count, stageMembers.length, this.constructor.name);
    }

    if (currentUserInfo) {
      this._updateMyPermission(
        currentUserInfo.stage,
        currentUserInfo.board,
        currentUserInfo.screen,
        reason,
        currentUserInfo.operatorId,
      );
    } else {
      // 巡课老师不再支持白板能力
      this._updateMyPermission(false, false, 0, reason);
    }

    /**
     * @todo [异常路径][1],18:09 ,2Z7sphCVyenhhW59ilzsjgY6edz用户连续调用了两次revoke_camera
     * 这里过滤后只有自己的id，自己显然和expireUsers没关系
     * userArr里保存的是当前权限列表里的所有用户id,日志上看，当前学生没有出现在permission_list里
     */
    const expireUsers = [...this._userAvMap.keys()].filter(id => !userArr.includes(id));

    expireUsers.forEach((id) => {
      const oldInfo = this._userAvMap.get(id);
      if (id === TSession.instance.getUserId()) {
        // 自己能收到自己的离线消息，一般是心跳断开导致的
        if (reason === TPermissionUpdateReason.Offline) {
          TEvent.instance.notify(TMainEvent.Self_Offline, oldInfo);
        } else {
          oldInfo.camera && this.reportEvent('revoke_camera', oldInfo);
          oldInfo.mic && this.reportEvent('revoke_mic', oldInfo);
          this._processMyDownStage(oldInfo, reason);
        }
      } else {
        TEvent.instance.notify(TMainEvent.Member_Stage_Down, oldInfo);
        TEvent.instance.notify(TMainEvent.AV_Remove, [oldInfo, reason], true, true);
      }
      this._userAvMap.delete(id);
    });
    this._permissionList = permissionList;

    // 上抛权限变更
    TEvent.instance.notify(TMainEvent.Permission_Update, [permissionList, reason], true, true);
  }

  private _updateStreams(streams: TStreamInfo[], sequence: number) {
    if (!streams || (!this.isLiveClass() && !this.isUnitedClass())) {    // 非公开课忽略流事件
      return;
    }
    if (sequence <= this._streamListSequence) {
      this._warn('_updateStreams', `ignore list ${sequence}/${this._streamListSequence}`);
      return;
    }
    // 过滤自己的快直播流
    streams = streams.filter(stream => stream.userId !== this.getUserId());
    const classInfo = this.getClassInfo();    // 补充课堂快直播流
    const classStream = new TStreamInfo();
    classStream.userId = 'mix';
    classStream.url = classInfo.liveUrl;
    if (location.protocol === 'https:') {
      classStream.url = classInfo.liveUrl.replace('http:', 'https:');
    }
    classStream.type = TStreamType.Main;
    streams.push(classStream);
    this._streamListSequence = sequence;
    this._streamList = streams;
    TEvent.instance.notify(TMainEvent.Stream_Update, [streams], true, true);
  }

  /**
   * 移除IFrame
   * @private
   */
  private _removeIFrame() {
    const parentDom = this._getRootDom();
    const iframe = document.getElementById('custom-iframe') as HTMLIFrameElement;
    if (iframe) {
      parentDom.removeChild(iframe);
    }
  }

  /**
   * 获取父节点
   * @private
   */
  private _getRootDom(): HTMLElement {
    if (!this._rootDom) {
      this._rootDom = document.getElementById('app');
    }
    return this._rootDom;
  }

  /**
   * 加载IFrame
   * @param url
   * @private
   */
  private _loadIFrame(url: string) {
    const parentDom = this._getRootDom();
    let iframe = document.getElementById('custom-iframe') as HTMLIFrameElement;
    if (iframe) {
      parentDom.removeChild(iframe);
    }
    iframe = document.createElement('iframe');
    iframe.classList.add('custom-iframe');
    iframe.id = 'custom-iframe';
    iframe.src = url;
    iframe.setAttribute('style', 'position: absolute;'
      + 'top: 0px;'
      + 'left: 0px;'
      + 'width: 100%;'
      + 'height: 100%;'
      + 'border: none;'
      + 'z-index: 999998;');
    parentDom.appendChild(iframe);
    this._info('_loadIFrame', `load iframe ${url} completed`);
    (window as any).closeCustomIframe = () => {
      this._removeIFrame();
    };
    (window as any).onWebViewMsg = (event: string, message: any) => {
      TEvent.instance.notify(event, message);
    };
    return true;
  }

  /**
   * 存储基本信息
   * @private
   */
  private _saveSessionInfo() {
    const schoolInfo = TSession.instance.getSchoolInfo();
    localStorage.setItem('schoolid', schoolInfo.schoolId.toString());
    localStorage.setItem('userid', TSession.instance.getUserId());
    localStorage.setItem('token', TSession.instance.getToken());
    localStorage.setItem(TSession.instance.getUserId(), TSession.instance.getToken());
    // localStorage.setItem('sessionid', TSession.instance.getSessionID());
    localStorage.setItem('globalrandom', TSession.instance.getGlobalRandom());
    localStorage.setItem('schoolinfo', JSON.stringify(schoolInfo));
    localStorage.setItem('classInfo', JSON.stringify(this.getClassInfo()));
  }

  /**
   * 加载基本信息
   * @private
   */
  private _loadSessionInfo() {
    try {
      // TSession.instance.setParams('sessionid', localStorage.getItem('sessionid'));
      TSession.instance.setParams('globalrandom', localStorage.getItem('globalrandom'));
      TSession.instance.setSchoolInfo(JSON.parse(localStorage.getItem('schoolinfo')));
      TSession.instance.setClassInfo(JSON.parse(localStorage.getItem('classInfo')));
    } catch (err) {
      this._warn('_loadSessionInfo', `load session info fail: ${err}`);
    }
  }

  /**
   * 添加课堂可见性监听
   */
  private _addClassVisibleChangeListener() {
    // 监听页面可见性
    this._classVisibleChangeEventName = 'visibilitychange';
    if ((document as any).hidden !== 'undefined') {
      // hidden = 'hidden';
      this._classVisibleChangeEventName = 'visibilitychange';
    } else if ((document as any).mozHidden !== 'undefined') {
      // hidden = 'mozHidden';
      this._classVisibleChangeEventName = 'mozvisibilitychange';
    } else if ((document as any).msHidden !== 'undefined') {
      // hidden = 'msHidden';
      this._classVisibleChangeEventName = 'msvisibilitychange';
    } else if ((document as any).webkitHidden !== 'undefined') {
      // hidden = 'webkitHidden';
      this._classVisibleChangeEventName = 'webkitvisibilitychange';
    }
    document.addEventListener(this._classVisibleChangeEventName, this._onClassVisibleChange.bind(this));
  }

  /**
   * 移除课堂可见性监听
   */
  private _removeClassVisibleChangeListener() {
    document.addEventListener(this._classVisibleChangeEventName, this._onClassVisibleChange.bind(this));
  }

  /**
   * 页面可见性变化通知
   */
  private _onClassVisibleChange() {
    if (document.visibilityState === 'hidden') {
      TState.getInstance().setState(TMainState.Class_Visible, false, this.constructor.name);
    } else {
      TState.getInstance().setState(TMainState.Class_Visible, true, this.constructor.name);
    }
  }

  /**
   * 更新课堂信息
   */
  private _updateClassInfo(info: any) {
    if (info) {
      this._info('_updateClassInfo', JSON.stringify(info));
      if (info.data && JSON.stringify(info.data) !== '{}') {
        const classInfo = TSession.instance.getClassInfo();
        const joinedClass = TState.instance.getState(TMainState.Joined_Class);
        const classStatus = TState.instance.getState(TMainState.Class_Status);
        const started = TClassStatus.Already_Start;
        const userId = TSession.instance.getUserId();

        // 如果课中出现了老师变更，触发页面刷新
        if (info.data.teacher_id
          && info.data.teacher_id !== classInfo.teacherId
          && joinedClass
          && classStatus === started
        ) {
          this._info('teacher_changed', JSON.stringify({
            newTeacher: info.data.teacher_id,
            oldTeacher: classInfo.teacherId,
          }));

          if (userId !== classInfo.teacherId) {
            this.showMessageBox(
              i18next.t('提示'),
              `${TSession.instance.getRoleInfo().roleInfo.teacher}发生了变更，为了保证最佳体验，请重新进入`,
              [i18next.t('确定')],
              () => {
                const reload = window.customReload || window.location.reload;
                reload();
              },
            );
          } else {
            this.showMessageBox(
              i18next.t('提示'),
              i18next.t(
                '抱歉，您已被另一位{{arg_0}}移出。如有疑问，请联系管理员或相关{{arg_0}}。',
                {
                  arg_0: TSession.instance.getRoleInfo().roleInfo.teacher,
                  arg_1: TSession.instance.getRoleInfo().roomInfo.name,
                },
              ),
              [i18next.t('确定')],
              () => this.unInitialize(),
            );
          }
        }

        classInfo.mergeInfo(info.data);
        this._saveSessionInfo();
        // 更新布局信息
        this._updateClassLayout();
        TEvent.instance.notify(TMainEvent.Modify_Class, info ? info : classInfo);
        TState.instance.setState(
          TMainState.Class_Status,
          classInfo.status,
          this.constructor.name,
        );
        // 更新全员静音状态
        TState.instance.setState(TMainState.Mute_All, classInfo.muteAll === 1, this.constructor.name);
        // 更新全员视频状态
        TState.instance.setState(TMainState.Mute_Video_All, classInfo.muteVideoAll === 1, this.constructor.name);
        // 处理全员禁言
        TState.instance.setState(TMainState.Silence_All, classInfo.silenceAll === 1, this.constructor.name);
        TState.instance.setState(TMainState.Silence_Mode, classInfo.silenceMode, this.constructor.name);
        // 处理允许申请上麦
        TState.instance.setState(TMainState.Enable_Stage, classInfo.enableStage === 1, this.constructor.name);
        TBusinessCommand.instance.onEnableStageChanged(classInfo.enableStage === 1);
        // console.log(`===>>> : updateClassInfo : Enable_Stage : ${classInfo.enableStage}`);
      }
    } else {
      this.updateClassInfo();
    }
  }

  // 从后台主动拉取权限列表
  private _getPermissionList(reason = '') {
    this._warn('_getPermissionList', `reason: ${reason}`);
    TBusinessMember.instance.getPermissionList(
      TSession.instance.getClassId(),
      TSession.instance.getToken(),
    )
      .then((result: TPermissionListResult) => {
        const permissionList: TPermissionInfo[] = [];
        result.members.forEach((member: TMemberInfo) => {
          permissionList.push(member);
        });
        this._updatePermissionList(
          permissionList,
          TPermissionUpdateReason.Unknown,
          result.permissionListSeq,
          { dataFrom: `member/permissionList when ${reason}` },
        );
      })
      .catch((error) => {
        this._warn('_getPermissionList', `failed: ${error.toString()}`);
      });
  }

  // 是否使用高清音质
  private _isHighAudioQuality() {
    const classInfo = this.getClassInfo();
    return classInfo && classInfo.audioQuality === 1;
  }

  // 判断DOMRect是否有效
  private _isDomRectValid(rect: DOMRect) {
    return rect && rect.width > 0 && rect.height > 0;
  }

  // 比较DOMRect是否相同(相同返回true，否则为false)
  private _compareDomRect(rectSrc: DOMRect, rectDst: DOMRect) {
    return rectSrc
      && rectDst
      && rectSrc.top === rectDst.top
      && rectSrc.left === rectDst.left
      && rectSrc.width === rectDst.width
      && rectSrc.height === rectDst.height;
  }

  // 重新加载课中页
  private _reloadClass() {
    let requestPath = /^(127\.0\.0\.1|localhost)/.test(location.host) ? `${location.host}/class.html` : `${location.host}/${SDK_MAIN_VERSION}/class.html`;
    if (/\/class\.html$/.test(location.pathname)) {
      // 分支体验时路径不是 SDK_MAIN_VERSION，如果是class页，直接用 location.pathname
      requestPath = `${location.host}${location.pathname}`; // location.pathname 有 / 开头，注意不要重复了
    }
    // 拼接URL重新加载课堂(不能直接reload，否则在移动端会进入预加载的loading页)
    const urlParams = new URLSearchParams(location.search);
    urlParams.set('platform', TSession.instance.getPlatform());
    urlParams.set('device', TSession.instance.getParams('device', ''));
    urlParams.set('schoolid', TSession.instance.getParams('schoolid', ''));
    urlParams.set('classid', TSession.instance.getParams('classid', ''));
    urlParams.set('lng', TSession.instance.getLanguage());
    urlParams.set('userid', TSession.instance.getUserId());
    urlParams.set('token', TSession.instance.getParams('token', ''));
    urlParams.set('cid', TSession.instance.getCid());
    urlParams.set('uid', TSession.instance.getUid());
    urlParams.set('env', TSession.instance.getEnv());
    urlParams.set('isreload', '1'); // 标记是reload，initParams时要区分处理
    // ios 重新加载页面的时候,移动端不会调manualJoinClass方法. ios 有预加载逻辑完成后会去掉delay参数并改成完整url,
    // 若未预加载完成,短url带delay=true,会一直等native回调.导致页面停在loading状态无法正常刷新. 所以强制设为false.
    if (TSession.instance.isIOSNative()) {
      // urlParams.set('delay', 'false');
      urlParams.delete('delay');
    }
    const url = `${location.protocol.replace(/(lcich|tiw-cache)/, 'http')}//${requestPath}?${urlParams.toString()}`;
    this._info('_reloadClass', url);
    window.removeunloadListener();
    window.location.replace(url);
  }

  // 切换角色
  private _switchRole() {
    const infoAction = 'trtc._switchRole';
    if (this._isAnchor) {
      this._info(infoAction, 'already isAnchor');
      return Promise.resolve();
    }
    if (!this._switchRolePromise) {
      this._info(infoAction, 'do switchRole');

      // 注意 promise 结束要设为 null，否则出错后就一直出错
      this._switchRolePromise = TTrtc.instance.switchRole(true)
        .then(() => {
          this._isAnchor = true;
          this._switchRolePromise = null;
          this._info(infoAction, 'success');
        })
        .catch((err) => {
          this._switchRolePromise = null;
          this._error(infoAction, `failed: ${err.toString()}`);
          throw err;
        });
    } else {
      this._info(infoAction, 'use existed promise');
    }
    return this._switchRolePromise;
  }
  // 处理自己上麦事件
  private _processMyUpStage(info: TPermissionInfo, reason: TPermissionUpdateReason) {
    // 自己上麦时，主动报一次心跳刷新间隔
    this._info('_processMyUpStage', `enter with ${JSON.stringify(info)}`);
    info.camera && this.reportEvent('grant_camera', info);
    info.mic && this.reportEvent('grant_mic', info);
    this._updateClassHeartbeat(500);
    if (this.isLiveClass()) {   // 保留原公开课逻辑
      TEvent.instance.notify(TMainEvent.AV_Add, [info, reason], true, true);
      // 直播课通知已上台
      TBusinessCommand.instance.notifyStageStatus(false);
      TState.instance.setState(TMainState.Invite_Stage_Status, false, this.constructor.name);
    } else if (this.isUnitedClass()) {
      if (this._joinType === TJoinType.RTC || this.getState(TMainState.Joined_TRTC, false)) {
        TEvent.instance.notify(TMainEvent.AV_Add, [info, reason], true, true);
      } else {    // 未加入TRTC房间则需要先加入
        this.enterRtcRoom(true)
          .then(() => {
            this.setState(TMainState.RTC_Mode, true);
            TEvent.instance.notify(TMainEvent.AV_Add, [this.getPermission(this.getUserId()), reason], true, true);
          });
      }
    }
  }
  // 处理自己下麦
  private _processMyDownStage(info: TPermissionInfo, reason: TPermissionUpdateReason) {
    this._info('_processMyDownStage', `enter with ${JSON.stringify(info)}`);

    info.camera && this.reportEvent('revoke_camera', info);
    info.mic && this.reportEvent('revoke_mic', info);
    TEvent.instance.notify(TMainEvent.AV_Remove, [info, reason], true, true);
    if (this.isLiveClass() && !this.isTeacher()) {   // 保留原公开课逻辑
      if (this._isJoinedClass) {  // 本地下台退出TRTC房间
        this.hangupToStage({});
      }
      // 直播课通知已下台
      TBusinessCommand.instance.notifyStageStatus(true);
    } else if (this.isUnitedClass()) {
      if (this._joinType === TJoinType.Live) {
        // 如果是直播方式进，下台就退出rtc房间
        this.quitRtcRoom()
          .then(() => {
            this.setState(TMainState.RTC_Mode, false);
          });
      }
    }
  }
}
